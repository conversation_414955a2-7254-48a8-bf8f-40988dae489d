@echo off
chcp 65001 > nul
echo ========================================
echo    التحزيم النهائي - نظام إدارة المدرسة
echo ========================================
echo.

echo [1/5] التحقق من المتطلبات...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت
    pause
    exit /b 1
)
echo ✅ Python موجود

echo.
echo [2/5] تثبيت المكتبات المطلوبة...
pip install --upgrade pyinstaller setuptools
pip install jaraco.text more-itertools importlib-metadata zipp
echo ✅ تم تثبيت المكتبات

echo.
echo [3/5] تنظيف الملفات القديمة...
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"
echo ✅ تم التنظيف

echo.
echo [4/5] بدء التحزيم النهائي...
echo جاري التحزيم مع الإصلاحات...
pyinstaller simple_build.spec --clean --noconfirm

if errorlevel 1 (
    echo ❌ فشل التحزيم
    pause
    exit /b 1
)

echo.
echo [5/5] التحقق من النتائج...
if exist "dist\school_system\school_system.exe" (
    echo ✅ تم التحزيم بنجاح!
    echo.
    echo 📁 المجلد: dist\school_system\
    echo 🚀 الملف التنفيذي: school_system.exe
    echo 💾 قاعدة البيانات: ستُنشأ في نفس مجلد البرنامج
    echo 🖥️ واجهة المستخدم: بدون موجه أوامر
    echo.
    
    echo إنشاء ملف تشغيل سريع...
    echo @echo off > "تشغيل_البرنامج.bat"
    echo cd "dist\school_system" >> "تشغيل_البرنامج.bat"
    echo start school_system.exe >> "تشغيل_البرنامج.bat"
    echo ✅ تم إنشاء ملف التشغيل السريع
    
    echo.
    echo هل تريد فتح مجلد البرنامج؟ (y/n)
    set /p choice=
    if /i "%choice%"=="y" (
        explorer "dist\school_system"
    )
) else (
    echo ❌ لم يتم العثور على الملف التنفيذي
)

echo.
echo ========================================
echo           انتهت العملية
echo ========================================
pause
