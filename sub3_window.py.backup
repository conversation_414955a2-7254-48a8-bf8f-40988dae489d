#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""نافذة إعدادات الطابعة"""

import sys
import os
import sqlite3
import win32print
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

class PrinterSettingsWindow(QMainWindow):
    """نافذة إعدادات الطابعة"""
    
    def __init__(self, db_path="data.db"):
        super().__init__()
        self.db_path = db_path
        self.init_database()
        self.init_ui()
        self.load_printers()
        self.load_current_settings()
    
    def init_database(self):
        """إنشاء جدول إعدادات الطابعة"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS إعدادات_الطابعة (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    الطابعة_الحرارية TEXT,
                    الطابعة_العادية TEXT,
                    تاريخ_التحديث TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # إدراج سجل افتراضي إذا لم يكن موجود
            cursor.execute("SELECT COUNT(*) FROM إعدادات_الطابعة")
            if cursor.fetchone()[0] == 0:
                cursor.execute("""
                    INSERT INTO إعدادات_الطابعة (الطابعة_الحرارية, الطابعة_العادية)
                    VALUES ('', '')
                """)
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"خطأ في إنشاء قاعدة البيانات: {str(e)}")
    
    def center_window(self):
        """توسيط النافذة في وسط الشاشة"""
        try:
            screen = QApplication.desktop().screenGeometry()
            window_size = self.geometry()
            x = (screen.width() - window_size.width()) // 2
            y = (screen.height() - window_size.height()) // 2
            self.move(x, y)
        except Exception as e:
            print(f"خطأ في توسيط النافذة: {str(e)}")
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("🖨️ إعدادات الطابعة")
        self.setFixedSize(800, 600)
        
        # توسيط النافذة
        self.center_window()
        
        # الويدجيت الرئيسي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        
        # العنوان الرئيسي
        title_label = QLabel("🖨️ إعدادات الطابعة")
        title_label.setFont(QFont("Calibri", 15, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                background-color: #2c3e50;
                color: white;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # مجموعة الطابعة الحرارية
        self.create_thermal_printer_group(main_layout)
        
        # مجموعة الطابعة الافتراضية
        self.create_default_printer_group(main_layout)
        
        # مجموعة الأزرار
        self.create_buttons_group(main_layout)
        
        # شريط الحالة
        self.statusBar().setFont(QFont("Calibri", 13, QFont.Bold))
        self.statusBar().showMessage("جاهز - إعدادات الطابعة")
    
    def create_thermal_printer_group(self, main_layout):
        """إنشاء مجموعة الطابعة الحرارية"""
        thermal_group = QGroupBox("🔥 الطابعة الحرارية")
        thermal_group.setFont(QFont("Calibri", 15, QFont.Bold))
        thermal_layout = QVBoxLayout(thermal_group)
        
        # تسمية
        thermal_label = QLabel("اختر الطابعة الحرارية:")
        thermal_label.setFont(QFont("Calibri", 13, QFont.Bold))
        thermal_layout.addWidget(thermal_label)
        
        # قائمة الطابعات الحرارية
        self.thermal_combo = QComboBox()
        self.thermal_combo.setFont(QFont("Calibri", 13, QFont.Bold))
        self.thermal_combo.setStyleSheet("""
            QComboBox {
                padding: 8px;
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                background-color: white;
            }
            QComboBox:focus {
                border: 2px solid #3498db;
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #2c3e50;
                margin-right: 5px;
            }
        """)
        self.thermal_combo.currentTextChanged.connect(self.save_thermal_printer)
        thermal_layout.addWidget(self.thermal_combo)
        
        main_layout.addWidget(thermal_group)
    
    def create_default_printer_group(self, main_layout):
        """إنشاء مجموعة الطابعة الافتراضية"""
        default_group = QGroupBox("🖨️ الطابعة الافتراضية")
        default_group.setFont(QFont("Calibri", 15, QFont.Bold))
        default_layout = QVBoxLayout(default_group)
        
        # تسمية
        default_label = QLabel("اختر الطابعة الافتراضية:")
        default_label.setFont(QFont("Calibri", 13, QFont.Bold))
        default_layout.addWidget(default_label)
        
        # قائمة الطابعات الافتراضية
        self.default_combo = QComboBox()
        self.default_combo.setFont(QFont("Calibri", 13, QFont.Bold))
        self.default_combo.setStyleSheet("""
            QComboBox {
                padding: 8px;
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                background-color: white;
            }
            QComboBox:focus {
                border: 2px solid #3498db;
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #2c3e50;
                margin-right: 5px;
            }
        """)
        self.default_combo.currentTextChanged.connect(self.save_default_printer)
        default_layout.addWidget(self.default_combo)
        
        main_layout.addWidget(default_group)
    
    def create_buttons_group(self, main_layout):
        """إنشاء مجموعة الأزرار"""
        buttons_group = QGroupBox("⚡ العمليات")
        buttons_group.setFont(QFont("Calibri", 15, QFont.Bold))
        buttons_layout = QHBoxLayout(buttons_group)
        
        # زر تحديث قائمة الطابعات
        refresh_btn = QPushButton("🔄 تحديث قائمة الطابعات")
        refresh_btn.setFont(QFont("Calibri", 13, QFont.Bold))
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        refresh_btn.clicked.connect(self.load_printers)
        buttons_layout.addWidget(refresh_btn)
        
        # زر اختبار الطباعة
        test_btn = QPushButton("🧪 اختبار الطباعة")
        test_btn.setFont(QFont("Calibri", 13, QFont.Bold))
        test_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        test_btn.clicked.connect(self.test_print)
        buttons_layout.addWidget(test_btn)
        
        # زر إغلاق
        close_btn = QPushButton("❌ إغلاق")
        close_btn.setFont(QFont("Calibri", 13, QFont.Bold))
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        close_btn.clicked.connect(self.close)
        buttons_layout.addWidget(close_btn)
        
        main_layout.addWidget(buttons_group)
    
    def get_installed_printers(self):
        """الحصول على قائمة الطابعات المثبتة في الجهاز"""
        try:
            printers = []
            # الحصول على جميع الطابعات المثبتة
            printer_info = win32print.EnumPrinters(win32print.PRINTER_ENUM_LOCAL | win32print.PRINTER_ENUM_CONNECTIONS)
            
            for printer in printer_info:
                printers.append(printer[2])  # اسم الطابعة
            
            return printers
        except Exception as e:
            print(f"خطأ في الحصول على قائمة الطابعات: {str(e)}")
            return []
    
    def load_printers(self):
        """تحميل قائمة الطابعات في القوائم المنسدلة"""
        try:
            printers = self.get_installed_printers()
            
            # تحديث قائمة الطابعة الحرارية
            current_thermal = self.thermal_combo.currentText()
            self.thermal_combo.clear()
            self.thermal_combo.addItem("اختر الطابعة الحرارية")
            self.thermal_combo.addItems(printers)
            
            # استعادة الاختيار السابق
            if current_thermal and current_thermal in printers:
                self.thermal_combo.setCurrentText(current_thermal)
            
            # تحديث قائمة الطابعة الافتراضية
            current_default = self.default_combo.currentText()
            self.default_combo.clear()
            self.default_combo.addItem("اختر الطابعة الافتراضية")
            self.default_combo.addItems(printers)
            
            # استعادة الاختيار السابق
            if current_default and current_default in printers:
                self.default_combo.setCurrentText(current_default)
            
            self.statusBar().showMessage(f"تم تحديث قائمة الطابعات - تم العثور على {len(printers)} طابعة")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل قائمة الطابعات:\n{str(e)}")
            self.statusBar().showMessage("خطأ في تحميل الطابعات")

    def load_current_settings(self):
        """تحميل الإعدادات الحالية من قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("SELECT الطابعة_الحرارية, الطابعة_العادية FROM إعدادات_الطابعة LIMIT 1")
            result = cursor.fetchone()

            if result:
                thermal_printer, default_printer = result

                # تعيين الطابعة الحرارية
                if thermal_printer:
                    index = self.thermal_combo.findText(thermal_printer)
                    if index >= 0:
                        self.thermal_combo.setCurrentIndex(index)

                # تعيين الطابعة الافتراضية
                if default_printer:
                    index = self.default_combo.findText(default_printer)
                    if index >= 0:
                        self.default_combo.setCurrentIndex(index)

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل الإعدادات: {str(e)}")

    def save_thermal_printer(self):
        """حفظ الطابعة الحرارية المحددة"""
        try:
            selected_printer = self.thermal_combo.currentText()
            if selected_printer == "اختر الطابعة الحرارية":
                return

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # تحديث أو إدراج الإعدادات
            cursor.execute("""
                UPDATE إعدادات_الطابعة
                SET الطابعة_الحرارية = ?, تاريخ_التحديث = CURRENT_TIMESTAMP
                WHERE id = 1
            """, (selected_printer,))

            if cursor.rowcount == 0:
                cursor.execute("""
                    INSERT INTO إعدادات_الطابعة (الطابعة_الحرارية)
                    VALUES (?)
                """, (selected_printer,))

            conn.commit()
            conn.close()

            self.statusBar().showMessage(f"تم حفظ الطابعة الحرارية: {selected_printer}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ الطابعة الحرارية:\n{str(e)}")

    def save_default_printer(self):
        """حفظ الطابعة الافتراضية المحددة وتعيينها كطابعة افتراضية للنظام"""
        try:
            selected_printer = self.default_combo.currentText()
            if selected_printer == "اختر الطابعة الافتراضية":
                return

            # تعيين الطابعة كطابعة افتراضية للنظام
            try:
                win32print.SetDefaultPrinter(selected_printer)
            except Exception as e:
                QMessageBox.warning(self, "تحذير", f"تم حفظ الطابعة في قاعدة البيانات ولكن فشل في تعيينها كطابعة افتراضية للنظام:\n{str(e)}")

            # حفظ في قاعدة البيانات
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                UPDATE إعدادات_الطابعة
                SET الطابعة_العادية = ?, تاريخ_التحديث = CURRENT_TIMESTAMP
                WHERE id = 1
            """, (selected_printer,))

            if cursor.rowcount == 0:
                cursor.execute("""
                    INSERT INTO إعدادات_الطابعة (الطابعة_العادية)
                    VALUES (?)
                """, (selected_printer,))

            conn.commit()
            conn.close()

            self.statusBar().showMessage(f"تم حفظ وتعيين الطابعة الافتراضية: {selected_printer}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ الطابعة الافتراضية:\n{str(e)}")

    def test_print(self):
        """اختبار الطباعة على الطابعة المحددة"""
        try:
            thermal_printer = self.thermal_combo.currentText()
            default_printer = self.default_combo.currentText()

            if thermal_printer == "اختر الطابعة الحرارية" and default_printer == "اختر الطابعة الافتراضية":
                QMessageBox.warning(self, "تحذير", "يرجى اختيار طابعة واحدة على الأقل للاختبار")
                return

            # إنشاء نص الاختبار
            test_text = f"""
=================================
        اختبار الطباعة
=================================

التاريخ: {QDateTime.currentDateTime().toString('yyyy-MM-dd hh:mm:ss')}

الطابعة الحرارية: {thermal_printer if thermal_printer != "اختر الطابعة الحرارية" else "غير محددة"}
الطابعة الافتراضية: {default_printer if default_printer != "اختر الطابعة الافتراضية" else "غير محددة"}

هذا اختبار للطباعة من نظام إدارة الطلاب
تم الاختبار بنجاح!

=================================
"""

            # طباعة على الطابعة الحرارية إذا كانت محددة
            if thermal_printer != "اختر الطابعة الحرارية":
                self.print_to_printer(thermal_printer, test_text, "اختبار الطابعة الحرارية")

            # طباعة على الطابعة الافتراضية إذا كانت محددة
            if default_printer != "اختر الطابعة الافتراضية":
                self.print_to_printer(default_printer, test_text, "اختبار الطابعة الافتراضية")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في اختبار الطباعة:\n{str(e)}")

    def print_to_printer(self, printer_name, text, title):
        """طباعة نص على طابعة محددة"""
        try:
            from PyQt5.QtPrintSupport import QPrinter, QPrintDialog
            from PyQt5.QtGui import QPainter, QFont

            # إعداد الطابعة
            printer = QPrinter()
            printer.setPrinterName(printer_name)
            printer.setPageSize(QPrinter.A4)

            # إعداد الرسام
            painter = QPainter()
            if painter.begin(printer):
                # إعداد الخط
                font = QFont("Calibri", 10)
                painter.setFont(font)

                # رسم النص
                painter.drawText(100, 100, text)
                painter.end()

                QMessageBox.information(self, "نجح الاختبار", f"تم إرسال {title} إلى الطابعة: {printer_name}")
            else:
                QMessageBox.warning(self, "فشل الاختبار", f"فشل في بدء الطباعة على: {printer_name}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ في الطباعة", f"فشل في الطباعة على {printer_name}:\n{str(e)}")

def main():
    """تشغيل النافذة"""
    app = QApplication(sys.argv)
    window = PrinterSettingsWindow()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
