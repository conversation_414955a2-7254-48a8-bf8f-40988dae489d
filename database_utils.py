#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
وحدة إدارة قاعدة البيانات
تحتوي على دوال مساعدة لإدارة مسار قاعدة البيانات في البيئات المختلفة
"""

import os
import sys
import sqlite3
import shutil
from pathlib import Path
from datetime import datetime

def get_application_path():
    """
    الحصول على مسار التطبيق الصحيح
    يعمل في البيئة العادية وبعد التحزيم بـ PyInstaller
    """
    if getattr(sys, 'frozen', False):
        # التطبيق محزم بـ PyInstaller
        application_path = os.path.dirname(sys.executable)
    else:
        # التطبيق يعمل من الكود المصدري
        application_path = os.path.dirname(os.path.abspath(__file__))
    
    return application_path

def get_database_path():
    """
    الحصول على مسار قاعدة البيانات الصحيح
    يضمن أن قاعدة البيانات تكون في مجلد التطبيق وليس في _internal
    """
    app_path = get_application_path()
    db_path = os.path.join(app_path, "school_database.db")
    return db_path

def ensure_database_exists():
    """
    التأكد من وجود قاعدة البيانات وإنشاؤها إذا لم تكن موجودة
    """
    db_path = get_database_path()
    
    # إذا كانت قاعدة البيانات غير موجودة، أنشئها
    if not os.path.exists(db_path):
        print(f"🔍 [INFO] قاعدة البيانات غير موجودة في: {db_path}")
        print(f"🔧 [INFO] إنشاء قاعدة بيانات جديدة...")
        create_database_structure(db_path)
    
    return db_path

def create_database_structure(db_path):
    """
    إنشاء هيكل قاعدة البيانات الأساسي
    """
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # إنشاء جدول بيانات المؤسسة
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS بيانات_المؤسسة (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                المؤسسة TEXT,
                رقم_الهاتف TEXT,
                المدينة TEXT,
                العنوان TEXT,
                البريد_الإلكتروني TEXT,
                الموقع_الإلكتروني TEXT,
                تاريخ_الإنشاء TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # إدراج بيانات افتراضية للمؤسسة
        cursor.execute("""
            INSERT OR IGNORE INTO بيانات_المؤسسة (id, المؤسسة, رقم_الهاتف, المدينة)
            VALUES (1, 'المؤسسة التعليمية', '', '')
        """)
        
        # إنشاء جدول البيانات الرئيسي
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS جدول_البيانات (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                اسم_التلميذ TEXT NOT NULL,
                رمز_التلميذ TEXT UNIQUE NOT NULL,
                القسم TEXT NOT NULL,
                رقم_الهاتف_الأول TEXT,
                رقم_الهاتف_الثاني TEXT,
                العنوان TEXT,
                ملاحظات TEXT,
                تاريخ_التسجيل TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # إنشاء جدول الواجبات الشهرية
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS monthly_duties (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                student_id INTEGER NOT NULL,
                month TEXT NOT NULL,
                year INTEGER NOT NULL,
                amount_required REAL DEFAULT 0,
                amount_paid REAL DEFAULT 0,
                amount_remaining REAL DEFAULT 0,
                payment_date TEXT,
                payment_status TEXT DEFAULT 'غير مدفوع',
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (student_id) REFERENCES جدول_البيانات (id)
            )
        """)
        
        # إنشاء جدول واجبات التسجيل
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS registration_fees (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                student_id INTEGER NOT NULL,
                payment_type TEXT NOT NULL,
                amount_paid REAL NOT NULL,
                payment_method TEXT DEFAULT 'نقداً',
                payment_date TEXT,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (student_id) REFERENCES جدول_البيانات (id)
            )
        """)
        
        # إنشاء جدول الأقسام
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS sections (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                section_name TEXT UNIQUE NOT NULL,
                monthly_duty REAL DEFAULT 0,
                registration_fee REAL DEFAULT 0,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # إنشاء جدول المعلمين
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS teachers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                teacher_name TEXT NOT NULL,
                phone TEXT,
                email TEXT,
                address TEXT,
                specialization TEXT,
                hire_date TEXT,
                salary REAL DEFAULT 0,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # إنشاء جدول الحضور
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS attendance (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                student_id INTEGER NOT NULL,
                attendance_date TEXT NOT NULL,
                status TEXT DEFAULT 'حاضر',
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (student_id) REFERENCES جدول_البيانات (id)
            )
        """)
        
        conn.commit()
        conn.close()
        
        print(f"✅ [SUCCESS] تم إنشاء قاعدة البيانات بنجاح في: {db_path}")
        
    except Exception as e:
        print(f"❌ [ERROR] خطأ في إنشاء قاعدة البيانات: {str(e)}")
        raise

def migrate_database_if_needed():
    """
    نقل قاعدة البيانات من _internal إلى مجلد التطبيق إذا لزم الأمر
    """
    try:
        app_path = get_application_path()
        target_db_path = get_database_path()
        
        # البحث عن قاعدة البيانات في _internal
        internal_path = os.path.join(app_path, "_internal", "school_database.db")
        
        # إذا كانت قاعدة البيانات موجودة في _internal وغير موجودة في المجلد الرئيسي
        if os.path.exists(internal_path) and not os.path.exists(target_db_path):
            print(f"🔄 [INFO] نقل قاعدة البيانات من _internal إلى مجلد التطبيق...")
            shutil.copy2(internal_path, target_db_path)
            print(f"✅ [SUCCESS] تم نقل قاعدة البيانات بنجاح إلى: {target_db_path}")
        
        # البحث في مجلدات أخرى محتملة
        possible_paths = [
            os.path.join(app_path, "data", "school_database.db"),
            os.path.join(app_path, "database", "school_database.db"),
            os.path.join(os.path.dirname(app_path), "school_database.db")
        ]
        
        for possible_path in possible_paths:
            if os.path.exists(possible_path) and not os.path.exists(target_db_path):
                print(f"🔄 [INFO] نقل قاعدة البيانات من: {possible_path}")
                shutil.copy2(possible_path, target_db_path)
                print(f"✅ [SUCCESS] تم نقل قاعدة البيانات بنجاح")
                break
        
    except Exception as e:
        print(f"❌ [ERROR] خطأ في نقل قاعدة البيانات: {str(e)}")

def get_connection():
    """
    الحصول على اتصال بقاعدة البيانات
    يضمن أن قاعدة البيانات موجودة ويمكن الوصول إليها
    """
    try:
        # نقل قاعدة البيانات إذا لزم الأمر
        migrate_database_if_needed()
        
        # التأكد من وجود قاعدة البيانات
        db_path = ensure_database_exists()
        
        # إنشاء الاتصال
        conn = sqlite3.connect(db_path)
        
        # تفعيل دعم المفاتيح الخارجية
        conn.execute("PRAGMA foreign_keys = ON")
        
        return conn
        
    except Exception as e:
        print(f"❌ [ERROR] خطأ في الاتصال بقاعدة البيانات: {str(e)}")
        raise

def test_database_connection():
    """
    اختبار الاتصال بقاعدة البيانات
    """
    try:
        conn = get_connection()
        cursor = conn.cursor()
        
        # اختبار جدول بيانات المؤسسة
        cursor.execute("SELECT COUNT(*) FROM بيانات_المؤسسة")
        count = cursor.fetchone()[0]
        
        conn.close()
        
        print(f"✅ [SUCCESS] اختبار قاعدة البيانات نجح. عدد سجلات المؤسسة: {count}")
        return True
        
    except Exception as e:
        print(f"❌ [ERROR] فشل اختبار قاعدة البيانات: {str(e)}")
        return False

def backup_database(backup_path=None):
    """
    إنشاء نسخة احتياطية من قاعدة البيانات
    """
    try:
        source_db = get_database_path()
        
        if backup_path is None:
            app_path = get_application_path()
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = os.path.join(app_path, f"backup_school_database_{timestamp}.db")
        
        shutil.copy2(source_db, backup_path)
        print(f"✅ [SUCCESS] تم إنشاء نسخة احتياطية في: {backup_path}")
        return backup_path
        
    except Exception as e:
        print(f"❌ [ERROR] خطأ في إنشاء النسخة الاحتياطية: {str(e)}")
        return None

# اختبار الوحدة
if __name__ == "__main__":
    print("🧪 [TEST] اختبار وحدة إدارة قاعدة البيانات...")
    
    print(f"📁 مسار التطبيق: {get_application_path()}")
    print(f"🗄️ مسار قاعدة البيانات: {get_database_path()}")
    
    # اختبار الاتصال
    if test_database_connection():
        print("🎉 جميع الاختبارات نجحت!")
    else:
        print("❌ فشلت الاختبارات!")
