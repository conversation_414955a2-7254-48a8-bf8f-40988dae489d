#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# ========================================
# 🔧 إعدادات التقرير القابلة للتعديل اليدوي
# ========================================

# 📋 ترتيب الأعمدة في الجدول (غير الترتيب حسب الحاجة)
COLUMN_ORDER = [
    'week5',      # الأسبوع الخامس
    'week4',      # الأسبوع الرابع
    'week3',      # الأسبوع الثالث
    'week2',      # الأسبوع الثاني
    'week1',      # الأسبوع الأول
    'name',       # اسم التلميذ
    'id',         # الرمز
    'order'       # رقم الترتيب
]

# 📏 عرض الأعمدة بالمليمتر (غير القيم حسب الحاجة)
COLUMN_WIDTHS = {
    'week5': 24,      # الأسبوع الخامس (3 حصص × 8)
    'week4': 24,      # الأسبوع الرابع (3 حصص × 8)
    'week3': 24,      # الأسبوع الثالث (3 حصص × 8)
    'week2': 24,      # الأسبوع الثاني (3 حصص × 8)
    'week1': 24,      # الأسبوع الأول (3 حصص × 8)
    'name': 40,       # اسم التلميذ
    'id': 26,         # الرمز
    'order': 14,      # رقم الترتيب
    'session': 8      # كل حصة أسبوعية
}

# 📐 ارتفاع الصفوف بالمليمتر (غير القيم حسب الحاجة)
ROW_HEIGHTS = {
    'header_main': 14,      # رأس الجدول الرئيسي
    'header_sub': 7,        # رأس الجدول الفرعي (الحصص)
    'student_row': 4.5,      # صف التلميذ
    'info_row': 7          # صف معلومات القسم
}

# 📊 عرض أعمدة الجدول الأول (معلومات القسم) بالمليمتر
INFO_TABLE_WIDTHS = {
    'section': 48,         # عرض عمود القسم
    'subject': 48,         # عرض عمود المادة
    'teacher': 64,         # عرض عمود الأستاذ
    'count': 40           # عرض عمود عدد التلاميذ
}

# 🖼️ نمط الحدود (غير القيم حسب الحاجة)
BORDER_STYLE = {
    'width': 0.3,           # سمك الحدود
    'color_r': 0,           # اللون الأحمر (0-255)
    'color_g': 0,           # اللون الأخضر (0-255)
    'color_b': 0,           # اللون الأزرق (0-255)
    'style': 'solid'        # نمط الخط: solid, dashed, dotted
}

# 📄 إعدادات الصفحة (غير القيم حسب الحاجة)
PAGE_SETTINGS = {
    'orientation': 'P',     # P = عمودي، L = أفقي
    'margin_top': 5,        # الهامش العلوي
    'margin_bottom': 5,     # الهامش السفلي
    'margin_left': 5,       # الهامش الأيسر
    'margin_right': 5       # الهامش الأيمن
}

# 🔤 أسماء الأعمدة (غير النصوص حسب الحاجة)
COLUMN_NAMES = {
    'week5': 'الأسبوع 5\n--/--',
    'week4': 'الأسبوع 4\n--/--',
    'week3': 'الأسبوع 3\n--/--',
    'week2': 'الأسبوع 2\n--/--',
    'week1': 'الأسبوع 1\n--/--',
    'absence': 'عدد حصص الغياب',
    'name': 'اسم التلميذ',
    'id': 'الرمز',
    'order': 'الرقم\nالترتيبي'
}

# ========================================

import os
import sys
import sqlite3
import calendar
import subprocess
from datetime import datetime, timedelta

try:
    from fpdf import FPDF
    import arabic_reshaper
    from bidi.algorithm import get_display
except ImportError:
    subprocess.check_call([sys.executable, "-m", "pip", "install", "fpdf2", "arabic-reshaper", "python-bidi"])
    from fpdf import FPDF
    import arabic_reshaper
    from bidi.algorithm import get_display
from db_path import get_db_path

class ArabicPDF(FPDF):
    def __init__(self):
        super().__init__(PAGE_SETTINGS['orientation'], 'mm', 'A4')
        self.set_margins(
            PAGE_SETTINGS['margin_left'],
            PAGE_SETTINGS['margin_top'],
            PAGE_SETTINGS['margin_right']
        )
        self.set_auto_page_break(auto=True, margin=PAGE_SETTINGS['margin_bottom'])
        
        # إضافة الخطوط
        fonts_dir = os.path.join(os.path.dirname(__file__), 'fonts')
        calibri_path = os.path.join(fonts_dir, 'calibri.ttf')
        calibri_bold_path = os.path.join(fonts_dir, 'calibrib.ttf')
        
        if os.path.exists(calibri_path):
            self.add_font('Calibri', '', calibri_path)
            self.calibri_available = True
        else:
            self.calibri_available = False
            
        if os.path.exists(calibri_bold_path):
            self.add_font('Calibri', 'B', calibri_bold_path)
            self.calibri_bold_available = True
        else:
            self.calibri_bold_available = False
        
        # تعيين الخط الافتراضي والحدود
        if self.calibri_available:
            self.set_font('Calibri', '', 10)
        else:
            self.set_font('Arial', '', 10)

        # تعيين نمط الحدود من الإعدادات
        self.set_draw_color(
            BORDER_STYLE['color_r'],
            BORDER_STYLE['color_g'],
            BORDER_STYLE['color_b']
        )
        self.set_line_width(BORDER_STYLE['width'])

    def set_main_title_font(self):
        """خط العناوين الرئيسية - Calibri 15 Bold"""
        if self.calibri_bold_available:
            self.set_font('Calibri', 'B', 15)
        else:
            self.set_font('Arial', 'B', 15)

    def set_subtitle_font(self):
        """خط العناوين الفرعية - Calibri 14 Bold"""
        if self.calibri_bold_available:
            self.set_font('Calibri', 'B', 14)
        else:
            self.set_font('Arial', 'B', 14)

    def set_detail_font(self):
        """خط التفاصيل - Calibri 13 Bold"""
        if self.calibri_bold_available:
            self.set_font('Calibri', 'B', 12)
        else:
            self.set_font('Arial', 'B', 12)

    def set_table_header_font(self):
        """خط رؤوس الجدول - Calibri 15 Bold"""
        if self.calibri_bold_available:
            self.set_font('Calibri', 'B', 12)
        else:
            self.set_font('Arial', 'B', 12)

    def set_table_row_font(self):
        """خط صفوف الجدول - Calibri 13 Bold"""
        if self.calibri_bold_available:
            self.set_font('Calibri', 'B', 10)
        else:
            self.set_font('Arial', 'B', 10)

    def ar_text(self, txt: str) -> str:
        """تحويل النص العربي"""
        reshaped = arabic_reshaper.reshape(str(txt))
        return get_display(reshaped)

def get_weeks_in_month(year, month):
    """حساب الأسابيع في الشهر (الاثنين بداية الأسبوع)"""
    first_day = datetime(year, month, 1)
    last_day = datetime(year, month, calendar.monthrange(year, month)[1])
    
    # العثور على أول اثنين
    days_until_monday = (7 - first_day.weekday()) % 7
    if first_day.weekday() != 0:
        first_monday = first_day + timedelta(days=days_until_monday)
    else:
        first_monday = first_day
    
    weeks = []
    current_monday = first_monday
    
    while current_monday <= last_day:
        week_end = current_monday + timedelta(days=6)
        if week_end > last_day:
            week_end = last_day
        
        weeks.append({
            'start': current_monday,
            'end': week_end,
            'week_num': len(weeks) + 1
        })
        
        current_monday += timedelta(days=7)
    
    return weeks

def add_page_footer(pdf, page_width, y):
    """إضافة المعلومات والتوقيع في نهاية الصفحة"""
    # المعلومات
    y += 5
    pdf.set_detail_font()

    notes = [
        "• يتم وضع علامة (×) في الخانة المقابلة للحصة التي غاب فيها التلميذ"
    ]

    for note in notes:
        pdf.set_xy(0.2, y)
        pdf.cell(page_width, 5, pdf.ar_text(note), border=0, align='R')
        y += 6

    # التوقيع والتاريخ
    y += 2
    pdf.set_xy(0.2, y)
    pdf.cell(page_width / 2, 8, pdf.ar_text(f"التاريخ: {datetime.now().strftime('%Y-%m-%d')}"), border=0, align='R')

    pdf.set_xy(0.2 + page_width / 2, y)
    pdf.cell(page_width / 2, 8, pdf.ar_text("التوقيع: ________________"), border=0, align='R')

def generate_attendance_sheet_pdf(section, year, month, month_name, db_path="data.db"):
    """إنشاء ورقة متابعة الغياب PDF"""
    try:
        print(f"🔧 بدء إنشاء ورقة PDF للقسم: {section}")
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # جلب معلومات القسم
        try:
            cursor.execute("""
                SELECT القسم, المادة, اسم_الاستاذ
                FROM جدول_المواد_والاقسام
                WHERE القسم = ?
                LIMIT 1
            """, (section,))

            section_info = cursor.fetchone()
            if not section_info:
                section_info = (section, "غير محدد", "غير محدد")
        except Exception as e:
            print(f"⚠️ خطأ في جلب معلومات القسم: {e}")
            section_info = (section, "غير محدد", "غير محدد")
        
        # جلب التلاميذ مرتبين حسب الرمز
        cursor.execute("""
            SELECT id, اسم_التلميذ, رمز_التلميذ
            FROM جدول_البيانات 
            WHERE القسم = ? AND اسم_التلميذ IS NOT NULL AND اسم_التلميذ != ''
            ORDER BY CAST(رمز_التلميذ AS INTEGER) ASC, اسم_التلميذ ASC
        """, (section,))
        
        students = cursor.fetchall()
        conn.close()
        
        if not students:
            raise Exception(f"لا يوجد تلاميذ في القسم '{section}'")
        
        # حساب الأسابيع
        weeks = get_weeks_in_month(year, month)

        # تحديث أسماء الأعمدة بالتواريخ الفعلية
        week_names = {}

        # ترتيب الأسابيع بشكل صحيح (الأسبوع 1 = الأول، الأسبوع 5 = الأخير)
        for i, week in enumerate(weeks):
            week_number = i + 1  # الأسبوع الأول = 1، الثاني = 2، إلخ
            week_key = f'week{week_number}'
            monday_date = week['start'].strftime('%d/%m')
            week_names[week_key] = f"الأسبوع {week_number}\n{monday_date}"

        # إضافة الأسبوع الخامس دائماً (فارغ إذا لم يكن موجوداً)
        if len(weeks) < 5:
            for week_num in range(len(weeks) + 1, 6):
                week_key = f'week{week_num}'
                week_names[week_key] = f"الأسبوع {week_num}\n--/--"

        # تحديث ترتيب الأعمدة ليشمل دائماً 5 أسابيع
        actual_column_order = []

        # إضافة الأسابيع بترتيب عكسي (من الخامس إلى الأول)
        for week_num in range(5, 0, -1):
            week_key = f'week{week_num}'
            actual_column_order.append(week_key)

        # إضافة باقي الأعمدة
        for col in COLUMN_ORDER:
            if not col.startswith('week'):
                actual_column_order.append(col)
        
        # إنشاء PDF
        pdf = ArabicPDF()
        pdf.add_page()
        
        # الشعار والعنوان
        y = 1  # بداية أعلى بسبب الهوامش الصغيرة
        page_width = 210 - 0.4  # عرض الصفحة مطروح منه الهوامش

        # شعار المؤسسة (إذا كان متوفراً)
        logo_path = None
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT ImagePath1 FROM بيانات_المؤسسة LIMIT 1")
            logo_row = cursor.fetchone()
            if logo_row and os.path.exists(logo_row[0]):
                logo_path = logo_row[0]
            conn.close()
        except:
            pass

        if logo_path:
            pdf.image(logo_path, x=(page_width - 40) / 2 + 0.2, y=y, w=40, h=20)
            y += 20
        else:
            # شعار نصي
            pdf.set_main_title_font()
            pdf.set_xy(0.2, y)
            pdf.cell(page_width, 10, pdf.ar_text("🏫 مؤسسة التعليم"), border=0, align='C')
            y += 8

        # عنوان الورقة
        pdf.set_main_title_font()
        pdf.set_xy(0.2, y)
        pdf.cell(page_width, 10, pdf.ar_text("ورقة متابعة الغياب الشهرية"), border=0, align='C')
        y += 8  # تقليل المسافة إلى 5 نقط

        pdf.set_subtitle_font()
        pdf.set_xy(0.2, y)
        pdf.cell(page_width, 8, pdf.ar_text(f"شهر {month_name} {year}"), border=0, align='C')
        y += 8  # تقليل المسافة إلى 5 نقط
        
        # معلومات القسم
        pdf.set_detail_font()
        info_y = y

        # استخدام العروض القابلة للتعديل
        x_pos = PAGE_SETTINGS['margin_left']

        # الصف الأول من المعلومات
        pdf.set_xy(x_pos, info_y)
        pdf.cell(INFO_TABLE_WIDTHS['section'], ROW_HEIGHTS['info_row'], pdf.ar_text(f"القسم: {section_info[0]}"), border=1, align='C')
        x_pos += INFO_TABLE_WIDTHS['section']

        pdf.set_xy(x_pos, info_y)
        pdf.cell(INFO_TABLE_WIDTHS['subject'], ROW_HEIGHTS['info_row'], pdf.ar_text(f"المادة: {section_info[1]}"), border=1, align='C')
        x_pos += INFO_TABLE_WIDTHS['subject']

        pdf.set_xy(x_pos, info_y)
        pdf.cell(INFO_TABLE_WIDTHS['teacher'], ROW_HEIGHTS['info_row'], pdf.ar_text(f"الأستاذ(ة): {section_info[2]}"), border=1, align='C')
        x_pos += INFO_TABLE_WIDTHS['teacher']

        pdf.set_xy(x_pos, info_y)
        pdf.cell(INFO_TABLE_WIDTHS['count'], ROW_HEIGHTS['info_row'], pdf.ar_text(f"عدد التلاميذ: {len(students)}"), border=1, align='C')

        y += ROW_HEIGHTS['info_row'] + 0  # تقليل المسافة إلى 5 نقط
        
        # جدول الغياب
        pdf.set_table_header_font()

        # استخدام إعدادات عرض الأعمدة من الإعدادات
        col_widths = COLUMN_WIDTHS.copy()

        # رأس الجدول - الصف الأول (الأعمدة الرئيسية)
        x = PAGE_SETTINGS['margin_left']

        for column_key in actual_column_order:
            if column_key.startswith('week'):
                # للأسابيع، استخدام التاريخ الفعلي
                column_name = week_names.get(column_key, COLUMN_NAMES.get(column_key, column_key))
                # عرض الأسبوع يساوي 3 حصص
                cell_width = col_widths['session'] * 3
            else:
                # للأعمدة الأخرى
                column_name = COLUMN_NAMES[column_key]
                cell_width = col_widths[column_key]

            # رسم إطار الخلية أولاً
            pdf.rect(x, y, cell_width, ROW_HEIGHTS['header_main'])

            # كتابة النص في وسط الخلية
            if '\n' in column_name:
                lines = column_name.split('\n')
                # السطر الأول
                pdf.set_xy(x, y + 2)
                pdf.cell(cell_width, ROW_HEIGHTS['header_main']/2 - 2, pdf.ar_text(lines[0]), border=0, align='C')
                # السطر الثاني
                pdf.set_xy(x, y + ROW_HEIGHTS['header_main']/2 + 1)
                pdf.cell(cell_width, ROW_HEIGHTS['header_main']/2 - 2, pdf.ar_text(lines[1]), border=0, align='C')
            else:
                pdf.set_xy(x, y + ROW_HEIGHTS['header_main']/4)
                pdf.cell(cell_width, ROW_HEIGHTS['header_main']/2, pdf.ar_text(column_name), border=0, align='C')

            x += cell_width

        # رأس الجدول - الصف الثاني (الحصص الفرعية)
        y += ROW_HEIGHTS['header_main']
        x = PAGE_SETTINGS['margin_left']

        for column_key in actual_column_order:
            if column_key.startswith('week'):
                # إضافة رؤوس الحصص الفرعية للأسابيع (معكوسة: ح3، ح2، ح1)
                for session_num in range(3, 0, -1):  # عكس الترتيب
                    pdf.set_xy(x, y)
                    pdf.cell(
                        col_widths['session'],
                        ROW_HEIGHTS['header_sub'],
                        pdf.ar_text(f"ح{session_num}"),
                        border=1,
                        align='C'
                    )
                    x += col_widths['session']
            else:
                # للأعمدة الأخرى، خلية فارغة بنفس الارتفاع
                pdf.set_xy(x, y)
                pdf.cell(
                    col_widths[column_key],
                    ROW_HEIGHTS['header_sub'],
                    "",
                    border=1,
                    align='C'
                )
                x += col_widths[column_key]

        y += ROW_HEIGHTS['header_sub']
        
        # صفوف التلاميذ (حسب الترتيب المحدد في الإعدادات)
        pdf.set_table_row_font()
        for i, student in enumerate(students, 1):
            student_id, student_name, student_code = student

            x = PAGE_SETTINGS['margin_left']

            # بيانات التلميذ حسب ترتيب الأعمدة
            student_data = {
                'name': pdf.ar_text(str(student_name)),
                'id': str(student_code or student_id),
                'order': str(i)
            }

            # إضافة بيانات الأسابيع (دائماً 5 أسابيع)
            for week_num in range(1, 6):
                week_key = f'week{week_num}'
                student_data[week_key] = ""  # خلايا فارغة للتعبئة اليدوية

            # عرض الأعمدة حسب الترتيب المحدد
            for column_key in actual_column_order:
                if column_key.startswith('week'):
                    # للأسابيع، إنشاء 3 خلايا فرعية للحصص (معكوسة: ح3، ح2، ح1)
                    for session_num in range(3):  # لا نحتاج لعكس هنا لأنها خلايا فارغة
                        pdf.set_xy(x, y)
                        pdf.cell(
                            col_widths['session'],
                            ROW_HEIGHTS['student_row'],
                            "",  # خلية فارغة للتعبئة اليدوية
                            border=1,
                            align='C'
                        )
                        x += col_widths['session']
                else:
                    # للأعمدة الأخرى
                    pdf.set_xy(x, y)
                    align = 'R' if column_key == 'name' else 'C'
                    pdf.cell(
                        col_widths[column_key],
                        ROW_HEIGHTS['student_row'],
                        student_data[column_key],
                        border=1,
                        align=align
                    )
                    x += col_widths[column_key]

            y += ROW_HEIGHTS['student_row']

            # انتقال لصفحة جديدة عند الحاجة
            if y > 250:  # تعديل للصفحة العمودية مع مساحة للمعلومات
                # إضافة المعلومات والتوقيع في نهاية الصفحة
                add_page_footer(pdf, page_width, y)
                pdf.add_page()
                y = 20

        # إضافة المعلومات والتوقيع في نهاية الصفحة الأخيرة
        add_page_footer(pdf, page_width, y)
        
        return pdf
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء PDF: {e}")
        raise

def create_attendance_sheet_report(section, year, month, month_name, db_path="data.db"):
    """إنشاء تقرير ورقة متابعة الغياب وفتحه"""
    try:
        # إنشاء PDF
        pdf = generate_attendance_sheet_pdf(section, year, month, month_name, db_path)
        
        # إنشاء مجلد التقارير
        reports_dir = os.path.join(os.path.expanduser('~'), 'Desktop', 'تقارير متابعة الغياب')
        if not os.path.exists(reports_dir):
            os.makedirs(reports_dir)
        
        # تحديد اسم الملف (إزالة الأحرف الخاصة)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        safe_section = section.replace("/", "_").replace("\\", "_").replace(" ", "_")
        filename = f"ورقة_متابعة_الغياب_{safe_section}_{month_name}_{year}_{timestamp}.pdf"
        output_path = os.path.join(reports_dir, filename)
        
        # حفظ الملف
        pdf.output(output_path)
        print(f"✅ تم إنشاء التقرير: {output_path}")
        
        # فتح الملف
        try:
            if sys.platform == 'win32':
                os.startfile(output_path)
            elif sys.platform == 'darwin':  # macOS
                subprocess.call(['open', output_path])
            else:  # Linux
                subprocess.call(['xdg-open', output_path])
        except Exception as e:
            print(f"⚠️ تم إنشاء التقرير ولكن تعذر فتحه: {e}")
        
        return True, output_path, "تم إنشاء ورقة متابعة الغياب بنجاح"
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء التقرير: {e}")
        return False, None, f"خطأ في إنشاء التقرير: {str(e)}"

if __name__ == "__main__":
    # اختبار
    success, path, message = create_attendance_sheet_report("الأولى ابتدائي", 2024, 1, "يناير")
    print(f"النتيجة: {success}")
    print(f"المسار: {path}")
    print(f"الرسالة: {message}")
