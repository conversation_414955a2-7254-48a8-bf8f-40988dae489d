# -*- coding: utf-8 -*-

import sys
import os
import sqlite3
from PyQt5.QtWidgets import (QApplication, QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame,
                           QGraphicsDropShadowEffect)
from PyQt5.QtGui import QFont, QColor, QIcon, QPixmap
from PyQt5.QtCore import Qt
from db_path import get_db_path

class Sub01Window(QWidget):
    """نافذة المعين في الحراسة العامة"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("المعين في الحراسة العامة")
        self.setFixedSize(1000, 600)
        self.setLayoutDirection(Qt.RightToLeft)

        # إضافة أيقونة البرنامج إلى النافذة
        icon_path = "01.ico"
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))

        # تعريف مسار قاعدة البيانات
        self.db_path = get_db_path()

        # إعداد واجهة المستخدم
        self.setup_ui()

        # تحميل البيانات من قاعدة البيانات
        self.load_data()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(5)

        # إنشاء مربع تسمية "المعين في الحراسة العامة"
        self.title_label = QLabel("المعين في إدارة التعليم الخصوصي")
        self.title_label.setAlignment(Qt.AlignCenter)
        self.title_label.setFont(QFont("Calibri", 24, QFont.Bold))
        self.title_label.setStyleSheet("""
            background-color: #5C83B4;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 10px;
        """)

        # إضافة تأثير الظل
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(15)
        shadow.setXOffset(5)
        shadow.setYOffset(5)
        shadow.setColor(QColor(0, 0, 0, 150))
        self.title_label.setGraphicsEffect(shadow)

        # إنشاء مربع تسمية "السلك" كعنوان ثابت
        self.cycle_label = QLabel(" 555")
        self.cycle_label.setAlignment(Qt.AlignCenter)
        self.cycle_label.setFont(QFont("Calibri", 18, QFont.Bold))
        self.cycle_label.setWordWrap(True)  # السماح بتقسيم النص على أسطر متعددة
        self.cycle_label.setMinimumHeight(80)  # تحديد ارتفاع أدنى
        self.cycle_label.setStyleSheet("""
            background-color: #5C83B4;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 5px;
        """)

        # إضافة تأثير الظل
        shadow2 = QGraphicsDropShadowEffect()
        shadow2.setBlurRadius(15)
        shadow2.setXOffset(5)
        shadow2.setYOffset(5)
        shadow2.setColor(QColor(0, 0, 0, 150))
        self.cycle_label.setGraphicsEffect(shadow2)

        # إنشاء مربع تسمية "المؤسسة" - سيتم تحديثه من قاعدة البيانات
        self.institution_label = QLabel("المؤسسة")
        self.institution_label.setAlignment(Qt.AlignCenter)
        self.institution_label.setFont(QFont("Calibri", 22, QFont.Bold))
        self.institution_label.setStyleSheet("""
            background-color: white;
            color: black;
            padding: 12px;
            border-radius: 8px;
            border: 2px solid #5C83B4;
            margin: 5px;
        """)

        # إضافة تأثير الظل
        shadow3 = QGraphicsDropShadowEffect()
        shadow3.setBlurRadius(15)
        shadow3.setXOffset(5)
        shadow3.setYOffset(5)
        shadow3.setColor(QColor(0, 0, 0, 150))
        self.institution_label.setGraphicsEffect(shadow3)

        # إنشاء مربع تسمية "السنة الدراسية"
        self.academic_year_label = QLabel("السنة الدراسية : ")
        self.academic_year_label.setAlignment(Qt.AlignCenter)
        self.academic_year_label.setFont(QFont("Calibri", 22, QFont.Bold))
        self.academic_year_label.setStyleSheet("""
            background-color: white;
            color: black;
            padding: 12px;
            border-radius: 8px;
            border: 2px solid #5C83B4;
            margin: 5px;
        """)

        # إضافة تأثير الظل
        shadow4 = QGraphicsDropShadowEffect()
        shadow4.setBlurRadius(15)
        shadow4.setXOffset(5)
        shadow4.setYOffset(5)
        shadow4.setColor(QColor(0, 0, 0, 150))
        self.academic_year_label.setGraphicsEffect(shadow4)

        # إنشاء مربع تسمية "الهاتف"
        self.phone_label = QLabel("الهاتف : ")
        self.phone_label.setAlignment(Qt.AlignCenter)
        self.phone_label.setFont(QFont("Calibri", 22, QFont.Bold))
        self.phone_label.setStyleSheet("""
            background-color: white;
            color: black;
            padding: 12px;
            border-radius: 8px;
            border: 2px solid #5C83B4;
            margin: 5px;
        """)

        # إضافة تأثير الظل
        shadow5 = QGraphicsDropShadowEffect()
        shadow5.setBlurRadius(15)
        shadow5.setXOffset(5)
        shadow5.setYOffset(5)
        shadow5.setColor(QColor(0, 0, 0, 150))
        self.phone_label.setGraphicsEffect(shadow5)

        # إنشاء مربع تسمية "رقم التسجيل"
        self.registration_label = QLabel("رقم التسجيل : ")
        self.registration_label.setAlignment(Qt.AlignCenter)
        self.registration_label.setFont(QFont("Calibri", 22, QFont.Bold))
        self.registration_label.setStyleSheet("""
            background-color: white;
            color: black;
            padding: 12px;
            border-radius: 8px;
            border: 2px solid #5C83B4;
            margin: 5px;
        """)

        # إضافة تأثير الظل
        shadow6 = QGraphicsDropShadowEffect()
        shadow6.setBlurRadius(15)
        shadow6.setXOffset(5)
        shadow6.setYOffset(5)
        shadow6.setColor(QColor(0, 0, 0, 150))
        self.registration_label.setGraphicsEffect(shadow6)



        # إضافة العناصر إلى التخطيط الرئيسي
        main_layout.addWidget(self.title_label)
        main_layout.addWidget(self.cycle_label)
        main_layout.addWidget(self.institution_label)
        main_layout.addWidget(self.academic_year_label)
        main_layout.addWidget(self.phone_label)
        main_layout.addWidget(self.registration_label)

        # إضافة مساحة فارغة في الأسفل
        main_layout.addStretch()

    def load_data(self):
        """تحميل البيانات من قاعدة البيانات"""
        try:
            # التحقق من وجود قاعدة البيانات
            if not os.path.exists(self.db_path):
                print(f"خطأ: ملف قاعدة البيانات '{self.db_path}' غير موجود.")
                return

            # فتح اتصال بقاعدة البيانات
            conn = sqlite3.connect(get_db_path())
            cursor = conn.cursor()

            # استعلام للحصول على بيانات المؤسسة
            cursor.execute("""
                SELECT رقم_الهاتف, المؤسسة, السنة_الدراسية, رقم_التسجيل
                FROM بيانات_المؤسسة LIMIT 1
            """)
            result = cursor.fetchone()

            if result:
                # تحديث مربعات التسمية بالبيانات المسترجعة
                phone_value = result[0] if result[0] else "غير محدد"
                institution_value = result[1] if result[1] else "غير محدد"
                academic_year = result[2] if result[2] else "غير محدد"
                registration_number = result[3] if result[3] else "غير محدد"

                # السلك يبقى كعنوان ثابت
                self.cycle_label.setText("نظام متكامل ذكي لتسيير الجوانب التربوية والمالية بمؤسسات الدعم التربوي بكفاءة واحتراف")
                self.institution_label.setText(institution_value)
                self.academic_year_label.setText("السنة الدراسية : " + academic_year)
                self.phone_label.setText("الهاتف : " + phone_value)
                self.registration_label.setText("رقم التسجيل : " + registration_number)
            else:
                print("لم يتم العثور على بيانات المؤسسة.")
                # قيم افتراضية
                self.cycle_label.setText("55555")
                self.institution_label.setText("غير محدد")
                self.academic_year_label.setText("السنة الدراسية : غير محدد")
                self.phone_label.setText("الهاتف : غير محدد")
                self.registration_label.setText("رقم التسجيل : غير محدد")

            # إغلاق الاتصال بقاعدة البيانات
            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل البيانات: {str(e)}")

    def refresh_data(self):
        """تحديث البيانات من قاعدة البيانات"""
        self.load_data()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    window = Sub01Window()
    window.show()
    sys.exit(app.exec_())
