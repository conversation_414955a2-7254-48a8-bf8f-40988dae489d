# نظام الاختيار اليدوي للتوصيلات الموحدة

## 📋 **المشكلة والحل**

### **المشكلة السابقة:**
- البرنامج كان يقوم بجمع التوصيلات تلقائياً رغم اختلاف تاريخ الدفع
- المستخدم لم يكن لديه تحكم في اختيار التوصيلات المراد جمعها
- عدم وجود مرونة في اختيار التوصيلات حسب الحاجة

### **الحل الجديد:**
- ✅ **اختيار يدوي:** المستخدم يحدد التوصيلات التي يريد جمعها
- ✅ **صناديق اختيار:** إضافة عمود اختيار في كل جدول
- ✅ **إزالة تلقائية:** إزالة علامات الاختيار بعد الطباعة
- ✅ **مرونة كاملة:** تحكم كامل في عملية التجميع

---

## 🔧 **التحديثات المطبقة**

### **1. جدول الواجبات الشهرية**

#### **إضافة عمود الاختيار:**
```python
# تحديث أعمدة الجدول
columns = ["ID", "اختيار", "القسم", "الشهر", "السنة", "المطلوب", "المدفوع", "المتبقي", "تاريخ الدفع", "الحالة", "ملاحظات"]

# إضافة صندوق اختيار لكل صف
checkbox = QCheckBox()
checkbox.setStyleSheet("QCheckBox { margin: 5px; }")
self.duties_table.setCellWidget(row, 1, checkbox)
```

#### **زر طباعة التوصيل الموحد للمحدد:**
```python
print_unified_selected_btn = QPushButton("🖨️ طباعة التوصيل الموحد للمحدد")
print_unified_selected_btn.clicked.connect(self.print_unified_selected_duties)
```

#### **دالة طباعة التوصيل الموحد للمحدد:**
```python
def print_unified_selected_duties(self):
    """طباعة التوصيل الموحد للواجبات المحددة"""
    # جمع الواجبات المحددة
    selected_duties = []
    
    for row in range(self.duties_table.rowCount()):
        checkbox = self.duties_table.cellWidget(row, 1)
        if checkbox and checkbox.isChecked():
            # جمع بيانات الواجب
            duty_data = {...}
            selected_duties.append(duty_data)
    
    # التحقق من وجود واجبات محددة
    if not selected_duties:
        QMessageBox.warning(self, "تحذير", "يرجى تحديد واجبات لطباعة التوصيل الموحد.")
        return
    
    # إنشاء وطباعة التوصيل
    receipt_content = self.create_unified_selected_receipt_content(selected_duties)
    self.send_to_thermal_printer(receipt_content)
    
    # إزالة علامات الاختيار بعد الطباعة
    for row in range(self.duties_table.rowCount()):
        checkbox = self.duties_table.cellWidget(row, 1)
        if checkbox and checkbox.isChecked():
            checkbox.setChecked(False)
```

---

### **2. جدول واجبات التسجيل**

#### **إضافة عمود الاختيار:**
```python
# تحديث أعمدة الجدول
columns = ["ID", "اختيار", "القسم", "نوع الدفعة", "المبلغ", "طريقة الدفع", "تاريخ الدفع", "ملاحظات"]

# إضافة صندوق اختيار لكل صف
checkbox = QCheckBox()
checkbox.setStyleSheet("QCheckBox { margin: 5px; }")
self.registration_table.setCellWidget(row, 1, checkbox)
```

#### **زر طباعة التوصيل الموحد للمحدد:**
```python
print_unified_reg_selected_btn = QPushButton("🖨️ طباعة التوصيل الموحد للمحدد")
print_unified_reg_selected_btn.clicked.connect(self.print_unified_selected_registration)
```

#### **دالة طباعة التوصيل الموحد للمحدد:**
```python
def print_unified_selected_registration(self):
    """طباعة التوصيل الموحد لواجبات التسجيل المحددة"""
    # جمع الدفعات المحددة
    selected_payments = []
    
    for row in range(self.registration_table.rowCount()):
        checkbox = self.registration_table.cellWidget(row, 1)
        if checkbox and checkbox.isChecked():
            # جمع بيانات الدفعة
            payment_data = {...}
            selected_payments.append(payment_data)
    
    # التحقق من وجود دفعات محددة
    if not selected_payments:
        QMessageBox.warning(self, "تحذير", "يرجى تحديد دفعات لطباعة التوصيل الموحد.")
        return
    
    # إنشاء وطباعة التوصيل
    receipt_content = self.create_unified_selected_registration_content(selected_payments)
    self.send_to_thermal_printer(receipt_content)
    
    # إزالة علامات الاختيار بعد الطباعة
    for row in range(self.registration_table.rowCount()):
        checkbox = self.registration_table.cellWidget(row, 1)
        if checkbox and checkbox.isChecked():
            checkbox.setChecked(False)
```

---

## 🎯 **منطق التجميع الذكي**

### **للواجبات الشهرية:**
```python
# تجميع حسب الشهر والسنة وتاريخ الدفع (اليوم فقط، ليس التوقيت)
for duty in selected_duties:
    # استخراج التاريخ فقط (بدون التوقيت) للتجميع
    payment_date_only = None
    if payment_date:
        if ' ' in str(payment_date):
            payment_date_only = str(payment_date).split(' ')[0]
        else:
            payment_date_only = str(payment_date)
    
    # مفتاح التجميع: الشهر + السنة + تاريخ الدفع (بدون توقيت)
    key = f"{month} {year} - {payment_date_only or 'غير محدد'}"
```

### **لواجبات التسجيل:**
```python
# تجميع حسب نوع الدفعة وتاريخ الدفع (اليوم فقط، ليس التوقيت)
for payment in selected_payments:
    # استخراج التاريخ فقط (بدون التوقيت) للتجميع
    payment_date_only = None
    if payment_date:
        if ' ' in str(payment_date):
            payment_date_only = str(payment_date).split(' ')[0]
        else:
            payment_date_only = str(payment_date)
    
    # مفتاح التجميع: نوع الدفعة + تاريخ الدفع (بدون توقيت)
    key = f"{payment_type} - {payment_date_only or 'غير محدد'}"
```

---

## 🖼️ **واجهة المستخدم الجديدة**

### **جدول الواجبات الشهرية:**
```
┌─────────┬─────────┬─────────┬─────────┬─────────┬─────────┬─────────┬─────────┬─────────┬─────────┬─────────┐
│   ID    │ اختيار  │  القسم  │  الشهر  │  السنة  │ المطلوب │ المدفوع │ المتبقي │ تاريخ الدفع │ الحالة │ ملاحظات │
├─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┤
│ (مخفي)  │   ☑️    │ الرياضيات│  يناير  │  2024   │ 200.00  │ 200.00  │  0.00   │2024-01-15│  مدفوع  │         │
│ (مخفي)  │   ☐     │  العلوم  │  يناير  │  2024   │ 200.00  │ 200.00  │  0.00   │2024-01-20│  مدفوع  │         │
│ (مخفي)  │   ☑️    │ العربية  │  فبراير │  2024   │ 200.00  │ 200.00  │  0.00   │2024-02-10│  مدفوع  │         │
└─────────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────────┘
```

### **الأزرار الجديدة:**
```
┌─────────────┬─────────────┬─────────────┬─────────────────────────────────────┐
│    تعديل    │    حذف      │ طباعة توصيل │    طباعة التوصيل الموحد للمحدد      │
└─────────────┴─────────────┴─────────────┴─────────────────────────────────────┘
```

---

## 📝 **مثال على الاستخدام**

### **السيناريو:**
المستخدم لديه 4 واجبات شهرية:
1. ✅ **يناير 2024 - الرياضيات - 2024-01-15** (محدد)
2. ❌ **يناير 2024 - العلوم - 2024-01-20** (غير محدد)
3. ✅ **فبراير 2024 - العربية - 2024-02-10** (محدد)
4. ❌ **فبراير 2024 - الفيزياء - 2024-02-15** (غير محدد)

### **النتيجة:**
```
========================================
           المؤسسة التعليمية
========================================

              وصل الأداء

    اسم التلميذ: أحمد محمد علي
    رمز التلميذ: ST001

الأقسام: الرياضيات، العربية
----------------------------------------

                الشهر: يناير 2024
        المطلوب: 200.00 درهم
        المدفوع: 200.00 درهم
        المتبقي: 0.00 درهم
    تاريخ الدفع: 2024-01-15
            الحالة: مدفوع
------------------------------

                الشهر: فبراير 2024
        المطلوب: 200.00 درهم
        المدفوع: 200.00 درهم
        المتبقي: 0.00 درهم
    تاريخ الدفع: 2024-02-10
            الحالة: مدفوع
------------------------------

تاريخ الطباعة 2024-03-15 14:30:25

        نتمنى لكم التوفيق والنجاح
```

---

## ✅ **المزايا الجديدة**

### **للمستخدمين:**
- 🎯 **تحكم كامل:** اختيار التوصيلات المراد جمعها يدوياً
- 🔄 **مرونة عالية:** إمكانية جمع أي مجموعة من التوصيلات
- 🧹 **تنظيف تلقائي:** إزالة علامات الاختيار بعد الطباعة
- ⚡ **سهولة الاستخدام:** واجهة بسيطة وواضحة

### **للنظام:**
- 🛡️ **أمان أكبر:** منع التجميع الخاطئ للتوصيلات
- 📊 **دقة عالية:** تجميع حسب التاريخ الفعلي للدفع
- 🔧 **صيانة أسهل:** كود منظم ومفهوم
- 🚀 **أداء محسن:** معالجة فقط للتوصيلات المحددة

---

## 📁 **الملفات المعدلة**

### **`multi_section_duties_window.py`**
- ✅ إضافة عمود الاختيار في جدول الواجبات الشهرية
- ✅ إضافة عمود الاختيار في جدول واجبات التسجيل
- ✅ إضافة أزرار طباعة التوصيل الموحد للمحدد
- ✅ إضافة دوال طباعة التوصيل الموحد للواجبات المحددة
- ✅ إضافة دوال طباعة التوصيل الموحد لواجبات التسجيل المحددة
- ✅ تحديث منطق التجميع الذكي
- ✅ تحديث تلوين الصفوف لتجاهل عمود الاختيار
- ✅ تحديث دوال التعديل والحذف والطباعة

---

## 🎉 **النتيجة النهائية**

تم تطبيق نظام الاختيار اليدوي بنجاح:

✅ **تحكم كامل:** المستخدم يحدد التوصيلات المراد جمعها  
✅ **مرونة عالية:** إمكانية اختيار أي مجموعة من التوصيلات  
✅ **تجميع ذكي:** يجمع حسب التاريخ الفعلي للدفع  
✅ **تنظيف تلقائي:** إزالة علامات الاختيار بعد الطباعة  
✅ **واجهة محسنة:** صناديق اختيار واضحة وسهلة الاستخدام  
✅ **عبارة ختامية:** "نتمنى لكم التوفيق والنجاح" في جميع التوصيلات  

النظام الآن يوفر تحكماً كاملاً ومرونة عالية في إدارة التوصيلات الموحدة! 🎯
