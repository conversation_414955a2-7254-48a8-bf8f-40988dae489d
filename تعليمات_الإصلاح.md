# إصلاح مسار قاعدة البيانات - الحل البسيط

## 🎯 المشكلة
بعض ملفات البرنامج تبحث عن قاعدة البيانات في `_internal` وبعضها في مجلد التطبيق.

## ✅ الحل البسيط

### الخطوة 1: تشغيل سكريبت الإصلاح
```bash
python fix_db_paths.py
```

هذا السكريبت سيقوم بـ:
- ✅ إضافة `from db_path import get_db_path` لكل ملف
- ✅ استبدال جميع مراجع `"school_database.db"` بـ `get_db_path()`
- ✅ إنشاء نسخ احتياطية للملفات المعدلة

### الخطوة 2: اختبار البرنامج
```bash
python main_window.py
```

### الخطوة 3: التحزيم
```bash
pyinstaller build_app.spec
```

## 🎯 النتيجة

**قبل الإصلاح:**
```python
# في الملفات المختلفة
conn = sqlite3.connect("school_database.db")  # ❌ مسار مختلف
self.db_path = "school_database.db"           # ❌ مسار مختلف
```

**بعد الإصلاح:**
```python
# في جميع الملفات
from db_path import get_db_path
conn = sqlite3.connect(get_db_path())         # ✅ مسار موحد
self.db_path = get_db_path()                  # ✅ مسار موحد
```

## 📍 مسار قاعدة البيانات

- **في البيئة العادية:** نفس مجلد ملفات البرنامج
- **بعد التحزيم:** نفس مجلد التطبيق (وليس `_internal`)

## 🔧 ملفات الحل

1. **`db_path.py`** - دالة واحدة بسيطة لإرجاع مسار قاعدة البيانات
2. **`fix_db_paths.py`** - سكريبت لتحديث جميع الملفات
3. **`build_app.spec`** - ملف تحزيم بسيط

## ✅ مزايا هذا الحل

- 🎯 **بسيط:** دالة واحدة فقط
- 🔧 **مباشر:** يحل المشكلة بدون تعقيد
- 💾 **آمن:** ينشئ نسخ احتياطية
- 🚀 **سريع:** تحديث تلقائي لجميع الملفات

الآن جميع ملفات البرنامج ستجد قاعدة البيانات في نفس المكان! 🎉
