# تعليمات تحزيم نظام إدارة المدرسة

## المتطلبات الأساسية

### 1. Python
- تأكد من تثبيت Python 3.8 أو أحدث
- يمكن التحقق من الإصدار بالأمر: `python --version`

### 2. المكتبات المطلوبة
قم بتثبيت المكتبات التالية:
```bash
pip install PyQt5>=5.15.0
pip install fpdf2>=2.7.0
pip install arabic-reshaper>=3.0.0
pip install python-bidi>=0.4.2
pip install openpyxl>=3.1.0
pip install matplotlib>=3.5.0
pip install numpy>=1.21.0
pip install Pillow>=9.0.0
pip install pyinstaller
```

أو استخدم ملف requirements.txt إذا كان متوفراً:
```bash
pip install -r requirements.txt
```

## طرق التحزيم

### الطريقة الأولى: استخدام ملف الباتش المحسن (الأسهل والأفضل)
1. انقر نقراً مزدوجاً على ملف `build_app_fixed.bat`
2. انتظر حتى انتهاء العملية (قد تستغرق 5-10 دقائق)
3. ستجد البرنامج المحزم في مجلد `dist\نظام_إدارة_المدرسة\`

### الطريقة الثانية: استخدام ملف الباتش العادي
1. انقر نقراً مزدوجاً على ملف `build_app.bat`
2. انتظر حتى انتهاء العملية
3. ستجد البرنامج المحزم في مجلد `dist\نظام_إدارة_المدرسة\`

### الطريقة الثالثة: استخدام سطر الأوامر (للمتقدمين)
1. افتح موجه الأوامر (Command Prompt) في مجلد المشروع
2. نفذ أحد الأوامر التالية:

**للملف المحسن (يحل مشكلة jaraco.text):**
```bash
pyinstaller main_window_fixed.spec --clean --noconfirm
```

**للملف العادي:**
```bash
pyinstaller main_window.spec --clean --noconfirm
```

**مع خيارات إضافية لحل المشاكل:**
```bash
pyinstaller main_window.spec --clean --noconfirm --hidden-import=jaraco.text --collect-all=jaraco --collect-all=pkg_resources
```

## ملاحظات مهمة

### 1. ملف قاعدة البيانات
- ملف `data.db` لن يتم تضمينه داخل الملف التنفيذي
- سيتم إنشاؤه تلقائياً في نفس مجلد البرنامج عند التشغيل الأول
- هذا يسمح بالاحتفاظ بالبيانات حتى بعد تحديث البرنامج

### 2. المجلدات المطلوبة
سيتم إنشاء المجلدات التالية تلقائياً:
- `fonts/` - للخطوط العربية
- `logs/` - لملفات السجلات
- `reports/` - للتقارير المُنشأة

### 3. الملفات المُضمنة
تم تضمين الملفات التالية في التحزيم:
- جميع ملفات النوافذ الموجودة فعلياً
- ملفات النظام المالي والإدارة
- ملفات الطباعة والتقارير
- أيقونة البرنامج (01.ico)

## استكشاف الأخطاء

### خطأ: "ModuleNotFoundError: No module named 'jaraco.text'"
**هذا هو الخطأ الأكثر شيوعاً. الحلول:**

1. **استخدم ملف `build_app_fixed.bat`** - يحل المشكلة تلقائياً
2. **أو استخدم ملف `main_window_fixed.spec`** - محسن لهذه المشكلة
3. **أو ثبت المكتبات المطلوبة يدوياً:**
   ```bash
   pip install jaraco.text more-itertools importlib-metadata zipp
   pip install --upgrade setuptools pkg_resources
   ```

### خطأ: "Module not found"
- تأكد من تثبيت جميع المكتبات المطلوبة
- أضف اسم الوحدة المفقودة إلى قائمة `hiddenimports` في ملف `main_window.spec`
- استخدم `--collect-all=اسم_المكتبة` في سطر الأوامر

### خطأ: "File not found"
- تأكد من وجود جميع الملفات المذكورة في قائمة `datas`
- احذف الملفات غير الموجودة من القائمة
- تحقق من صحة مسارات الملفات

### البرنامج لا يعمل بعد التحزيم
- جرب تشغيل البرنامج من موجه الأوامر لرؤية رسائل الخطأ:
  ```bash
  cd "dist\نظام_إدارة_المدرسة"
  "نظام_إدارة_المدرسة.exe"
  ```
- تأكد من وجود جميع الملفات المطلوبة في مجلد البرنامج
- تحقق من وجود ملف `data.db` في نفس مجلد البرنامج

### خطأ: "Failed to execute script"
- جرب تشغيل البرنامج مع وضع التصحيح:
  ```bash
  pyinstaller main_window.spec --clean --noconfirm --debug=all
  ```
- تحقق من ملفات السجل في مجلد `build`

## تحسين الأداء

### تقليل حجم الملف التنفيذي
- تم استبعاد الوحدات غير المطلوبة مثل `tkinter` و `unittest`
- تم تفعيل ضغط UPX لتقليل الحجم

### تحسين سرعة التشغيل
- تم استخدام وضع "one-folder" بدلاً من "one-file" لتحسين الأداء
- تم تحسين قائمة `hiddenimports` لتشمل الوحدات المطلوبة فقط

## معلومات إضافية

- **نقطة الدخول**: `main_window.py`
- **اسم البرنامج المحزم**: `نظام_إدارة_المدرسة.exe`
- **مجلد الإخراج**: `dist\نظام_إدارة_المدرسة\`
- **نوع التحزيم**: One-folder (مجلد واحد)

## الدعم
في حالة مواجهة أي مشاكل، تأكد من:
1. تحديث PyInstaller إلى أحدث إصدار
2. التحقق من صحة مسارات الملفات
3. مراجعة رسائل الخطأ في موجه الأوامر
