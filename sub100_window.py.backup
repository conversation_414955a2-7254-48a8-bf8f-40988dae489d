"""
sub100_window.py - وحدة رسائل التأكيد المخصصة للبرنامج

هذا الملف يحتوي على دوال إنشاء رسائل التأكيد المخصصة المستخدمة في البرنامج.
تم استخراج هذه الدوال من ملفات مختلفة وتجميعها هنا لتسهيل الصيانة والتعديل.

تاريخ الإنشاء: 2023-10-15
آخر تحديث: 2023-10-15
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
                            QTextBrowser, QMessageBox, QFrame, QTreeWidget, QTreeWidgetItem,
                            QCheckBox, QSplitter, QDialogButtonBox, QStyle)
from PyQt5.QtGui import QFont, QIcon, QPixmap, QColor
from PyQt5.QtCore import Qt, QSize

class ConfirmationDialogs:
    """فئة تحتوي على دوال إنشاء رسائل التأكيد المخصصة"""

    @staticmethod
    def create_entry_confirmation_dialog(parent, selected_students):
        """إنشاء نافذة تأكيد السماح بالدخول"""
        confirm_dialog = QDialog(parent)
        confirm_dialog.setWindowTitle("تأكيد السماح بالدخول")
        confirm_dialog.setFixedSize(500, 400)
        confirm_dialog.setLayoutDirection(Qt.RightToLeft)

        # إضافة أيقونة البرنامج
        try:
            app_icon = QIcon("01.ico")
            confirm_dialog.setWindowIcon(app_icon)
        except Exception as e:
            print(f"خطأ في تحميل أيقونة البرنامج: {e}")

        # تنسيق النافذة
        confirm_dialog.setStyleSheet("""
            QDialog {
                background-color: #f0f8ff;
                border: 2px solid #0066cc;
                border-radius: 10px;
            }
            QLabel {
                color: #333333;
                font-weight: bold;
            }
            QTextBrowser {
                border: 1px solid #3498db;
                border-radius: 5px;
                padding: 10px;
                background-color: white;
            }
            QPushButton {
                border-radius: 5px;
                padding: 8px 15px;
                font-weight: bold;
                min-height: 35px;
            }
            QPushButton#confirm_btn {
                background-color: #27ae60;
                color: white;
            }
            QPushButton#confirm_btn:hover {
                background-color: #2ecc71;
                border: 2px solid #27ae60;
            }
            QPushButton#cancel_btn {
                background-color: #e74c3c;
                color: white;
            }
            QPushButton#cancel_btn:hover {
                background-color: #c0392b;
                border: 2px solid #e74c3c;
            }
        """)

        # إنشاء تخطيط النافذة
        layout = QVBoxLayout(confirm_dialog)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # إضافة أيقونة وعنوان
        header_layout = QHBoxLayout()

        # محاولة إضافة أيقونة البرنامج
        icon_label = QLabel()
        icon_label.setAlignment(Qt.AlignCenter)
        try:
            program_icon = QPixmap("01.ico")
            if not program_icon.isNull():
                program_icon = program_icon.scaled(48, 48, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                icon_label.setPixmap(program_icon)
                header_layout.addWidget(icon_label)
        except Exception as e:
            print(f"خطأ في تحميل أيقونة البرنامج للعنوان: {e}")

        # إضافة عنوان النافذة
        title_label = QLabel("تأكيد السماح بالدخول")
        title_label.setFont(QFont("Calibri", 16, QFont.Bold))
        title_label.setStyleSheet("color: #0066cc;")
        title_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(title_label, 1)

        layout.addLayout(header_layout)

        # إضافة رسالة التأكيد
        message_browser = QTextBrowser()
        message_browser.setReadOnly(True)

        # تنسيق النص بخط Calibri 13 أسود غامق وعرض المعلومات في جدول
        html_content = """
        <div dir='rtl' style='text-align: right;'>
            <p style='font-family: Calibri; font-size: 13pt; color: black; font-weight: bold;'>
                سيتم تسجيل السماح بالدخول للتلاميذ التالية أسماؤهم:
            </p>

            <table style='width: 100%; border-collapse: collapse; margin-top: 10px; font-family: Calibri; font-size: 13pt; color: black; font-weight: bold;'>
                <tr style='background-color: #0066cc; color: white;'>
                    <th style='border: 1.5px solid #0055bb; padding: 8px; text-align: center;'>ر.ت</th>
                    <th style='border: 1.5px solid #0055bb; padding: 8px; text-align: center;'>الرمز</th>
                    <th style='border: 1.5px solid #0055bb; padding: 8px; text-align: right;'>الاسم والنسب</th>
                </tr>
        """

        # إضافة معلومات التلاميذ في جدول
        for student in selected_students:
            html_content += f"""
                <tr style='border-bottom: 1px solid #dddddd;'>
                    <td style='border: 1.5px solid #0066cc; padding: 8px; text-align: center;'>{student.get('rt', '')}</td>
                    <td style='border: 1.5px solid #0066cc; padding: 8px; text-align: center;'>{student['code']}</td>
                    <td style='border: 1.5px solid #0066cc; padding: 8px; text-align: right;'>{student['name']}</td>
                </tr>
            """

        html_content += """
            </table>
        </div>
        """

        message_browser.setHtml(html_content)
        layout.addWidget(message_browser)

        # إضافة أزرار التأكيد والإلغاء
        buttons_layout = QHBoxLayout()

        confirm_btn = QPushButton("تأكيد")
        confirm_btn.setObjectName("confirm_btn")
        confirm_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        confirm_btn.setCursor(Qt.PointingHandCursor)
        confirm_btn.setFixedWidth(105)
        confirm_btn.clicked.connect(confirm_dialog.accept)

        cancel_btn = QPushButton("إلغاء")
        cancel_btn.setObjectName("cancel_btn")
        cancel_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        cancel_btn.setCursor(Qt.PointingHandCursor)
        cancel_btn.setFixedWidth(105)
        cancel_btn.clicked.connect(confirm_dialog.reject)

        buttons_layout.addWidget(confirm_btn)
        buttons_layout.addWidget(cancel_btn)

        layout.addLayout(buttons_layout)

        return confirm_dialog

    @staticmethod
    def create_late_confirmation_dialog(parent, selected_students):
        """إنشاء نافذة تأكيد تسجيل التأخر"""
        confirm_dialog = QDialog(parent)
        confirm_dialog.setWindowTitle("تأكيد تسجيل التأخر")
        confirm_dialog.setFixedSize(500, 400)
        confirm_dialog.setLayoutDirection(Qt.RightToLeft)

        # إضافة أيقونة البرنامج
        try:
            app_icon = QIcon("01.ico")
            confirm_dialog.setWindowIcon(app_icon)
        except Exception as e:
            print(f"خطأ في تحميل أيقونة البرنامج: {e}")

        # تنسيق النافذة
        confirm_dialog.setStyleSheet("""
            QDialog {
                background-color: #f0f8ff;
                border: 2px solid #0066cc;
                border-radius: 10px;
            }
            QLabel {
                color: #333333;
                font-weight: bold;
            }
            QTextBrowser {
                border: 1px solid #3498db;
                border-radius: 5px;
                padding: 10px;
                background-color: white;
            }
            QPushButton {
                border-radius: 5px;
                padding: 8px 15px;
                font-weight: bold;
                min-height: 35px;
            }
            QPushButton#confirm_btn {
                background-color: #27ae60;
                color: white;
            }
            QPushButton#confirm_btn:hover {
                background-color: #2ecc71;
                border: 2px solid #27ae60;
            }
            QPushButton#cancel_btn {
                background-color: #e74c3c;
                color: white;
            }
            QPushButton#cancel_btn:hover {
                background-color: #c0392b;
                border: 2px solid #e74c3c;
            }
        """)

        # إنشاء تخطيط النافذة
        layout = QVBoxLayout(confirm_dialog)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # إضافة أيقونة وعنوان
        header_layout = QHBoxLayout()

        # محاولة إضافة أيقونة البرنامج
        icon_label = QLabel()
        icon_label.setAlignment(Qt.AlignCenter)
        try:
            program_icon = QPixmap("01.ico")
            if not program_icon.isNull():
                program_icon = program_icon.scaled(48, 48, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                icon_label.setPixmap(program_icon)
                header_layout.addWidget(icon_label)
        except Exception as e:
            print(f"خطأ في تحميل أيقونة البرنامج للعنوان: {e}")

        # إضافة عنوان النافذة
        title_label = QLabel("تأكيد تسجيل التأخر")
        title_label.setFont(QFont("Calibri", 16, QFont.Bold))
        title_label.setStyleSheet("color: #0066cc;")
        title_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(title_label, 1)

        layout.addLayout(header_layout)

        # إضافة رسالة التأكيد
        message_browser = QTextBrowser()
        message_browser.setReadOnly(True)

        # تنسيق النص بخط Calibri 13 أسود غامق وعرض المعلومات في جدول
        html_content = """
        <div dir='rtl' style='text-align: right;'>
            <p style='font-family: Calibri; font-size: 13pt; color: black; font-weight: bold;'>
                سيتم تسجيل التأخر للتلاميذ التالية أسماؤهم:
            </p>

            <table style='width: 100%; border-collapse: collapse; margin-top: 10px; font-family: Calibri; font-size: 13pt; color: black; font-weight: bold;'>
                <tr style='background-color: #0066cc; color: white;'>
                    <th style='border: 1.5px solid #0055bb; padding: 8px; text-align: center;'>ر.ت</th>
                    <th style='border: 1.5px solid #0055bb; padding: 8px; text-align: center;'>الرمز</th>
                    <th style='border: 1.5px solid #0055bb; padding: 8px; text-align: right;'>الاسم والنسب</th>
                </tr>
        """

        # إضافة معلومات التلاميذ في جدول
        for student in selected_students:
            html_content += f"""
                <tr style='border-bottom: 1px solid #dddddd;'>
                    <td style='border: 1.5px solid #0066cc; padding: 8px; text-align: center;'>{student.get('rt', '')}</td>
                    <td style='border: 1.5px solid #0066cc; padding: 8px; text-align: center;'>{student['code']}</td>
                    <td style='border: 1.5px solid #0066cc; padding: 8px; text-align: right;'>{student['name']}</td>
                </tr>
            """

        html_content += """
            </table>
        </div>
        """

        message_browser.setHtml(html_content)
        layout.addWidget(message_browser)

        # إضافة أزرار التأكيد والإلغاء
        buttons_layout = QHBoxLayout()

        confirm_btn = QPushButton("تأكيد")
        confirm_btn.setObjectName("confirm_btn")
        confirm_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        confirm_btn.setCursor(Qt.PointingHandCursor)
        confirm_btn.setFixedWidth(105)
        confirm_btn.clicked.connect(confirm_dialog.accept)

        cancel_btn = QPushButton("إلغاء")
        cancel_btn.setObjectName("cancel_btn")
        cancel_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        cancel_btn.setCursor(Qt.PointingHandCursor)
        cancel_btn.setFixedWidth(105)
        cancel_btn.clicked.connect(confirm_dialog.reject)

        buttons_layout.addWidget(confirm_btn)
        buttons_layout.addWidget(cancel_btn)

        layout.addLayout(buttons_layout)

        return confirm_dialog

    @staticmethod
    def show_custom_success_message(parent, message, title="نجاح"):
        """عرض رسالة نجاح مخصصة"""
        success_dialog = QDialog(parent)
        success_dialog.setWindowTitle(title)
        success_dialog.setFixedSize(450, 250)
        success_dialog.setLayoutDirection(Qt.RightToLeft)

        # إضافة أيقونة البرنامج
        try:
            app_icon = QIcon("01.ico")
            success_dialog.setWindowIcon(app_icon)
        except Exception as e:
            print(f"خطأ في تحميل أيقونة البرنامج: {e}")

        # تنسيق النافذة
        success_dialog.setStyleSheet("""
            QDialog {
                background-color: #f0fff0;
                border: 2px solid #2ecc71;
                border-radius: 10px;
            }
            QLabel {
                color: #333333;
                font-weight: bold;
            }
            QLabel#icon_label {
                padding: 10px;
            }
            QLabel#message_label {
                background-color: #eafaf1;
                border: 1px solid #2ecc71;
                border-radius: 5px;
                padding: 15px;
                font-size: 14pt;
            }
            QPushButton {
                background-color: #2ecc71;
                color: white;
                border-radius: 5px;
                padding: 8px 15px;
                font-weight: bold;
                min-height: 35px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #27ae60;
                border: 2px solid #2ecc71;
            }
        """)

        # إنشاء تخطيط النافذة
        layout = QVBoxLayout(success_dialog)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # إضافة أيقونة وعنوان
        header_layout = QHBoxLayout()

        # محاولة إضافة أيقونة النجاح
        icon_label = QLabel()
        icon_label.setObjectName("icon_label")
        icon_label.setAlignment(Qt.AlignCenter)
        try:
            # يمكن استخدام أيقونة النجاح المخصصة إذا كانت متاحة
            success_icon = QPixmap("success.png")  # استبدل بالمسار الصحيح إذا كان متاحاً
            if success_icon.isNull():
                success_icon = QStyle.standardPixmap(QStyle.SP_MessageBoxInformation, QStyle.StyleOptionButton(), parent)
            success_icon = success_icon.scaled(48, 48, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            icon_label.setPixmap(success_icon)
        except Exception:
            # استخدام أيقونة قياسية في حالة الفشل
            icon_label.setText("✓")
            icon_label.setFont(QFont("Arial", 24))
            icon_label.setStyleSheet("color: #2ecc71;")

        header_layout.addWidget(icon_label)

        # إضافة عنوان النافذة
        title_label = QLabel(title)
        title_label.setFont(QFont("Calibri", 16, QFont.Bold))
        title_label.setStyleSheet("color: #27ae60;")
        title_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(title_label, 1)

        layout.addLayout(header_layout)

        # إضافة رسالة النجاح
        message_label = QLabel(message)
        message_label.setObjectName("message_label")
        message_label.setFont(QFont("Calibri", 13))
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setWordWrap(True)
        layout.addWidget(message_label)

        # إضافة زر الموافقة
        button_layout = QHBoxLayout()
        ok_button = QPushButton("موافق")
        ok_button.setFont(QFont("Calibri", 12, QFont.Bold))
        ok_button.setCursor(Qt.PointingHandCursor)
        ok_button.clicked.connect(success_dialog.accept)
        button_layout.addWidget(ok_button)

        layout.addLayout(button_layout)

        # عرض النافذة
        success_dialog.exec_()
        return True

    @staticmethod
    def create_absence_justification_print_dialog(parent, justification_data):
        """إنشاء نافذة تأكيد طباعة تبرير الغياب"""
        confirm_dialog = QDialog(parent)
        confirm_dialog.setWindowTitle("تأكيد طباعة تبرير الغياب")
        confirm_dialog.setFixedSize(500, 400)
        confirm_dialog.setLayoutDirection(Qt.RightToLeft)

        # إضافة أيقونة البرنامج
        try:
            app_icon = QIcon("01.ico")
            confirm_dialog.setWindowIcon(app_icon)
        except Exception as e:
            print(f"خطأ في تحميل أيقونة البرنامج: {e}")

        # تنسيق النافذة
        confirm_dialog.setStyleSheet("""
            QDialog {
                background-color: #f0f8ff;
                border: 2px solid #3498db;
                border-radius: 10px;
            }
            QLabel {
                color: #333333;
                font-weight: bold;
            }
            QTextBrowser {
                border: 1px solid #3498db;
                border-radius: 5px;
                padding: 10px;
                background-color: white;
            }
            QPushButton {
                border-radius: 5px;
                padding: 8px 15px;
                font-weight: bold;
                min-height: 35px;
            }
            QPushButton#print_btn {
                background-color: #3498db;
                color: white;
            }
            QPushButton#print_btn:hover {
                background-color: #2980b9;
                border: 2px solid #3498db;
            }
            QPushButton#cancel_btn {
                background-color: #95a5a6;
                color: white;
            }
            QPushButton#cancel_btn:hover {
                background-color: #7f8c8d;
                border: 2px solid #95a5a6;
            }
        """)

        # إنشاء تخطيط النافذة
        layout = QVBoxLayout(confirm_dialog)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # إضافة أيقونة وعنوان
        header_layout = QHBoxLayout()

        # محاولة إضافة أيقونة البرنامج
        icon_label = QLabel()
        icon_label.setAlignment(Qt.AlignCenter)
        try:
            program_icon = QPixmap("01.ico")
            if not program_icon.isNull():
                program_icon = program_icon.scaled(48, 48, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                icon_label.setPixmap(program_icon)
                header_layout.addWidget(icon_label)
        except Exception as e:
            print(f"خطأ في تحميل أيقونة البرنامج للعنوان: {e}")

        # إضافة عنوان النافذة
        title_label = QLabel("تأكيد طباعة تبرير الغياب")
        title_label.setFont(QFont("Calibri", 16, QFont.Bold))
        title_label.setStyleSheet("color: #3498db;")
        title_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(title_label, 1)

        layout.addLayout(header_layout)

        # إضافة معاينة التبرير
        message_browser = QTextBrowser()
        message_browser.setReadOnly(True)

        # تنسيق النص بخط Calibri 13 أسود غامق وعرض المعلومات في جدول
        student_name = justification_data.get('student_name', 'غير محدد')
        student_code = justification_data.get('student_code', 'غير محدد')
        section = justification_data.get('section', 'غير محدد')
        justification_date = justification_data.get('justification_date', 'غير محدد')
        start_date = justification_data.get('start_date', 'غير محدد')
        end_date = justification_data.get('end_date', 'غير محدد')
        days_count = justification_data.get('days_count', 'غير محدد')
        reason = justification_data.get('reason', 'غير محدد')
        notes = justification_data.get('notes', '')

        html_content = f"""
        <div dir='rtl' style='text-align: right;'>
            <p style='font-family: Calibri; font-size: 13pt; color: black; font-weight: bold;'>
                سيتم طباعة تبرير الغياب للتلميذ(ة):
            </p>
            <p style='font-family: Calibri; font-size: 14pt; color: #3498db; font-weight: bold; text-align: center;'>
                {student_name} - {section}
            </p>

            <table style='width: 100%; border-collapse: collapse; margin-top: 10px; font-family: Calibri; font-size: 13pt; color: black; font-weight: bold;'>
                <tr>
                    <td style='border: 1.5px solid #3498db; padding: 8px; text-align: right; background-color: #eaf2f8;'>رمز التلميذ:</td>
                    <td style='border: 1.5px solid #3498db; padding: 8px; text-align: right;'>{student_code}</td>
                </tr>
                <tr>
                    <td style='border: 1.5px solid #3498db; padding: 8px; text-align: right; background-color: #eaf2f8;'>تاريخ التبرير:</td>
                    <td style='border: 1.5px solid #3498db; padding: 8px; text-align: right;'>{justification_date}</td>
                </tr>
                <tr>
                    <td style='border: 1.5px solid #3498db; padding: 8px; text-align: right; background-color: #eaf2f8;'>فترة الغياب:</td>
                    <td style='border: 1.5px solid #3498db; padding: 8px; text-align: right;'>من {start_date} إلى {end_date} ({days_count} يوم)</td>
                </tr>
                <tr>
                    <td style='border: 1.5px solid #3498db; padding: 8px; text-align: right; background-color: #eaf2f8;'>سبب الغياب:</td>
                    <td style='border: 1.5px solid #3498db; padding: 8px; text-align: right;'>{reason}</td>
                </tr>
        """

        if notes:
            html_content += f"""
                <tr>
                    <td style='border: 1.5px solid #3498db; padding: 8px; text-align: right; background-color: #eaf2f8;'>ملاحظات:</td>
                    <td style='border: 1.5px solid #3498db; padding: 8px; text-align: right;'>{notes}</td>
                </tr>
            """

        html_content += """
            </table>
        </div>
        """

        message_browser.setHtml(html_content)
        layout.addWidget(message_browser)

        # إضافة أزرار الطباعة والإلغاء
        buttons_layout = QHBoxLayout()

        print_btn = QPushButton("طباعة التبرير")
        print_btn.setObjectName("print_btn")
        print_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        print_btn.setCursor(Qt.PointingHandCursor)
        print_btn.setFixedWidth(150)
        print_btn.clicked.connect(confirm_dialog.accept)

        cancel_btn = QPushButton("إلغاء")
        cancel_btn.setObjectName("cancel_btn")
        cancel_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        cancel_btn.setCursor(Qt.PointingHandCursor)
        cancel_btn.setFixedWidth(150)
        cancel_btn.clicked.connect(confirm_dialog.reject)

        buttons_layout.addWidget(print_btn)
        buttons_layout.addWidget(cancel_btn)

        layout.addLayout(buttons_layout)

        return confirm_dialog

    @staticmethod
    def show_custom_warning_message(parent, message, title="تنبيه"):
        """عرض رسالة تحذير مخصصة"""
        warning_dialog = QDialog(parent)
        warning_dialog.setWindowTitle(title)
        warning_dialog.setFixedSize(450, 250)
        warning_dialog.setLayoutDirection(Qt.RightToLeft)

        # إضافة أيقونة البرنامج
        try:
            app_icon = QIcon("01.ico")
            warning_dialog.setWindowIcon(app_icon)
        except Exception as e:
            print(f"خطأ في تحميل أيقونة البرنامج: {e}")

        # تنسيق النافذة
        warning_dialog.setStyleSheet("""
            QDialog {
                background-color: #fffbf0;
                border: 2px solid #f39c12;
                border-radius: 10px;
            }
            QLabel {
                color: #333333;
                font-weight: bold;
            }
            QLabel#icon_label {
                padding: 10px;
            }
            QLabel#message_label {
                background-color: #fef9e7;
                border: 1px solid #f39c12;
                border-radius: 5px;
                padding: 15px;
                font-size: 14pt;
            }
            QPushButton {
                background-color: #f39c12;
                color: white;
                border-radius: 5px;
                padding: 8px 15px;
                font-weight: bold;
                min-height: 35px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #e67e22;
                border: 2px solid #f39c12;
            }
        """)

        # إنشاء تخطيط النافذة
        layout = QVBoxLayout(warning_dialog)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # إضافة أيقونة وعنوان
        header_layout = QHBoxLayout()

        # محاولة إضافة أيقونة التحذير
        icon_label = QLabel()
        icon_label.setObjectName("icon_label")
        icon_label.setAlignment(Qt.AlignCenter)
        try:
            warning_icon = QPixmap("warning.png")  # استبدل بالمسار الصحيح إذا كان متاحاً
            if warning_icon.isNull():
                warning_icon = QStyle.standardPixmap(QStyle.SP_MessageBoxWarning, QStyle.StyleOptionButton(), parent)
            warning_icon = warning_icon.scaled(48, 48, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            icon_label.setPixmap(warning_icon)
        except Exception:
            # استخدام نص بديل في حالة الفشل
            icon_label.setText("⚠")
            icon_label.setFont(QFont("Arial", 24))
            icon_label.setStyleSheet("color: #f39c12;")

        header_layout.addWidget(icon_label)

        # إضافة عنوان النافذة
        title_label = QLabel(title)
        title_label.setFont(QFont("Calibri", 16, QFont.Bold))
        title_label.setStyleSheet("color: #e67e22;")
        title_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(title_label, 1)

        layout.addLayout(header_layout)

        # إضافة رسالة التحذير
        message_label = QLabel(message)
        message_label.setObjectName("message_label")
        message_label.setFont(QFont("Calibri", 13))
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setWordWrap(True)
        layout.addWidget(message_label)

        # إضافة زر الموافقة
        button_layout = QHBoxLayout()
        ok_button = QPushButton("موافق")
        ok_button.setFont(QFont("Calibri", 12, QFont.Bold))
        ok_button.setCursor(Qt.PointingHandCursor)
        ok_button.clicked.connect(warning_dialog.accept)
        button_layout.addWidget(ok_button)

        layout.addLayout(button_layout)

        # عرض النافذة
        warning_dialog.exec_()
        return True

    @staticmethod
    def show_custom_error_message(parent, message, title="خطأ"):
        """عرض رسالة خطأ مخصصة"""
        error_dialog = QDialog(parent)
        error_dialog.setWindowTitle(title)
        error_dialog.setFixedSize(450, 250)
        error_dialog.setLayoutDirection(Qt.RightToLeft)

        # إضافة أيقونة البرنامج
        try:
            app_icon = QIcon("01.ico")
            error_dialog.setWindowIcon(app_icon)
        except Exception as e:
            print(f"خطأ في تحميل أيقونة البرنامج: {e}")

        # تنسيق النافذة
        error_dialog.setStyleSheet("""
            QDialog {
                background-color: #fff0f0;
                border: 2px solid #e74c3c;
                border-radius: 10px;
            }
            QLabel {
                color: #333333;
                font-weight: bold;
            }
            QLabel#icon_label {
                padding: 10px;
            }
            QLabel#message_label {
                background-color: #fdedec;
                border: 1px solid #e74c3c;
                border-radius: 5px;
                padding: 15px;
                font-size: 14pt;
            }
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border-radius: 5px;
                padding: 8px 15px;
                font-weight: bold;
                min-height: 35px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #c0392b;
                border: 2px solid #e74c3c;
            }
        """)

        # إنشاء تخطيط النافذة
        layout = QVBoxLayout(error_dialog)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # إضافة أيقونة وعنوان
        header_layout = QHBoxLayout()

        # محاولة إضافة أيقونة الخطأ
        icon_label = QLabel()
        icon_label.setObjectName("icon_label")
        icon_label.setAlignment(Qt.AlignCenter)
        try:
            error_icon = QPixmap("error.png")  # استبدل بالمسار الصحيح إذا كان متاحاً
            if error_icon.isNull():
                error_icon = QStyle.standardPixmap(QStyle.SP_MessageBoxCritical, QStyle.StyleOptionButton(), parent)
            error_icon = error_icon.scaled(48, 48, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            icon_label.setPixmap(error_icon)
        except Exception:
            # استخدام نص بديل في حالة الفشل
            icon_label.setText("✖")
            icon_label.setFont(QFont("Arial", 24))
            icon_label.setStyleSheet("color: #e74c3c;")

        header_layout.addWidget(icon_label)

        # إضافة عنوان النافذة
        title_label = QLabel(title)
        title_label.setFont(QFont("Calibri", 16, QFont.Bold))
        title_label.setStyleSheet("color: #c0392b;")
        title_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(title_label, 1)

        layout.addLayout(header_layout)

        # إضافة رسالة الخطأ
        message_label = QLabel(message)
        message_label.setObjectName("message_label")
        message_label.setFont(QFont("Calibri", 13))
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setWordWrap(True)
        layout.addWidget(message_label)

        # إضافة زر الموافقة
        button_layout = QHBoxLayout()
        ok_button = QPushButton("موافق")
        ok_button.setFont(QFont("Calibri", 12, QFont.Bold))
        ok_button.setCursor(Qt.PointingHandCursor)
        ok_button.clicked.connect(error_dialog.accept)
        button_layout.addWidget(ok_button)

        layout.addLayout(button_layout)

        # عرض النافذة
        error_dialog.exec_()
        return True

    @staticmethod
    def show_custom_confirmation_dialog(parent, message, title="تأكيد"):
        """عرض نافذة تأكيد مخصصة مع خيارات نعم/لا"""
        confirm_dialog = QDialog(parent)
        confirm_dialog.setWindowTitle(title)
        confirm_dialog.setFixedSize(500, 280)
        confirm_dialog.setLayoutDirection(Qt.RightToLeft)

        # إضافة أيقونة البرنامج
        try:
            app_icon = QIcon("01.ico")
            confirm_dialog.setWindowIcon(app_icon)
        except Exception as e:
            print(f"خطأ في تحميل أيقونة البرنامج: {e}")

        # تنسيق النافذة
        confirm_dialog.setStyleSheet("""
            QDialog {
                background-color: #f0f8ff;
                border: 2px solid #3498db;
                border-radius: 10px;
            }
            QLabel {
                color: #333333;
                font-weight: bold;
            }
            QLabel#icon_label {
                padding: 10px;
            }
            QLabel#message_label {
                background-color: #ebf5fb;
                border: 1px solid #3498db;
                border-radius: 5px;
                padding: 15px;
                font-size: 14pt;
            }
            QPushButton {
                border-radius: 5px;
                padding: 8px 15px;
                font-weight: bold;
                min-height: 35px;
                min-width: 120px;
            }
            QPushButton#yes_btn {
                background-color: #3498db;
                color: white;
            }
            QPushButton#yes_btn:hover {
                background-color: #2980b9;
                border: 2px solid #3498db;
            }
            QPushButton#no_btn {
                background-color: #95a5a6;
                color: white;
            }
            QPushButton#no_btn:hover {
                background-color: #7f8c8d;
                border: 2px solid #95a5a6;
            }
        """)

        # إنشاء تخطيط النافذة
        layout = QVBoxLayout(confirm_dialog)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # إضافة أيقونة وعنوان
        header_layout = QHBoxLayout()

        # محاولة إضافة أيقونة التأكيد
        icon_label = QLabel()
        icon_label.setObjectName("icon_label")
        icon_label.setAlignment(Qt.AlignCenter)
        try:
            confirm_icon = QPixmap("confirm.png")  # استبدل بالمسار الصحيح إذا كان متاحاً
            if confirm_icon.isNull():
                confirm_icon = QStyle.standardPixmap(QStyle.SP_MessageBoxQuestion, QStyle.StyleOptionButton(), parent)
            confirm_icon = confirm_icon.scaled(48, 48, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            icon_label.setPixmap(confirm_icon)
        except Exception:
            # استخدام نص بديل في حالة الفشل
            icon_label.setText("?")
            icon_label.setFont(QFont("Arial", 24))
            icon_label.setStyleSheet("color: #3498db;")

        header_layout.addWidget(icon_label)

        # إضافة عنوان النافذة
        title_label = QLabel(title)
        title_label.setFont(QFont("Calibri", 16, QFont.Bold))
        title_label.setStyleSheet("color: #2980b9;")
        title_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(title_label, 1)

        layout.addLayout(header_layout)

        # إضافة رسالة التأكيد
        message_label = QLabel(message)
        message_label.setObjectName("message_label")
        message_label.setFont(QFont("Calibri", 13))
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setWordWrap(True)
        layout.addWidget(message_label)

        # إضافة أزرار نعم/لا
        button_layout = QHBoxLayout()

        yes_button = QPushButton("نعم")
        yes_button.setObjectName("yes_btn")
        yes_button.setFont(QFont("Calibri", 12, QFont.Bold))
        yes_button.setCursor(Qt.PointingHandCursor)
        yes_button.clicked.connect(confirm_dialog.accept)

        no_button = QPushButton("لا")
        no_button.setObjectName("no_btn")
        no_button.setFont(QFont("Calibri", 12, QFont.Bold))
        no_button.setCursor(Qt.PointingHandCursor)
        no_button.clicked.connect(confirm_dialog.reject)

        button_layout.addWidget(yes_button)
        button_layout.addWidget(no_button)

        layout.addLayout(button_layout)

        # عرض النافذة وإرجاع النتيجة
        result = confirm_dialog.exec_() == QDialog.Accepted
        return result

    @staticmethod
    def show_pdf_success_dialog(parent, filepath, title="تم إنشاء التقرير بنجاح"):
        """عرض نافذة نجاح مخصصة لتقارير PDF مع خيار فتح الملف"""
        success_dialog = QDialog(parent)
        success_dialog.setWindowTitle(title)
        success_dialog.setFixedSize(550, 300)
        success_dialog.setLayoutDirection(Qt.RightToLeft)

        # إضافة أيقونة البرنامج
        try:
            app_icon = QIcon("01.ico")
            success_dialog.setWindowIcon(app_icon)
        except Exception as e:
            print(f"خطأ في تحميل أيقونة البرنامج: {e}")

        # تنسيق النافذة
        success_dialog.setStyleSheet("""
            QDialog {
                background-color: #f0fff0;
                border: 2px solid #2ecc71;
                border-radius: 10px;
            }
            QLabel {
                color: #333333;
                font-weight: bold;
            }
            QLabel#icon_label {
                padding: 10px;
            }
            QLabel#message_label {
                background-color: #eafaf1;
                border: 1px solid #2ecc71;
                border-radius: 5px;
                padding: 15px;
                font-size: 13pt;
            }
            QLabel#path_label {
                background-color: #f2f3f4;
                border: 1px solid #bdc3c7;
                border-radius: 3px;
                padding: 8px;
                font-family: 'Courier New';
                font-size: 11pt;
            }
            QPushButton {
                border-radius: 5px;
                padding: 8px 15px;
                font-weight: bold;
                min-height: 35px;
                min-width: 150px;
            }
            QPushButton#open_btn {
                background-color: #2ecc71;
                color: white;
            }
            QPushButton#open_btn:hover {
                background-color: #27ae60;
                border: 2px solid #2ecc71;
            }
            QPushButton#close_btn {
                background-color: #95a5a6;
                color: white;
            }
            QPushButton#close_btn:hover {
                background-color: #7f8c8d;
                border: 2px solid #95a5a6;
            }
        """)

        # إنشاء تخطيط النافذة
        layout = QVBoxLayout(success_dialog)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # إضافة أيقونة وعنوان
        header_layout = QHBoxLayout()

        # محاولة إضافة أيقونة PDF
        icon_label = QLabel()
        icon_label.setObjectName("icon_label")
        icon_label.setAlignment(Qt.AlignCenter)
        try:
            # محاولة تحميل أيقونة PDF
            pdf_icon = QPixmap("pdf.png")  # استبدل بالمسار الصحيح إذا كان متاحاً
            if pdf_icon.isNull():
                # استخدام أيقونة قياسية في حالة عدم توفر أيقونة PDF
                pdf_icon = QStyle.standardPixmap(QStyle.SP_FileIcon, QStyle.StyleOptionButton(), parent)
            pdf_icon = pdf_icon.scaled(48, 48, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            icon_label.setPixmap(pdf_icon)
        except Exception:
            # استخدام نص بديل في حالة الفشل
            icon_label.setText("PDF")
            icon_label.setFont(QFont("Arial", 18, QFont.Bold))
            icon_label.setStyleSheet("color: #e74c3c;")

        header_layout.addWidget(icon_label)

        # إضافة عنوان النافذة
        title_label = QLabel(title)
        title_label.setFont(QFont("Calibri", 16, QFont.Bold))
        title_label.setStyleSheet("color: #27ae60;")
        title_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(title_label, 1)

        layout.addLayout(header_layout)

        # إضافة رسالة النجاح
        message_label = QLabel("تم إنشاء التقرير بنجاح وحفظه في المسار التالي:")
        message_label.setObjectName("message_label")
        message_label.setFont(QFont("Calibri", 13))
        message_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(message_label)

        # إضافة مسار الملف
        path_label = QLabel(filepath)
        path_label.setObjectName("path_label")
        path_label.setAlignment(Qt.AlignCenter)
        path_label.setTextInteractionFlags(Qt.TextSelectableByMouse)
        path_label.setCursor(Qt.IBeamCursor)
        layout.addWidget(path_label)

        # إضافة سؤال فتح الملف
        question_label = QLabel("هل ترغب في فتح التقرير الآن؟")
        question_label.setFont(QFont("Calibri", 13, QFont.Bold))
        question_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(question_label)

        # إضافة أزرار فتح/إغلاق
        button_layout = QHBoxLayout()

        open_button = QPushButton("فتح التقرير")
        open_button.setObjectName("open_btn")
        open_button.setFont(QFont("Calibri", 12, QFont.Bold))
        open_button.setCursor(Qt.PointingHandCursor)
        open_button.clicked.connect(success_dialog.accept)

        close_button = QPushButton("إغلاق")
        close_button.setObjectName("close_btn")
        close_button.setFont(QFont("Calibri", 12, QFont.Bold))
        close_button.setCursor(Qt.PointingHandCursor)
        close_button.clicked.connect(success_dialog.reject)

        button_layout.addWidget(open_button)
        button_layout.addWidget(close_button)

        layout.addLayout(button_layout)

        # عرض النافذة وإرجاع النتيجة (True إذا اختار المستخدم فتح الملف)
        result = success_dialog.exec_() == QDialog.Accepted
        return result

    @staticmethod
    def create_teacher_selection_confirmation_dialog(parent, class_name):
        """إنشاء نافذة تأكيد اختيار الأساتذة"""
        confirm_dialog = QDialog(parent)
        confirm_dialog.setWindowTitle("تأكيد اختيار الأساتذة")
        confirm_dialog.setFixedSize(500, 250)
        confirm_dialog.setLayoutDirection(Qt.RightToLeft)

        # إضافة أيقونة البرنامج
        try:
            app_icon = QIcon("01.ico")
            confirm_dialog.setWindowIcon(app_icon)
        except Exception as e:
            print(f"خطأ في تحميل أيقونة البرنامج: {e}")

        # تنسيق النافذة
        confirm_dialog.setStyleSheet("""
            QDialog {
                background-color: #f0f8ff;
                border: 2px solid #3498db;
                border-radius: 10px;
            }
            QLabel {
                color: #333333;
                font-weight: bold;
            }
            QPushButton {
                border-radius: 5px;
                padding: 8px 15px;
                font-weight: bold;
                min-height: 35px;
                min-width: 120px;
            }
            QPushButton#yes_btn {
                background-color: #3498db;
                color: white;
            }
            QPushButton#yes_btn:hover {
                background-color: #2980b9;
                border: 2px solid #3498db;
            }
            QPushButton#no_btn {
                background-color: #95a5a6;
                color: white;
            }
            QPushButton#no_btn:hover {
                background-color: #7f8c8d;
                border: 2px solid #95a5a6;
            }
        """)

        # إنشاء تخطيط النافذة
        layout = QVBoxLayout(confirm_dialog)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # إضافة عنوان
        title_label = QLabel(f"اختيار أساتذة القسم: {class_name}")
        title_label.setFont(QFont("Calibri", 16, QFont.Bold))
        title_label.setStyleSheet("color: #2980b9;")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # إضافة رسالة التأكيد
        message_label = QLabel("هل ترغب في اختيار أساتذة محددين لإشعارهم بالشهادات الطبية؟")
        message_label.setFont(QFont("Calibri", 13))
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setWordWrap(True)
        layout.addWidget(message_label)

        # إضافة أزرار نعم/لا
        button_layout = QHBoxLayout()

        yes_button = QPushButton("نعم")
        yes_button.setObjectName("yes_btn")
        yes_button.setFont(QFont("Calibri", 12, QFont.Bold))
        yes_button.setCursor(Qt.PointingHandCursor)
        yes_button.clicked.connect(confirm_dialog.accept)

        no_button = QPushButton("لا")
        no_button.setObjectName("no_btn")
        no_button.setFont(QFont("Calibri", 12, QFont.Bold))
        no_button.setCursor(Qt.PointingHandCursor)
        no_button.clicked.connect(confirm_dialog.reject)

        button_layout.addWidget(yes_button)
        button_layout.addWidget(no_button)

        layout.addLayout(button_layout)

        return confirm_dialog

    @staticmethod
    def create_tree_teacher_selection_dialog(parent, class_name, db_path, teachers_data):
        """إنشاء نافذة اختيار الأساتذة باستخدام التنقل بين المواد"""
        from PyQt5.QtGui import QBrush, QColor
        from PyQt5.QtWidgets import QListWidget, QListWidgetItem

        dialog = QDialog(parent)
        dialog.setWindowTitle(f"اختيار أساتذة القسم: {class_name}")
        dialog.setMinimumSize(600, 450)
        dialog.setLayoutDirection(Qt.RightToLeft)

        # إضافة أيقونة البرنامج
        try:
            app_icon = QIcon("01.ico")
            dialog.setWindowIcon(app_icon)
        except Exception as e:
            print(f"خطأ في تحميل أيقونة البرنامج: {e}")

        # تنسيق النافذة
        dialog.setStyleSheet("""
            QDialog {
                background-color: #f0f8ff;
                border: 2px solid #3498db;
                border-radius: 10px;
            }
            QLabel {
                color: #333333;
                font-weight: bold;
            }
            QListWidget {
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                padding: 5px;
                background-color: white;
                font-family: 'Calibri';
                font-size: 13pt;
                font-weight: bold;
                color: black;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
            }
            QListWidget::item:hover {
                background-color: #eaf2f8;
            }
            QListWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QPushButton {
                border-radius: 5px;
                padding: 8px 15px;
                font-weight: bold;
                min-height: 35px;
                min-width: 120px;
            }
            QPushButton#next_btn, QPushButton#prev_btn {
                background-color: #2ecc71;
                color: white;
            }
            QPushButton#next_btn:hover, QPushButton#prev_btn:hover {
                background-color: #27ae60;
                border: 2px solid #2ecc71;
            }
            QPushButton#ok_btn {
                background-color: #3498db;
                color: white;
            }
            QPushButton#ok_btn:hover {
                background-color: #2980b9;
                border: 2px solid #3498db;
            }
            QPushButton#cancel_btn {
                background-color: #95a5a6;
                color: white;
            }
            QPushButton#cancel_btn:hover {
                background-color: #7f8c8d;
                border: 2px solid #95a5a6;
            }
        """)

        # إنشاء تخطيط النافذة
        layout = QVBoxLayout(dialog)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # إضافة عنوان
        title_label = QLabel(f"اختيار أساتذة القسم: {class_name}")
        title_label.setFont(QFont("Calibri", 16, QFont.Bold))
        title_label.setStyleSheet("color: #2980b9;")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # تنظيم الأساتذة حسب المادة
        subjects_list = []
        subjects_data = {}
        for teacher in teachers_data:
            subject = teacher.get('subject', 'غير محدد')
            if subject not in subjects_data:
                subjects_list.append(subject)
                subjects_data[subject] = {
                    "teachers": [],
                    "selected_teacher": None
                }
            subjects_data[subject]["teachers"].append(teacher)

        # إضافة عنوان المادة الحالية
        current_subject_index = 0
        current_subject_label = QLabel()
        current_subject_label.setFont(QFont("Calibri", 14, QFont.Bold))
        current_subject_label.setStyleSheet("color: #27ae60;")
        current_subject_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(current_subject_label)

        # إضافة تعليمات
        instructions_label = QLabel("اختر أستاذ واحد من القائمة أدناه:")
        instructions_label.setFont(QFont("Calibri", 13, QFont.Bold))
        layout.addWidget(instructions_label)

        # إنشاء قائمة الأساتذة
        teachers_list = QListWidget()
        teachers_list.setFont(QFont("Calibri", 13, QFont.Bold))
        teachers_list.setCursor(Qt.PointingHandCursor)
        layout.addWidget(teachers_list)

        # إضافة معلومات التقدم
        progress_label = QLabel()
        progress_label.setFont(QFont("Calibri", 12))
        progress_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(progress_label)

        # دالة لتحديث قائمة الأساتذة بناءً على المادة الحالية
        def update_teachers_list():
            teachers_list.clear()
            if current_subject_index < len(subjects_list):
                current_subject = subjects_list[current_subject_index]
                current_subject_label.setText(f"المادة: {current_subject}")

                # إضافة الأساتذة إلى القائمة
                for teacher in subjects_data[current_subject]["teachers"]:
                    teacher_name = teacher.get('teacher_name', '')
                    item = QListWidgetItem(teacher_name)
                    item.setData(Qt.UserRole, {
                        'teacher_name': teacher_name,
                        'subject': current_subject
                    })
                    teachers_list.addItem(item)

                # تحديد الأستاذ المختار مسبقاً إن وجد
                selected_teacher = subjects_data[current_subject]["selected_teacher"]
                if selected_teacher:
                    for i in range(teachers_list.count()):
                        item = teachers_list.item(i)
                        if item.text() == selected_teacher:
                            teachers_list.setCurrentItem(item)
                            break

                # تحديث معلومات التقدم
                progress_label.setText(f"المادة {current_subject_index + 1} من {len(subjects_list)}")

        # دالة للانتقال إلى المادة التالية
        def next_subject():
            nonlocal current_subject_index
            if current_subject_index < len(subjects_list) - 1:
                current_subject_index += 1
                update_teachers_list()
                prev_button.setEnabled(True)
                if current_subject_index == len(subjects_list) - 1:
                    next_button.setEnabled(False)

        # دالة للانتقال إلى المادة السابقة
        def prev_subject():
            nonlocal current_subject_index
            if current_subject_index > 0:
                current_subject_index -= 1
                update_teachers_list()
                next_button.setEnabled(True)
                if current_subject_index == 0:
                    prev_button.setEnabled(False)

        # دالة لمعالجة اختيار الأستاذ
        def on_teacher_selected():
            if teachers_list.currentItem():
                current_subject = subjects_list[current_subject_index]
                teacher_name = teachers_list.currentItem().text()
                subjects_data[current_subject]["selected_teacher"] = teacher_name

        # ربط حدث تغيير الاختيار
        teachers_list.itemClicked.connect(on_teacher_selected)

        # إضافة أزرار التنقل
        navigation_layout = QHBoxLayout()

        prev_button = QPushButton("السابق")
        prev_button.setObjectName("prev_btn")
        prev_button.setFont(QFont("Calibri", 12, QFont.Bold))
        prev_button.setCursor(Qt.PointingHandCursor)
        prev_button.clicked.connect(prev_subject)
        prev_button.setEnabled(False)  # تعطيل في البداية لأننا في المادة الأولى

        next_button = QPushButton("التالي")
        next_button.setObjectName("next_btn")
        next_button.setFont(QFont("Calibri", 12, QFont.Bold))
        next_button.setCursor(Qt.PointingHandCursor)
        next_button.clicked.connect(next_subject)
        next_button.setEnabled(len(subjects_list) > 1)  # تعطيل إذا كان هناك مادة واحدة فقط

        navigation_layout.addWidget(prev_button)
        navigation_layout.addWidget(next_button)

        layout.addLayout(navigation_layout)

        # إضافة أزرار موافق/إلغاء
        button_layout = QHBoxLayout()

        ok_button = QPushButton("موافق")
        ok_button.setObjectName("ok_btn")
        ok_button.setFont(QFont("Calibri", 12, QFont.Bold))
        ok_button.setCursor(Qt.PointingHandCursor)
        ok_button.clicked.connect(dialog.accept)

        cancel_button = QPushButton("إلغاء")
        cancel_button.setObjectName("cancel_btn")
        cancel_button.setFont(QFont("Calibri", 12, QFont.Bold))
        cancel_button.setCursor(Qt.PointingHandCursor)
        cancel_button.clicked.connect(dialog.reject)

        button_layout.addWidget(ok_button)
        button_layout.addWidget(cancel_button)

        layout.addLayout(button_layout)

        # تحديث قائمة الأساتذة للمادة الأولى
        update_teachers_list()

        # إضافة دالة للحصول على الأساتذة المحددين
        def get_selected_teachers():
            selected_teachers = []
            for subject, data in subjects_data.items():
                if data["selected_teacher"]:
                    for teacher in data["teachers"]:
                        if teacher.get('teacher_name') == data["selected_teacher"]:
                            selected_teachers.append({
                                'teacher_name': data["selected_teacher"],
                                'subject': subject
                            })
                            break
            return selected_teachers

        dialog.get_selected_teachers = get_selected_teachers

        return dialog

    @staticmethod
    def create_medical_certificate_notification_dialog(parent, notification_data):
        """إنشاء نافذة تأكيد إشعار الشهادات الطبية"""
        confirm_dialog = QDialog(parent)
        confirm_dialog.setWindowTitle("تأكيد إشعار الشهادات الطبية")
        confirm_dialog.setFixedSize(600, 450)
        confirm_dialog.setLayoutDirection(Qt.RightToLeft)

        # إضافة أيقونة البرنامج
        try:
            app_icon = QIcon("01.ico")
            confirm_dialog.setWindowIcon(app_icon)
        except Exception as e:
            print(f"خطأ في تحميل أيقونة البرنامج: {e}")

        # تنسيق النافذة
        confirm_dialog.setStyleSheet("""
            QDialog {
                background-color: #f0f8ff;
                border: 2px solid #f39c12;
                border-radius: 10px;
            }
            QLabel {
                color: #333333;
                font-weight: bold;
            }
            QTextBrowser {
                border: 1px solid #f39c12;
                border-radius: 5px;
                padding: 10px;
                background-color: white;
            }
            QPushButton {
                border-radius: 5px;
                padding: 8px 15px;
                font-weight: bold;
                min-height: 35px;
            }
            QPushButton#print_btn {
                background-color: #f39c12;
                color: white;
            }
            QPushButton#print_btn:hover {
                background-color: #e67e22;
                border: 2px solid #f39c12;
            }
            QPushButton#cancel_btn {
                background-color: #95a5a6;
                color: white;
            }
            QPushButton#cancel_btn:hover {
                background-color: #7f8c8d;
                border: 2px solid #95a5a6;
            }
        """)

        # إنشاء تخطيط النافذة
        layout = QVBoxLayout(confirm_dialog)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # إضافة أيقونة وعنوان
        header_layout = QHBoxLayout()

        # محاولة إضافة أيقونة البرنامج
        icon_label = QLabel()
        icon_label.setAlignment(Qt.AlignCenter)
        try:
            program_icon = QPixmap("01.ico")
            if not program_icon.isNull():
                program_icon = program_icon.scaled(48, 48, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                icon_label.setPixmap(program_icon)
                header_layout.addWidget(icon_label)
        except Exception as e:
            print(f"خطأ في تحميل أيقونة البرنامج للعنوان: {e}")

        # إضافة عنوان النافذة
        title_label = QLabel("تأكيد إشعار الشهادات الطبية")
        title_label.setFont(QFont("Calibri", 16, QFont.Bold))
        title_label.setStyleSheet("color: #e67e22;")
        title_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(title_label, 1)

        layout.addLayout(header_layout)

        # إضافة معاينة الإشعار
        message_browser = QTextBrowser()
        message_browser.setReadOnly(True)

        # تنسيق النص بخط Calibri 13 أسود غامق وعرض المعلومات في جدول
        student_name = notification_data.get('student_name', 'غير محدد')
        student_code = notification_data.get('student_code', 'غير محدد')
        class_name = notification_data.get('class_name', 'غير محدد')
        certificates = notification_data.get('certificates', [])
        teachers = notification_data.get('teachers', [])

        html_content = f"""
        <div dir='rtl' style='text-align: right;'>
            <p style='font-family: Calibri; font-size: 13pt; color: black; font-weight: bold;'>
                سيتم إنشاء إشعار بالشهادات الطبية للتلميذ(ة):
            </p>
            <p style='font-family: Calibri; font-size: 14pt; color: #e67e22; font-weight: bold; text-align: center;'>
                {student_name} - {class_name}
            </p>

            <p style='font-family: Calibri; font-size: 13pt; color: black; font-weight: bold;'>
                الشهادات الطبية:
            </p>
            <table style='width: 100%; border-collapse: collapse; margin-top: 10px; font-family: Calibri; font-size: 13pt; color: black; font-weight: bold;'>
                <tr style='background-color: #f39c12; color: white;'>
                    <th style='border: 1.5px solid #e67e22; padding: 8px; text-align: center;'>الرقم</th>
                    <th style='border: 1.5px solid #e67e22; padding: 8px; text-align: center;'>تاريخ البداية</th>
                    <th style='border: 1.5px solid #e67e22; padding: 8px; text-align: center;'>تاريخ النهاية</th>
                    <th style='border: 1.5px solid #e67e22; padding: 8px; text-align: center;'>المدة</th>
                </tr>
        """

        for cert in certificates:
            html_content += f"""
                <tr style='border-bottom: 1px solid #dddddd;'>
                    <td style='border: 1.5px solid #f39c12; padding: 8px; text-align: center;'>{cert.get('number', '')}</td>
                    <td style='border: 1.5px solid #f39c12; padding: 8px; text-align: center;'>{cert.get('start_date', '')}</td>
                    <td style='border: 1.5px solid #f39c12; padding: 8px; text-align: center;'>{cert.get('end_date', '')}</td>
                    <td style='border: 1.5px solid #f39c12; padding: 8px; text-align: center;'>{cert.get('duration', '')} يوم</td>
                </tr>
            """

        html_content += """
            </table>
        """

        if teachers:
            html_content += """
                <p style='font-family: Calibri; font-size: 13pt; color: black; font-weight: bold; margin-top: 15px;'>
                    الأساتذة المحددون للإشعار:
                </p>
                <table style='width: 100%; border-collapse: collapse; margin-top: 10px; font-family: Calibri; font-size: 13pt; color: black; font-weight: bold;'>
                    <tr style='background-color: #f39c12; color: white;'>
                        <th style='border: 1.5px solid #e67e22; padding: 8px; text-align: center;'>الرقم</th>
                        <th style='border: 1.5px solid #e67e22; padding: 8px; text-align: right;'>اسم الأستاذ</th>
                        <th style='border: 1.5px solid #e67e22; padding: 8px; text-align: right;'>المادة</th>
                    </tr>
            """

            for i, teacher in enumerate(teachers):
                html_content += f"""
                    <tr style='border-bottom: 1px solid #dddddd;'>
                        <td style='border: 1.5px solid #f39c12; padding: 8px; text-align: center;'>{i+1}</td>
                        <td style='border: 1.5px solid #f39c12; padding: 8px; text-align: right;'>{teacher.get('teacher_name', '')}</td>
                        <td style='border: 1.5px solid #f39c12; padding: 8px; text-align: right;'>{teacher.get('subject', '')}</td>
                    </tr>
                """

            html_content += """
                </table>
            """
        else:
            html_content += """
                <p style='font-family: Calibri; font-size: 13pt; color: #e67e22; font-weight: bold; margin-top: 15px; text-align: center;'>
                    لم يتم تحديد أي أستاذ للإشعار.
                </p>
            """

        html_content += """
        </div>
        """

        message_browser.setHtml(html_content)
        layout.addWidget(message_browser)

        # إضافة أزرار الطباعة والإلغاء
        buttons_layout = QHBoxLayout()

        print_btn = QPushButton("طباعة الإشعار")
        print_btn.setObjectName("print_btn")
        print_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        print_btn.setCursor(Qt.PointingHandCursor)
        print_btn.setFixedWidth(150)
        print_btn.clicked.connect(confirm_dialog.accept)

        cancel_btn = QPushButton("إلغاء")
        cancel_btn.setObjectName("cancel_btn")
        cancel_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        cancel_btn.setCursor(Qt.PointingHandCursor)
        cancel_btn.setFixedWidth(150)
        cancel_btn.clicked.connect(confirm_dialog.reject)

        buttons_layout.addWidget(print_btn)
        buttons_layout.addWidget(cancel_btn)

        layout.addLayout(buttons_layout)

        return confirm_dialog


if __name__ == "__main__":
    # يمكن إضافة اختبارات هنا إذا لزم الأمر
    pass