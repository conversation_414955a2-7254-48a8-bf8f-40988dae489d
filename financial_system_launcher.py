# ================= مشغل النظام المالي =================
# ملف تجميعي لتشغيل جميع نوافذ النظام المالي للمؤسسة التعليمية
# يحتوي على: قائمة رئيسية لاختيار النافذة المطلوبة

import sys
import os
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

# استيراد النوافذ المالية
try:
    from expense_management_window import ExpenseManagementWindow
    from budget_planning_window import BudgetPlanningWindow
    from cash_flow_window import CashFlowWindow
except ImportError as e:
    print(f"خطأ في استيراد النوافذ المالية: {e}")

class FinancialSystemLauncher(QMainWindow):
    """مشغل النظام المالي الرئيسي"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
        self.windows = {}  # لحفظ النوافذ المفتوحة
    
    def center_window(self, window):
        """توسيط النافذة في وسط الشاشة"""
        try:
            screen = QApplication.desktop().screenGeometry()
            window_size = window.geometry()
            x = (screen.width() - window_size.width()) // 2
            y = (screen.height() - window_size.height()) // 2
            window.move(x, y)
        except Exception as e:
            print(f"خطأ في توسيط النافذة: {str(e)}")
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("🏦 النظام المالي للمؤسسة التعليمية")
        self.setGeometry(100, 100, 800, 600)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # توسيط النافذة
        self.center_window(self)
        
        # الويدجت الرئيسي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(30, 30, 30, 30)
        
        # العنوان الرئيسي
        title_label = QLabel("🏦 النظام المالي للمؤسسة التعليمية")
        title_label.setFont(QFont("Calibri", 24, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #3498db, stop:1 #2980b9);
                color: white;
                border-radius: 15px;
                padding: 20px;
                margin-bottom: 20px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # وصف النظام
        description_label = QLabel("""
        مرحباً بك في النظام المالي الشامل للمؤسسة التعليمية
        
        يوفر هذا النظام جميع الأدوات اللازمة لإدارة الشؤون المالية:
        • مسك المصاريف وتصنيفها
        • إعداد الموازنة السنوية ومتابعتها
        • تقارير الأرباح والخسائر (P&L)
        • تتبع التدفقات النقدية (Cash Flow)
        """)
        description_label.setFont(QFont("Calibri", 12))
        description_label.setAlignment(Qt.AlignCenter)
        description_label.setStyleSheet("""
            QLabel {
                color: #34495e;
                background-color: #ecf0f1;
                border: 2px solid #bdc3c7;
                border-radius: 10px;
                padding: 15px;
                line-height: 1.5;
            }
        """)
        main_layout.addWidget(description_label)
        
        # شبكة الأزرار
        buttons_grid = QGridLayout()
        buttons_grid.setSpacing(15)
        
        # إنشاء الأزرار
        self.create_financial_buttons(buttons_grid)
        
        main_layout.addLayout(buttons_grid)
        
        # مساحة فارغة
        main_layout.addStretch()
        
        # شريط الحالة
        self.statusBar().showMessage("اختر النافذة المطلوبة من الأزرار أعلاه")
        self.statusBar().setFont(QFont("Calibri", 10))
    
    def create_financial_buttons(self, grid_layout):
        """إنشاء أزرار النوافذ المالية"""
        
        # زر مسك المصاريف
        expense_btn = self.create_main_button(
            "💰 مسك المصاريف",
            "إدخال وإدارة جميع مصاريف المؤسسة\nتصنيف المصاريف وتتبعها",
            "#e74c3c",
            self.open_expense_management
        )
        grid_layout.addWidget(expense_btn, 0, 0)
        
        # زر إعداد الموازنة
        budget_btn = self.create_main_button(
            "📊 إعداد الموازنة السنوية",
            "تخطيط الموازنة السنوية\nمقارنة المتوقع مع الفعلي",
            "#9b59b6",
            self.open_budget_planning
        )
        grid_layout.addWidget(budget_btn, 0, 1)

        # زر التدفقات النقدية
        cash_flow_btn = self.create_main_button(
            "💸 التدفقات النقدية",
            "تتبع التدفقات الداخلة والخارجة\nتحليل السيولة النقدية",
            "#16a085",
            self.open_cash_flow
        )
        grid_layout.addWidget(cash_flow_btn, 1, 0)
    
    def create_main_button(self, title, description, color, callback):
        """إنشاء زر رئيسي منسق"""
        button = QPushButton()
        button.setFixedSize(350, 120)
        button.setFont(QFont("Calibri", 12, QFont.Bold))
        
        # تنسيق الزر
        button.setStyleSheet(f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 15px;
                text-align: center;
                padding: 15px;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(color)};
                transform: scale(1.02);
            }}
            QPushButton:pressed {{
                background-color: {self.darken_color(color, 0.8)};
            }}
        """)
        
        # تعيين النص
        button.setText(f"{title}\n\n{description}")
        
        # ربط الإجراء
        button.clicked.connect(callback)
        
        return button
    
    def darken_color(self, hex_color, factor=0.9):
        """تغميق اللون"""
        hex_color = hex_color.lstrip('#')
        rgb = tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
        darkened = tuple(int(c * factor) for c in rgb)
        return f"#{darkened[0]:02x}{darkened[1]:02x}{darkened[2]:02x}"
    
    def open_expense_management(self):
        """فتح نافذة مسك المصاريف"""
        try:
            if 'expense' not in self.windows or not self.windows['expense'].isVisible():
                self.windows['expense'] = ExpenseManagementWindow()
                self.windows['expense'].show()
            else:
                self.windows['expense'].raise_()
                self.windows['expense'].activateWindow()
            
            self.statusBar().showMessage("تم فتح نافذة مسك المصاريف")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة مسك المصاريف: {str(e)}")
    
    def open_budget_planning(self):
        """فتح نافذة إعداد الموازنة السنوية"""
        try:
            if 'budget' not in self.windows or not self.windows['budget'].isVisible():
                self.windows['budget'] = BudgetPlanningWindow()
                self.windows['budget'].show()
            else:
                self.windows['budget'].raise_()
                self.windows['budget'].activateWindow()
            
            self.statusBar().showMessage("تم فتح نافذة إعداد الموازنة السنوية")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة إعداد الموازنة: {str(e)}")
    

    
    def open_cash_flow(self):
        """فتح نافذة التدفقات النقدية"""
        try:
            if 'cash_flow' not in self.windows or not self.windows['cash_flow'].isVisible():
                self.windows['cash_flow'] = CashFlowWindow()
                self.windows['cash_flow'].show()
            else:
                self.windows['cash_flow'].raise_()
                self.windows['cash_flow'].activateWindow()
            
            self.statusBar().showMessage("تم فتح نافذة التدفقات النقدية")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة التدفقات النقدية: {str(e)}")
    
    def closeEvent(self, event):
        """إغلاق جميع النوافذ عند إغلاق النافذة الرئيسية"""
        try:
            for window_name, window in self.windows.items():
                if window and window.isVisible():
                    window.close()
        except Exception as e:
            print(f"خطأ في إغلاق النوافذ: {str(e)}")
        
        event.accept()

def main():
    """الدالة الرئيسية لتشغيل النظام المالي"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # تعيين أيقونة التطبيق (اختياري)
    app.setApplicationName("النظام المالي للمؤسسة التعليمية")
    app.setApplicationVersion("1.0")
    
    # إنشاء وعرض النافذة الرئيسية
    launcher = FinancialSystemLauncher()
    launcher.show()
    
    # تشغيل التطبيق
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
