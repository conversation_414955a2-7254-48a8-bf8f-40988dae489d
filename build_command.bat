@echo off
echo ========================================
echo تحزيم تطبيق إدارة المدرسة
echo ========================================

echo.
echo 1. تشغيل سكريبت إصلاح المسارات...
python fix_db_paths.py

echo.
echo 2. بدء عملية التحزيم...
pyinstaller --noconsole --add-data "db_path.py;." --add-data "school_database.db;." --hidden-import jaraco.text main_window.py

echo.
echo ========================================
echo انتهى التحزيم!
echo ========================================
echo.
echo التطبيق موجود في: dist\main_window\
echo الملفات:
echo   - main_window.exe
echo   - _internal\ (يحتوي على school_database.db)
echo   - ملفات أخرى ضرورية
echo.
pause
