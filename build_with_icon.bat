@echo off
chcp 65001 > nul
echo ========================================
echo   تحزيم البرنامج مع إصلاح الأيقونة
echo ========================================
echo.

echo [1/6] التحقق من ملف الأيقونة...
if exist "01.ico" (
    echo ✅ تم العثور على ملف الأيقونة
) else (
    echo ❌ ملف الأيقونة 01.ico غير موجود
    echo يرجى التأكد من وجود الملف في مجلد المشروع
    pause
    exit /b 1
)

echo.
echo [2/6] التحقق من Python و PyInstaller...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت
    pause
    exit /b 1
)
echo ✅ Python موجود

python -c "import PyInstaller" 2>nul
if errorlevel 1 (
    echo ❌ PyInstaller غير مثبت. جاري التثبيت...
    pip install pyinstaller
)
echo ✅ PyInstaller موجود

echo.
echo [3/6] تثبيت المكتبات المطلوبة...
pip install --upgrade setuptools
pip install jaraco.text more-itertools importlib-metadata zipp
echo ✅ تم تثبيت المكتبات

echo.
echo [4/6] تنظيف الملفات القديمة...
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"
echo ✅ تم التنظيف

echo.
echo [5/6] بدء التحزيم مع إصلاح الأيقونة...
echo جاري التحزيم باستخدام ملف spec محسن للأيقونة...
pyinstaller build_with_icon.spec --clean --noconfirm

if errorlevel 1 (
    echo ❌ فشل التحزيم مع الملف المحسن
    echo محاولة التحزيم مع الملف البديل...
    pyinstaller simple_build.spec --clean --noconfirm
    
    if errorlevel 1 (
        echo ❌ فشل التحزيم نهائياً
        pause
        exit /b 1
    )
)

echo.
echo [6/6] التحقق من النتائج وإصلاح الأيقونة...

REM البحث عن الملف التنفيذي
set "exe_path="
if exist "dist\نظام_إدارة_المدرسة\نظام_إدارة_المدرسة.exe" (
    set "exe_path=dist\نظام_إدارة_المدرسة\نظام_إدارة_المدرسة.exe"
    set "dist_folder=dist\نظام_إدارة_المدرسة"
) else if exist "dist\school_system\school_system.exe" (
    set "exe_path=dist\school_system\school_system.exe"
    set "dist_folder=dist\school_system"
)

if defined exe_path (
    echo ✅ تم التحزيم بنجاح!
    echo 📁 المجلد: %dist_folder%
    echo 🚀 الملف التنفيذي: %exe_path%
    
    echo.
    echo جاري التأكد من وجود ملف الأيقونة في مجلد البرنامج...
    copy "01.ico" "%dist_folder%\" >nul 2>&1
    if exist "%dist_folder%\01.ico" (
        echo ✅ تم نسخ ملف الأيقونة إلى مجلد البرنامج
    ) else (
        echo ⚠️ تحذير: لم يتم نسخ ملف الأيقونة
    )
    
    echo.
    echo إنشاء ملف تشغيل سريع...
    echo @echo off > "تشغيل_نظام_إدارة_المدرسة.bat"
    echo cd "%dist_folder%" >> "تشغيل_نظام_إدارة_المدرسة.bat"
    if exist "dist\نظام_إدارة_المدرسة\نظام_إدارة_المدرسة.exe" (
        echo start "نظام_إدارة_المدرسة.exe" >> "تشغيل_نظام_إدارة_المدرسة.bat"
    ) else (
        echo start school_system.exe >> "تشغيل_نظام_إدارة_المدرسة.bat"
    )
    echo ✅ تم إنشاء ملف التشغيل السريع
    
    echo.
    echo ملاحظات مهمة:
    echo - الأيقونة ستظهر في شريط المهام وعنوان النافذة
    echo - قاعدة البيانات ستُنشأ في مجلد البرنامج
    echo - لا يظهر موجه الأوامر عند التشغيل
    
    echo.
    echo هل تريد فتح مجلد البرنامج؟ (y/n)
    set /p choice=
    if /i "%choice%"=="y" (
        explorer "%dist_folder%"
    )
) else (
    echo ❌ لم يتم العثور على الملف التنفيذي
    echo تحقق من مجلد dist
)

echo.
echo ========================================
echo           انتهت العملية
echo ========================================
pause
