# -*- mode: python ; coding: utf-8 -*-
# ملف spec محسن خصيصاً لضمان ظهور الأيقونة

import os

block_cipher = None

# التأكد من وجود ملف الأيقونة
icon_file = '01.ico'
if not os.path.exists(icon_file):
    print(f"تحذير: ملف الأيقونة {icon_file} غير موجود")
    icon_file = None

a = Analysis(
    ['main_window.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('01.ico', '.'),  # الأيقونة في المجلد الرئيسي
        ('01.ico', '_internal'),  # نسخة في _internal
        ('fonts/', 'fonts/'),
        ('logs/', 'logs/'),
        ('reports/', 'reports/'),
    ],
    hiddenimports=[
        # الوحدات الأساسية
        'PyQt5.QtWidgets',
        'PyQt5.QtGui', 
        'PyQt5.QtCore',
        'sqlite3',
        'logging',
        'sys',
        'os',
        # حل مشكلة jaraco
        'pkg_resources',
        'pkg_resources.py2_warn',
        'jaraco.text',
        'jaraco.functools',
        'more_itertools',
        'importlib_metadata',
        'zipp',
        'setuptools',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'matplotlib',
        'numpy',
        'pandas',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='نظام_إدارة_المدرسة',  # اسم عربي واضح
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,  # إخفاء موجه الأوامر
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=icon_file,  # تطبيق الأيقونة على الملف التنفيذي
    version=None,
    uac_admin=False,
    uac_uiaccess=False,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='نظام_إدارة_المدرسة'
)
