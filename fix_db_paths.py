#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
سكريبت بسيط لإصلاح مسارات قاعدة البيانات في جميع الملفات
يستبدل جميع مراجع "school_database.db" بالدالة الموحدة
"""

import os
import re
import glob

def fix_file(file_path):
    """إصلاح ملف واحد"""
    try:
        print(f"🔧 معالجة الملف: {file_path}")
        
        # قراءة الملف
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # إضافة الاستيراد إذا لم يكن موجوداً
        if "from db_path import get_db_path" not in content:
            # البحث عن مكان مناسب للاستيراد
            lines = content.split('\n')
            
            # البحث عن آخر سطر استيراد
            import_index = 0
            for i, line in enumerate(lines):
                if line.strip().startswith(('import ', 'from ')) and not line.strip().startswith('#'):
                    import_index = i + 1
            
            # إدراج الاستيراد
            lines.insert(import_index, "from db_path import get_db_path")
            content = '\n'.join(lines)
        
        # استبدال مراجع قاعدة البيانات
        replacements = [
            # sqlite3.connect("data.db")
            (r'sqlite3\.connect\s*\(\s*["\']data\.db["\']\s*\)', 'sqlite3.connect(get_db_path())'),

            # sqlite3.connect(r"data.db")
            (r'sqlite3\.connect\s*\(\s*r["\']data\.db["\']\s*\)', 'sqlite3.connect(get_db_path())'),

            # sqlite3.connect(self.db_path) - تحديث المتغيرات
            (r'sqlite3\.connect\s*\(\s*self\.db_path\s*\)', 'sqlite3.connect(get_db_path())'),

            # self.db_path = "data.db"
            (r'self\.db_path\s*=\s*["\']data\.db["\']', 'self.db_path = get_db_path()'),

            # db_path = "data.db" في المعاملات
            (r'db_path\s*=\s*["\']data\.db["\']', 'db_path=get_db_path()'),

            # أي مرجع مباشر آخر لـ data.db
            (r'["\']data\.db["\']', 'get_db_path()'),
        ]
        
        changes_count = 0
        for pattern, replacement in replacements:
            matches = re.findall(pattern, content)
            if matches:
                content = re.sub(pattern, replacement, content)
                changes_count += len(matches)
                print(f"  ✅ تم استبدال {len(matches)} مرجع")
        
        # حفظ الملف إذا تم إجراء تغييرات
        if content != original_content:
            # إنشاء نسخة احتياطية
            backup_path = f"{file_path}.backup"
            with open(backup_path, 'w', encoding='utf-8') as f:
                f.write(original_content)
            
            # حفظ الملف المحدث
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"  ✅ تم تحديث الملف ({changes_count} تغيير)")
            print(f"  💾 نسخة احتياطية: {backup_path}")
            return True
        else:
            print(f"  ℹ️ لا توجد تغييرات مطلوبة")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في معالجة {file_path}: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء إصلاح مسارات قاعدة البيانات")
    print("=" * 50)
    
    # العثور على ملفات Python
    python_files = []
    for file in glob.glob("*.py"):
        if file not in ["fix_db_paths.py", "db_path.py"]:
            python_files.append(file)
    
    if not python_files:
        print("❌ لم يتم العثور على ملفات Python")
        return
    
    print(f"📁 تم العثور على {len(python_files)} ملف:")
    for file in python_files:
        print(f"  - {file}")
    
    print("\n" + "=" * 50)
    
    # معالجة كل ملف
    updated_count = 0
    for file_path in python_files:
        if fix_file(file_path):
            updated_count += 1
        print()  # سطر فارغ
    
    print("=" * 50)
    print(f"🎉 انتهى الإصلاح!")
    print(f"📊 تم تحديث {updated_count} من {len(python_files)} ملف")
    
    if updated_count > 0:
        print(f"\n💡 الخطوات التالية:")
        print(f"  1. اختبر البرنامج: python main_window.py")
        print(f"  2. إذا كان يعمل، احذف ملفات .backup")
        print(f"  3. أعد تحزيم البرنامج")
    
    print(f"\n✅ الآن جميع الملفات ستجد قاعدة البيانات في:")
    print(f"  - البيئة العادية: نفس مجلد الملفات")
    print(f"  - بعد التحزيم: نفس مجلد التطبيق")

if __name__ == "__main__":
    main()
