#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import sqlite3
import traceback
from datetime import datetime
import subprocess

# إعدادات التقرير - مطابقة تماماً لتقرير القسم الشهري
COL_WIDTHS_TABLE1 = [50, 50, 50, 40]  # جدول معلومات القسم
COL_WIDTHS_TABLE2 = [25, 25, 25, 25, 30, 40, 20, 10]  # جدول واجبات التسجيل (معكوس)
COL_WIDTHS_TABLE3 = [60, 60, 70]  # جدول المجاميع

TABLE1_HEADERS = ['القيمة', 'البيان', 'القيمة', 'البيان']
TABLE2_HEADERS = ['طريقة الدفع', 'تاريخ الدفع', 'نوع الدفع', 'المبلغ المدفوع', 'رمز التلميذ', 'اسم التلميذ', 'الرقم']
TABLE3_HEADERS = ['النسبة المئوية', 'المبلغ', 'البيان']

ROW_HEIGHT_TABLE1 = 8
ROW_HEIGHT_TABLE2 = 7
ROW_HEIGHT_HEADER = 10

PAGE_MARGIN_TOP = 0.2
PAGE_MARGIN_BOTTOM = 0.2
PAGE_MARGIN_LEFT = 10
PAGE_MARGIN_RIGHT = 10

try:
    from fpdf import FPDF
    import arabic_reshaper
    from bidi.algorithm import get_display
except ImportError:
    subprocess.check_call([sys.executable, "-m", "pip", "install", "fpdf2", "arabic-reshaper", "python-bidi"])
    from fpdf import FPDF
    import arabic_reshaper
    from bidi.algorithm import get_display
from db_path import get_db_path

class ArabicPDF(FPDF):
    def __init__(self):
        super().__init__('P','mm','A4')
        self.set_margins(PAGE_MARGIN_LEFT, PAGE_MARGIN_TOP, PAGE_MARGIN_RIGHT)
        self.set_auto_page_break(auto=True, margin=PAGE_MARGIN_BOTTOM)
        fonts_dir = os.path.join(os.path.dirname(__file__), 'fonts')
        
        # إضافة خطوط Calibri إذا كانت متوفرة
        calibri_path = os.path.join(fonts_dir, 'calibri.ttf')
        calibri_bold_path = os.path.join(fonts_dir, 'calibrib.ttf')
        
        if os.path.exists(calibri_path):
            self.add_font('Calibri', '', calibri_path)
            self.calibri_available = True
        else:
            self.calibri_available = False
            
        if os.path.exists(calibri_bold_path):
            self.add_font('Calibri', 'B', calibri_bold_path)
            self.calibri_bold_available = True
        else:
            self.calibri_bold_available = False
        
        # تعيين الخط الافتراضي
        if self.calibri_available:
            self.set_font('Calibri', '', 13)
        else:
            self.set_font('Arial', '', 13)
        self.set_line_width(0.4)

    def set_title_font(self, size=15):
        """تعيين خط العناوين"""
        if self.calibri_bold_available:
            self.set_font('Calibri', 'B', size)
        else:
            self.set_font('Arial', 'B', size)

    def set_detail_font(self, size=13):
        """تعيين خط التفاصيل"""
        if self.calibri_bold_available:
            self.set_font('Calibri', 'B', size)
        else:
            self.set_font('Arial', 'B', size)

    def set_normal_font(self, size=12):
        """تعيين الخط العادي"""
        if self.calibri_available:
            self.set_font('Calibri', '', size)
        else:
            self.set_font('Arial', '', size)

    def ar_text(self, txt: str) -> str:
        """تحويل النص العربي ليتم عرضه بشكل صحيح"""
        reshaped = arabic_reshaper.reshape(str(txt))
        return get_display(reshaped)

def get_section_info_from_db(db_path, section):
    """جلب معلومات القسم من جدول registration_fees والجداول الثابتة"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # جلب معلومات القسم من جدول registration_fees أولاً (البيانات الحديثة)
        cursor.execute("""
            SELECT DISTINCT
                COALESCE(rf.اسم_الاستاذ, mat.اسم_الاستاذ) as اسم_الاستاذ,
                COALESCE(rf.المادة, mat.المادة) as المادة,
                mat.المجموعة,
                mat.نسبة_الواجبات
            FROM registration_fees rf
            LEFT JOIN جدول_المواد_والاقسام mat ON COALESCE(rf.القسم, '') = mat.القسم
            WHERE COALESCE(rf.القسم, '') = ?
            ORDER BY المادة
            LIMIT 1
        """, (section,))

        section_subjects_from_fees = cursor.fetchall()

        # إذا لم توجد بيانات في registration_fees، استخدم جدول المواد والأقسام (البيانات الثابتة)
        if not section_subjects_from_fees:
            cursor.execute("""
                SELECT اسم_الاستاذ, المادة, المجموعة, نسبة_الواجبات
                FROM جدول_المواد_والاقسام
                WHERE القسم = ?
                ORDER BY المادة
            """, (section,))
            section_subjects = cursor.fetchall()
        else:
            section_subjects = section_subjects_from_fees

        # جلب إحصائيات التلاميذ من جدول registration_fees مباشرة
        print(f"🔍 جلب إحصائيات التلاميذ من جدول registration_fees لـ {section}")

        # جلب الإحصائيات من جدول registration_fees مع ربطها بجدول البيانات
        cursor.execute("""
            SELECT COUNT(DISTINCT rf.student_id) as total_students,
                   COUNT(DISTINCT CASE WHEN COALESCE(rf.النوع, jb.النوع) = 'ذكر' THEN rf.student_id END) as male_count,
                   COUNT(DISTINCT CASE WHEN COALESCE(rf.النوع, jb.النوع) = 'أنثى' THEN rf.student_id END) as female_count
            FROM registration_fees rf
            LEFT JOIN جدول_البيانات jb ON rf.student_id = jb.id
            WHERE COALESCE(rf.القسم, jb.القسم) = ?
        """, (section,))

        fees_stats = cursor.fetchone()

        if fees_stats and fees_stats[0] > 0:
            print(f"✅ تم جلب الإحصائيات من جدول registration_fees: {fees_stats[0]} طالب")
            student_stats = fees_stats
        else:
            print(f"⚠️ لا توجد إحصائيات في registration_fees، استخدام البيانات الحية")
            # الرجوع للبيانات الحية
            cursor.execute("""
                SELECT COUNT(*) as total_students,
                       COUNT(CASE WHEN النوع = 'ذكر' THEN 1 END) as male_count,
                       COUNT(CASE WHEN النوع = 'أنثى' THEN 1 END) as female_count
                FROM جدول_البيانات
                WHERE القسم = ?
            """, (section,))

            student_stats = cursor.fetchone()

        conn.close()

        return {
            'section_subjects': section_subjects,
            'student_stats': student_stats
        }

    except Exception as e:
        print(f"خطأ في جلب معلومات القسم: {str(e)}")
        return None

def get_registration_fees_by_section(db_path, section):
    """جلب واجبات التسجيل للقسم من جدول registration_fees مباشرة"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        print(f"🔍 جلب واجبات التسجيل من جدول registration_fees لـ {section}")

        # جلب البيانات مباشرة من جدول registration_fees
        cursor.execute("""
            SELECT
                jb.اسم_التلميذ,
                jb.رمز_التلميذ,
                rf.payment_type,
                rf.amount_paid,
                rf.payment_date,
                rf.payment_method,
                rf.notes,
                COALESCE(rf.اسم_الاستاذ, 'غير محدد') as اسم_الاستاذ
            FROM registration_fees rf
            JOIN جدول_البيانات jb ON rf.student_id = jb.id
            WHERE COALESCE(rf.القسم, jb.القسم) = ?
            ORDER BY jb.اسم_التلميذ
        """, (section,))

        registration_fees = cursor.fetchall()
        conn.close()

        print(f"✅ تم جلب {len(registration_fees)} سجل من جدول registration_fees")
        return registration_fees

    except Exception as e:
        print(f"خطأ في جلب واجبات التسجيل: {str(e)}")
        return []

def generate_registration_fees_report(logo_path, section_info, registration_fees, section, output_path):
    """إنشاء تقرير واجبات التسجيل - مطابق تماماً لتقرير القسم الشهري"""
    pdf = ArabicPDF()
    margin = 10
    usable_w = pdf.w - 2 * margin

    pdf.add_page()
    y = pdf.get_y()

    # إضافة الشعار إذا كان متوفراً
    if logo_path and os.path.exists(logo_path):
        logo_w, logo_h = 60, 30
        x_logo = (pdf.w - logo_w) / 2
        pdf.image(logo_path, x=x_logo, y=y, w=logo_w, h=logo_h)
        y += logo_h + 5

    # عنوان التقرير
    pdf.set_draw_color(0, 51, 102)
    pdf.set_line_width(0.5)
    FIXED_BOX_HEIGHT = 12

    title_text = f"تقرير واجبات التسجيل - القسم: {section}"

    pdf.set_text_color(0, 51, 102)
    pdf.set_xy(margin, y)
    pdf.set_title_font(15)
    pdf.cell(usable_w, FIXED_BOX_HEIGHT, pdf.ar_text(title_text), border=1, align='C')

    pdf.set_text_color(0, 0, 0)
    y += FIXED_BOX_HEIGHT + 5

    # الجدول الأول: معلومات القسم
    pdf.set_title_font(14)
    pdf.set_text_color(0, 100, 0)
    pdf.set_xy(margin, y)
    pdf.cell(usable_w, 8, pdf.ar_text('معلومات القسم والمواد'), border=0, align='C')
    pdf.set_text_color(0, 0, 0)
    y += 10

    cols1 = COL_WIDTHS_TABLE1

    # معلومات إحصائية عن القسم
    if section_info and section_info['student_stats']:
        stats = section_info['student_stats']

        # إضافة معلومات المواد
        if section_info['section_subjects']:
            first_subject = section_info['section_subjects'][0]
            teacher_name = first_subject[0] or 'غير محدد'
            subject_name = first_subject[1] or 'غير محدد'
            group_name = first_subject[2] or 'غير محدد'
            duties_percent = str(first_subject[3]) + '%' if first_subject[3] else '100%'

            # العمود الرابع والثاني في نفس الصفوف - بدون حصة الأستاذ
            section_info_rows = [
                ['', 'المادة', str(stats[0]), 'إجمالي التلاميذ'],
                ['', 'الأستاذ(ة)', str(stats[1]), 'عدد الذكور'],
                ['', 'واجبات التسجيل', str(stats[2]), 'عدد الإناث'],
                ['', 'المجموعة', group_name, 'المجموعة']
            ]

            # إضافة القيم في العمود الثالث للعمود الثاني
            section_info_rows[0][2] = str(stats[0])  # إجمالي التلاميذ
            section_info_rows[1][2] = str(stats[1])  # عدد الذكور
            section_info_rows[2][2] = str(stats[2])  # عدد الإناث
            section_info_rows[3][2] = group_name     # المجموعة

            # إضافة القيم في العمود الأول للعمود الثاني
            section_info_rows[0][0] = subject_name   # المادة
            section_info_rows[1][0] = teacher_name   # الأستاذ(ة)
            section_info_rows[2][0] = 'واجبات التسجيل'  # نوع التقرير
            section_info_rows[3][0] = group_name     # المجموعة

        else:
            # معلومات افتراضية - بدون حصة الأستاذ
            section_info_rows = [
                ['غير محدد', 'المادة', str(stats[0]), 'إجمالي التلاميذ'],
                ['غير محدد', 'الأستاذ(ة)', str(stats[1]), 'عدد الذكور'],
                ['واجبات التسجيل', 'نوع التقرير', str(stats[2]), 'عدد الإناث'],
                ['غير محدد', 'المجموعة', 'غير محدد', 'المجموعة']
            ]
    else:
        section_info_rows = [
            ['غير محدد', 'المادة', '0', 'إجمالي التلاميذ'],
            ['غير محدد', 'الأستاذ(ة)', '0', 'عدد الذكور'],
            ['واجبات التسجيل', 'نوع التقرير', '0', 'عدد الإناث'],
            ['غير محدد', 'المجموعة', 'غير محدد', 'المجموعة']
        ]

    pdf.set_detail_font(13)
    pdf.set_fill_color(230, 240, 255)

    # رسم صفوف معلومات القسم
    for row in section_info_rows:
        x = margin
        for i, cell in enumerate(row):
            pdf.set_xy(x, y)
            fill = i % 2 == 1
            align = 'C' if i % 2 == 1 else 'R'
            pdf.cell(cols1[i], ROW_HEIGHT_TABLE1, pdf.ar_text(cell), border=1, align=align, fill=fill)
            x += cols1[i]
        y += ROW_HEIGHT_TABLE1

    y += 5

    # الجدول الثاني: واجبات التسجيل
    pdf.set_title_font(14)
    pdf.set_text_color(128, 0, 128)
    pdf.set_xy(margin, y)
    pdf.cell(usable_w, 8, pdf.ar_text(f'واجبات التسجيل'), border=0, align='C')
    pdf.set_text_color(0, 0, 0)
    y += 10

    cols2 = COL_WIDTHS_TABLE2

    pdf.set_detail_font(13)
    pdf.set_fill_color(255, 200, 255)

    # رسم رأس الجدول الثاني
    x = margin
    for i, header in enumerate(TABLE2_HEADERS):
        pdf.set_xy(x, y)
        pdf.cell(cols2[i], ROW_HEIGHT_HEADER, pdf.ar_text(header), border=1, align='C', fill=True)
        x += cols2[i]

    y += ROW_HEIGHT_HEADER
    pdf.set_normal_font(11)

    # محتوى جدول واجبات التسجيل (مع ترتيب معكوس مطابق لتقرير القسم الشهري)
    if registration_fees:
        for i, fee in enumerate(registration_fees):
            x = margin
            # ترتيب البيانات معكوس: طريقة الدفع، تاريخ الدفع، نوع الدفع، المبلغ المدفوع، رمز التلميذ، اسم التلميذ، الرقم
            data = [
                fee[5] or 'نقداً',   # طريقة الدفع
                fee[4] or 'غير محدد',  # تاريخ الدفع
                fee[2] or 'غير محدد',  # نوع الدفع
                f'{float(fee[3]):.2f}' if fee[3] else '0.00',  # المبلغ المدفوع
                fee[1] or '',  # رمز التلميذ
                fee[0] or '',  # اسم التلميذ
                str(i + 1)      # الرقم الترتيبي
            ]

            # تلوين حسب نوع الدفع (مطابق لنظام الألوان في تقرير القسم الشهري)
            if fee[2] == "دفعة كاملة":
                pdf.set_fill_color(220, 255, 220)  # أخضر فاتح
            elif fee[2] == "دفعة مقسطة":
                pdf.set_fill_color(255, 255, 200)  # أصفر فاتح
            else:
                pdf.set_fill_color(255, 220, 220)  # أحمر فاتح

            for j, cell in enumerate(data):
                pdf.set_xy(x, y)
                pdf.cell(cols2[j], ROW_HEIGHT_TABLE2, pdf.ar_text(cell), border=1, align='C', fill=True)
                x += cols2[j]

            y += ROW_HEIGHT_TABLE2

            # الانتقال إلى صفحة جديدة عند الحاجة
            if y > pdf.h - 50:
                pdf.add_page()
                y = pdf.get_y()

                # إضافة الشعار في الصفحة الجديدة
                if logo_path and os.path.exists(logo_path):
                    logo_w, logo_h = 60, 30
                    x_logo = (pdf.w - logo_w) / 2
                    pdf.image(logo_path, x=x_logo, y=y, w=logo_w, h=logo_h)
                    y += logo_h + 5

                # إضافة عنوان التقرير في الصفحة الجديدة
                pdf.set_draw_color(0, 51, 102)
                pdf.set_line_width(0.5)
                FIXED_BOX_HEIGHT = 12

                title_text = f"تقرير واجبات التسجيل - القسم: {section}"

                pdf.set_text_color(0, 51, 102)
                pdf.set_xy(margin, y)
                pdf.set_title_font(15)
                pdf.cell(usable_w, FIXED_BOX_HEIGHT, pdf.ar_text(title_text), border=1, align='C')

                pdf.set_text_color(0, 0, 0)
                y += FIXED_BOX_HEIGHT + 5

                # إضافة عنوان الجدول الثاني في الصفحة الجديدة
                pdf.set_title_font(14)
                pdf.set_text_color(128, 0, 128)
                pdf.set_xy(margin, y)
                pdf.cell(usable_w, 8, pdf.ar_text(f'واجبات التسجيل'), border=0, align='C')
                pdf.set_text_color(0, 0, 0)
                y += 10

                # إعادة رسم رأس الجدول الثاني في الصفحة الجديدة
                pdf.set_detail_font(13)
                pdf.set_fill_color(255, 200, 255)

                x = margin
                for i, header in enumerate(TABLE2_HEADERS):
                    pdf.set_xy(x, y)
                    pdf.cell(cols2[i], ROW_HEIGHT_HEADER, pdf.ar_text(header), border=1, align='C', fill=True)
                    x += cols2[i]

                y += ROW_HEIGHT_HEADER
                pdf.set_normal_font(11)
    else:
        # إذا لم توجد واجبات تسجيل
        pdf.set_xy(margin, y)
        pdf.set_fill_color(255, 245, 245)
        pdf.cell(sum(cols2), ROW_HEIGHT_TABLE2, pdf.ar_text('لا توجد واجبات تسجيل مسجلة لهذا القسم'), border=1, align='C', fill=True)
        y += ROW_HEIGHT_TABLE2

    y += 10

    # الجدول الثالث: مجموع المبالغ وحصة الأستاذ
    pdf.set_title_font(14)
    pdf.set_text_color(0, 100, 100)
    pdf.set_xy(margin, y)
    pdf.cell(usable_w, 8, pdf.ar_text('مجموع المبالغ وحصة الأستاذ(ة)'), border=0, align='C')
    pdf.set_text_color(0, 0, 0)
    y += 10

    cols3 = COL_WIDTHS_TABLE3

    # حساب المجاميع
    total_paid = 0
    total_count = 0

    if registration_fees:
        for fee in registration_fees:
            total_paid += float(fee[3]) if fee[3] else 0
            total_count += 1

    # إزالة حساب حصة الأستاذ - لا يحصل على أي حصة من واجبات التسجيل

    pdf.set_detail_font(13)
    pdf.set_fill_color(200, 255, 200)

    # رسم رأس الجدول الثالث
    x = margin
    for i, header in enumerate(TABLE3_HEADERS):
        pdf.set_xy(x, y)
        pdf.cell(cols3[i], ROW_HEIGHT_HEADER, pdf.ar_text(header), border=1, align='C', fill=True)
        x += cols3[i]

    y += ROW_HEIGHT_HEADER
    pdf.set_normal_font(12)

    # محتوى جدول المجاميع - بدون حصة الأستاذ
    summary_data = [
        ['-', f'{total_paid:.2f}', 'إجمالي المبلغ المحصل'],
        [f'{total_count}', '-', 'عدد دفعات التسجيل']
    ]

    for i, row in enumerate(summary_data):
        x = margin

        # تلوين مختلف لكل صف
        if i == 0:  # المحصل
            pdf.set_fill_color(240, 255, 240)  # أخضر فاتح
        elif i == 1:  # عدد الدفعات
            pdf.set_fill_color(255, 255, 240)  # أصفر فاتح
        else:  # حصة الأستاذ
            pdf.set_fill_color(240, 240, 255)  # أزرق فاتح

        for j, cell in enumerate(row):
            pdf.set_xy(x, y)
            pdf.cell(cols3[j], ROW_HEIGHT_TABLE2, pdf.ar_text(cell), border=1, align='C', fill=True)
            x += cols3[j]

        y += ROW_HEIGHT_TABLE2

    # إضافة تاريخ الطباعة
    y += 10
    current_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    pdf.set_normal_font(10)
    pdf.set_xy(margin, y)
    pdf.cell(usable_w/2, 8, pdf.ar_text(f'تاريخ الطباعة: {current_date}'), border=0, align='R')

    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    pdf.output(output_path)
    print(f"تم إنشاء تقرير واجبات التسجيل: {output_path}")

def print_registration_fees_report(parent=None, section=None):
    """
    دالة لإنشاء تقرير واجبات التسجيل - مطابق تماماً لتقرير القسم الشهري

    المعاملات:
        parent: كائن النافذة الأم
        section: اسم القسم

    العوائد:
        (success, output_path, message): ثلاثية تحدد نجاح العملية ومسار الملف ورسالة النتيجة
    """
    try:
        if not section:
            return False, None, "اسم القسم غير محدد."

        # تحديد مسار قاعدة البيانات
        db_path = os.path.join(os.path.dirname(__file__), 'data.db')

        # جلب بيانات القسم وواجبات التسجيل
        try:
            # جلب شعار المؤسسة
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            cursor.execute("SELECT ImagePath1 FROM بيانات_المؤسسة LIMIT 1")
            logo_row = cursor.fetchone()
            logo_path = logo_row[0] if logo_row and os.path.exists(logo_row[0]) else None

            conn.close()

            # جلب معلومات القسم مع الإحصائيات من جدول registration_fees
            section_info = get_section_info_from_db(db_path, section)

            # جلب واجبات التسجيل من جدول registration_fees
            registration_fees = get_registration_fees_by_section(db_path, section)

        except Exception as db_error:
            print(f"خطأ في الوصول لقاعدة البيانات: {db_error}")
            return False, None, f"خطأ في الوصول لقاعدة البيانات: {str(db_error)}"

        # إنشاء مجلد التقارير إذا لم يكن موجوداً
        reports_dir = os.path.join(os.path.expanduser('~'), 'Desktop', 'تقارير واجبات التسجيل')
        if not os.path.exists(reports_dir):
            os.makedirs(reports_dir)

        # تحديد اسم الملف
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        section_clean = section.replace(' ', '_').replace('/', '_')
        output_path = os.path.join(reports_dir, f"تقرير_واجبات_التسجيل_{section_clean}_{timestamp}.pdf")

        # إنشاء التقرير
        generate_registration_fees_report(logo_path, section_info, registration_fees, section, output_path)

        # فتح الملف بعد إنشائه
        try:
            if sys.platform == 'win32':
                os.startfile(output_path)
            elif sys.platform == 'darwin':  # macOS
                subprocess.call(['open', output_path])
            else:  # Linux
                subprocess.call(['xdg-open', output_path])
        except Exception as e:
            return True, output_path, f"تم إنشاء التقرير ولكن تعذر فتح الملف: {str(e)}"

        return True, output_path, "تم إنشاء تقرير واجبات التسجيل بنجاح."

    except Exception as e:
        traceback.print_exc()
        return False, None, f"حدث خطأ في إنشاء تقرير واجبات التسجيل: {str(e)}"

if __name__=='__main__':
    # مثال على الاستخدام
    try:
        success, output_path, message = print_registration_fees_report(
            section="قسم / 01"
        )

        print(f"النتيجة: {success}")
        print(f"المسار: {output_path}")
        print(f"الرسالة: {message}")

    except Exception as e:
        print(f"خطأ في الاختبار: {e}")
        traceback.print_exc()
