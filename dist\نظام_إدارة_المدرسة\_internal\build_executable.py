#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
===============================================================================
                    ملف تحزيم البرنامج الشامل
===============================================================================
ملف لتحزيم برنامج "المعين في الحراسة العامة" مع جميع المكتبات والملفات
ليعمل على أي جهاز بكفاءة عالية وسرعة جيدة

المطور: نظام إدارة المؤسسات التعليمية
التاريخ: 2025
الإصدار: 1.0
===============================================================================
"""

import os
import sys
import shutil
import subprocess
import zipfile
from datetime import datetime
import json

class ProgramBuilder:
    """فئة تحزيم البرنامج"""
    
    def __init__(self):
        self.script_dir = os.path.dirname(os.path.abspath(__file__))
        self.build_dir = os.path.join(self.script_dir, "build")
        self.dist_dir = os.path.join(self.script_dir, "dist")
        self.output_dir = os.path.join(self.script_dir, "البرنامج_المحزم")
        
        # قائمة الملفات الأساسية للبرنامج
        self.essential_files = [
            "main_window.py",
            "sub01_window.py",
            "sub2_window.py", 
            "sub8_window.py",
            "sub252_window.py",
            "sub262_window.py",
            "sub232_window.py",
            "sub3_window.py",
            "sub100_window.py",
            "attendance_processing_window.py",
            "budget_planning_window.py",
            "expense_management_window.py",
            "cash_flow_window.py",
            "financial_system_launcher.py",
            "monthly_duties_window.py",
            "archived_accounts_manager.py",
            "check_database.py",
            "01.ico",
            "data.db"
        ]
        
        # ملفات الطباعة والتقارير
        self.print_files = [
            "print101.py",
            "print111.py", 
            "print144.py",
            "print_registration_fees.py",
            "print_registration_fees_monthly_style.py",
            "print_registration_fees_simple.py",
            "print_section_monthly.py",
            "print_section_yearly.py",
            "attendance_sheet_report.py",
            "daily_attendance_sheet_report.py"
        ]
        
        # المجلدات المطلوبة
        self.required_folders = [
            "fonts",
            "logs",
            "reports"
        ]
        
        # المكتبات المطلوبة
        self.required_packages = [
            "PyQt5>=5.15.0",
            "fpdf2>=2.7.0",
            "arabic-reshaper>=3.0.0",
            "python-bidi>=0.4.2",
            "openpyxl>=3.1.0",
            "matplotlib>=3.5.0",
            "numpy>=1.21.0",
            "Pillow>=9.0.0"
        ]
        
        # المكتبات الاختيارية (للرسوم البيانية)
        self.optional_packages = [
            "pandas>=1.3.0",
            "seaborn>=0.11.0"
        ]

    def print_header(self):
        """طباعة رأس البرنامج"""
        print("=" * 80)
        print("                    🚀 تحزيم برنامج المعين في الحراسة العامة")
        print("=" * 80)
        print(f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📁 مجلد البرنامج: {self.script_dir}")
        print("=" * 80)

    def check_python_version(self):
        """فحص إصدار Python"""
        print("\n🔍 فحص إصدار Python...")
        
        version = sys.version_info
        print(f"   إصدار Python: {version.major}.{version.minor}.{version.micro}")
        
        if version.major < 3 or (version.major == 3 and version.minor < 7):
            print("❌ خطأ: يتطلب Python 3.7 أو أحدث")
            return False
        
        print("✅ إصدار Python مناسب")
        return True

    def check_files_exist(self):
        """فحص وجود الملفات الأساسية"""
        print("\n📋 فحص وجود الملفات الأساسية...")
        
        missing_files = []
        
        # فحص الملفات الأساسية
        for file in self.essential_files:
            file_path = os.path.join(self.script_dir, file)
            if os.path.exists(file_path):
                print(f"   ✅ {file}")
            else:
                print(f"   ❌ {file} - مفقود")
                missing_files.append(file)
        
        # فحص ملفات الطباعة
        for file in self.print_files:
            file_path = os.path.join(self.script_dir, file)
            if os.path.exists(file_path):
                print(f"   ✅ {file}")
            else:
                print(f"   ⚠️ {file} - مفقود (اختياري)")
        
        # فحص المجلدات
        for folder in self.required_folders:
            folder_path = os.path.join(self.script_dir, folder)
            if os.path.exists(folder_path):
                print(f"   ✅ {folder}/")
            else:
                print(f"   ⚠️ {folder}/ - مفقود (سيتم إنشاؤه)")
                os.makedirs(folder_path, exist_ok=True)
        
        if missing_files:
            print(f"\n❌ ملفات مفقودة: {', '.join(missing_files)}")
            return False
        
        print("\n✅ جميع الملفات الأساسية موجودة")
        return True

    def install_requirements(self):
        """تثبيت المكتبات المطلوبة"""
        print("\n📦 تثبيت المكتبات المطلوبة...")
        
        # تثبيت PyInstaller
        print("   📥 تثبيت PyInstaller...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller>=5.0"])
            print("   ✅ تم تثبيت PyInstaller")
        except subprocess.CalledProcessError:
            print("   ❌ فشل في تثبيت PyInstaller")
            return False
        
        # تثبيت المكتبات الأساسية
        for package in self.required_packages:
            print(f"   📥 تثبيت {package}...")
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", package])
                print(f"   ✅ تم تثبيت {package}")
            except subprocess.CalledProcessError:
                print(f"   ❌ فشل في تثبيت {package}")
                return False
        
        # تثبيت المكتبات الاختيارية
        print("\n   📦 تثبيت المكتبات الاختيارية...")
        for package in self.optional_packages:
            print(f"   📥 محاولة تثبيت {package}...")
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", package])
                print(f"   ✅ تم تثبيت {package}")
            except subprocess.CalledProcessError:
                print(f"   ⚠️ تعذر تثبيت {package} (اختياري)")
        
        print("\n✅ تم تثبيت جميع المكتبات المطلوبة")
        return True

    def create_spec_file(self):
        """إنشاء ملف .spec لـ PyInstaller"""
        print("\n📝 إنشاء ملف التكوين...")
        
        spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# قائمة الملفات الإضافية
added_files = [
    ('01.ico', '.'),
    ('data.db', '.'),
    ('fonts', 'fonts'),
    ('logs', 'logs'),
    ('reports', 'reports'),
]

# إضافة ملفات الطباعة إذا كانت موجودة
import os
script_dir = r'{self.script_dir}'

print_files = {self.print_files}
for file in print_files:
    file_path = os.path.join(script_dir, file)
    if os.path.exists(file_path):
        added_files.append((file, '.'))

a = Analysis(
    ['main_window.py'],
    pathex=[r'{self.script_dir}'],
    binaries=[],
    datas=added_files,
    hiddenimports=[
        'PyQt5.QtCore',
        'PyQt5.QtGui', 
        'PyQt5.QtWidgets',
        'sqlite3',
        'fpdf',
        'arabic_reshaper',
        'bidi.algorithm',
        'openpyxl',
        'matplotlib',
        'numpy',
        'PIL',
        'pandas',
        'seaborn'
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='المعين_في_الحراسة_العامة',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='01.ico'
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='المعين_في_الحراسة_العامة'
)
'''
        
        spec_file = os.path.join(self.script_dir, "program.spec")
        with open(spec_file, 'w', encoding='utf-8') as f:
            f.write(spec_content)
        
        print(f"   ✅ تم إنشاء ملف التكوين: {spec_file}")
        return spec_file

    def build_executable(self, spec_file):
        """بناء الملف التنفيذي"""
        print("\n🔨 بناء الملف التنفيذي...")
        print("   ⏳ هذه العملية قد تستغرق عدة دقائق...")
        
        try:
            # تنظيف المجلدات السابقة
            if os.path.exists(self.build_dir):
                shutil.rmtree(self.build_dir)
            if os.path.exists(self.dist_dir):
                shutil.rmtree(self.dist_dir)
            
            # بناء البرنامج
            cmd = [sys.executable, "-m", "PyInstaller", "--clean", spec_file]
            result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
            
            if result.returncode == 0:
                print("   ✅ تم بناء الملف التنفيذي بنجاح")
                return True
            else:
                print("   ❌ فشل في بناء الملف التنفيذي")
                print(f"   خطأ: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"   ❌ خطأ في بناء الملف التنفيذي: {str(e)}")
            return False

    def create_portable_package(self):
        """إنشاء حزمة محمولة"""
        print("\n📦 إنشاء الحزمة المحمولة...")

        # مسار البرنامج المبني
        built_program_dir = os.path.join(self.dist_dir, "المعين_في_الحراسة_العامة")

        if not os.path.exists(built_program_dir):
            print("   ❌ لم يتم العثور على البرنامج المبني")
            return False

        # إنشاء مجلد الحزمة النهائية
        if os.path.exists(self.output_dir):
            shutil.rmtree(self.output_dir)
        os.makedirs(self.output_dir)

        # نسخ البرنامج المبني
        print("   📁 نسخ ملفات البرنامج...")
        shutil.copytree(built_program_dir, os.path.join(self.output_dir, "البرنامج"))

        # إنشاء ملفات إضافية
        self.create_readme_file()
        self.create_launcher_script()
        self.create_requirements_file()
        self.create_version_info()

        print("   ✅ تم إنشاء الحزمة المحمولة")
        return True

    def create_readme_file(self):
        """إنشاء ملف التعليمات"""
        readme_content = """
===============================================================================
                    برنامج المعين في الحراسة العامة
===============================================================================

🎯 وصف البرنامج:
نظام متكامل ذكي لتسيير الجوانب التربوية والمالية بمؤسسات الدعم التربوي
بكفاءة واحتراف

📋 المميزات الرئيسية:
• إدارة بيانات المؤسسة والطلاب
• نظام الحضور والغياب المتقدم
• إدارة الواجبات الشهرية ورسوم التسجيل
• النظام المالي الشامل (المصاريف، الموازنة، التدفقات النقدية)
• تقارير PDF احترافية
• نظام النسخ الاحتياطي التلقائي
• واجهة عربية سهلة الاستخدام

🚀 كيفية التشغيل:
1. افتح مجلد "البرنامج"
2. شغل ملف "المعين_في_الحراسة_العامة.exe"
3. أو استخدم ملف "تشغيل_البرنامج.bat" للتشغيل السريع

📁 محتويات الحزمة:
• البرنامج/ - ملفات البرنامج الرئيسية
• تشغيل_البرنامج.bat - ملف التشغيل السريع
• متطلبات_النظام.txt - متطلبات التشغيل
• معلومات_الإصدار.json - تفاصيل الإصدار
• اقرأني.txt - هذا الملف

⚙️ متطلبات النظام:
• نظام التشغيل: Windows 7/8/10/11
• الذاكرة: 2 GB RAM أو أكثر
• مساحة القرص: 500 MB مساحة فارغة
• دقة الشاشة: 1024x768 أو أعلى

🔧 استكشاف الأخطاء:
• إذا لم يعمل البرنامج، تأكد من وجود ملف "data.db"
• في حالة ظهور خطأ في الخطوط، تأكد من وجود مجلد "fonts"
• للحصول على الدعم، راجع ملف السجلات في مجلد "logs"

📞 الدعم الفني:
للحصول على الدعم الفني أو الإبلاغ عن مشاكل، يرجى التواصل مع
فريق التطوير

===============================================================================
تم تطوير هذا البرنامج بعناية فائقة لخدمة المؤسسات التعليمية
© 2025 - جميع الحقوق محفوظة
===============================================================================
"""

        readme_file = os.path.join(self.output_dir, "اقرأني.txt")
        with open(readme_file, 'w', encoding='utf-8') as f:
            f.write(readme_content)

        print("   ✅ تم إنشاء ملف التعليمات")

    def create_launcher_script(self):
        """إنشاء ملف تشغيل سريع"""
        launcher_content = '''@echo off
chcp 65001 > nul
title برنامج المعين في الحراسة العامة

echo ===============================================================================
echo                    🚀 برنامج المعين في الحراسة العامة
echo ===============================================================================
echo.
echo 📅 جاري تشغيل البرنامج...
echo.

cd /d "%~dp0البرنامج"

if exist "المعين_في_الحراسة_العامة.exe" (
    echo ✅ تم العثور على البرنامج
    echo 🚀 بدء التشغيل...
    echo.
    start "" "المعين_في_الحراسة_العامة.exe"
    echo ✅ تم تشغيل البرنامج بنجاح
) else (
    echo ❌ خطأ: لم يتم العثور على ملف البرنامج
    echo 📁 تأكد من وجود ملف "المعين_في_الحراسة_العامة.exe" في مجلد "البرنامج"
    echo.
    pause
)

echo.
echo 📝 ملاحظة: يمكنك إغلاق هذه النافذة بأمان
timeout /t 3 > nul
'''

        launcher_file = os.path.join(self.output_dir, "تشغيل_البرنامج.bat")
        with open(launcher_file, 'w', encoding='utf-8') as f:
            f.write(launcher_content)

        print("   ✅ تم إنشاء ملف التشغيل السريع")

    def create_requirements_file(self):
        """إنشاء ملف متطلبات النظام"""
        requirements_content = """
===============================================================================
                        متطلبات تشغيل البرنامج
===============================================================================

🖥️ متطلبات النظام الأساسية:
• نظام التشغيل: Windows 7 أو أحدث (32-bit أو 64-bit)
• المعالج: Intel Pentium 4 أو AMD Athlon 64 أو أحدث
• الذاكرة: 2 GB RAM (يُنصح بـ 4 GB أو أكثر)
• مساحة القرص: 500 MB مساحة فارغة
• دقة الشاشة: 1024x768 أو أعلى (يُنصح بـ 1366x768)

📦 المكتبات المدمجة:
• PyQt5 - واجهة المستخدم الرسومية
• SQLite3 - قاعدة البيانات
• FPDF2 - إنشاء ملفات PDF
• Arabic Reshaper - دعم النصوص العربية
• Python-BIDI - اتجاه النصوص العربية
• OpenPyXL - التعامل مع ملفات Excel
• Matplotlib - الرسوم البيانية
• NumPy - العمليات الرياضية
• Pillow - معالجة الصور

🔧 إعدادات إضافية:
• تأكد من تمكين اللغة العربية في النظام
• يُنصح بتعطيل برامج مكافحة الفيروسات مؤقتاً عند التشغيل الأول
• تأكد من وجود صلاحيات الكتابة في مجلد البرنامج

⚡ نصائح لتحسين الأداء:
• أغلق البرامج غير الضرورية قبل تشغيل البرنامج
• تأكد من وجود مساحة كافية على القرص الصلب
• استخدم SSD بدلاً من HDD لأداء أفضل
• تأكد من تحديث تعريفات كرت الشاشة

🛠️ استكشاف الأخطاء الشائعة:
• إذا لم يعمل البرنامج: تأكد من وجود جميع الملفات
• إذا ظهرت رسالة خطأ: تحقق من ملفات السجل في مجلد "logs"
• إذا كانت الواجهة غير واضحة: اضبط دقة الشاشة
• إذا لم تظهر النصوص العربية: تأكد من تثبيت الخطوط العربية

===============================================================================
"""

        requirements_file = os.path.join(self.output_dir, "متطلبات_النظام.txt")
        with open(requirements_file, 'w', encoding='utf-8') as f:
            f.write(requirements_content)

        print("   ✅ تم إنشاء ملف متطلبات النظام")

    def create_version_info(self):
        """إنشاء ملف معلومات الإصدار"""
        version_info = {
            "اسم_البرنامج": "المعين في الحراسة العامة",
            "الإصدار": "1.0.0",
            "تاريخ_البناء": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "نوع_البناء": "إصدار كامل محمول",
            "المطور": "فريق تطوير أنظمة إدارة المؤسسات التعليمية",
            "الوصف": "نظام متكامل ذكي لتسيير الجوانب التربوية والمالية",
            "المكتبات_المدمجة": self.required_packages + self.optional_packages,
            "الملفات_الأساسية": self.essential_files,
            "ملفات_الطباعة": self.print_files,
            "المجلدات_المطلوبة": self.required_folders,
            "حجم_البرنامج_MB": self.get_directory_size(os.path.join(self.output_dir, "البرنامج")),
            "عدد_الملفات": self.count_files(os.path.join(self.output_dir, "البرنامج")),
            "نظام_التشغيل_المدعوم": "Windows 7/8/10/11",
            "معمارية_المعالج": "32-bit & 64-bit",
            "متطلبات_الذاكرة_MB": 2048,
            "مساحة_القرص_المطلوبة_MB": 500
        }

        version_file = os.path.join(self.output_dir, "معلومات_الإصدار.json")
        with open(version_file, 'w', encoding='utf-8') as f:
            json.dump(version_info, f, ensure_ascii=False, indent=2)

        print("   ✅ تم إنشاء ملف معلومات الإصدار")

    def get_directory_size(self, directory):
        """حساب حجم المجلد بالميجابايت"""
        if not os.path.exists(directory):
            return 0

        total_size = 0
        for dirpath, dirnames, filenames in os.walk(directory):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                if os.path.exists(filepath):
                    total_size += os.path.getsize(filepath)

        return round(total_size / (1024 * 1024), 2)

    def count_files(self, directory):
        """عد الملفات في المجلد"""
        if not os.path.exists(directory):
            return 0

        file_count = 0
        for dirpath, dirnames, filenames in os.walk(directory):
            file_count += len(filenames)

        return file_count

    def create_final_archive(self):
        """إنشاء أرشيف نهائي مضغوط"""
        print("\n📦 إنشاء الأرشيف النهائي...")

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        archive_name = f"المعين_في_الحراسة_العامة_v1.0_{timestamp}.zip"
        archive_path = os.path.join(self.script_dir, archive_name)

        try:
            with zipfile.ZipFile(archive_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for root, dirs, files in os.walk(self.output_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        arc_name = os.path.relpath(file_path, self.output_dir)
                        zipf.write(file_path, arc_name)

            archive_size = os.path.getsize(archive_path) / (1024 * 1024)
            print(f"   ✅ تم إنشاء الأرشيف: {archive_name}")
            print(f"   📊 حجم الأرشيف: {archive_size:.2f} MB")

            return archive_path

        except Exception as e:
            print(f"   ❌ خطأ في إنشاء الأرشيف: {str(e)}")
            return None

    def cleanup_build_files(self):
        """تنظيف ملفات البناء المؤقتة"""
        print("\n🧹 تنظيف الملفات المؤقتة...")

        cleanup_items = [
            self.build_dir,
            self.dist_dir,
            os.path.join(self.script_dir, "program.spec"),
            os.path.join(self.script_dir, "__pycache__")
        ]

        for item in cleanup_items:
            if os.path.exists(item):
                try:
                    if os.path.isdir(item):
                        shutil.rmtree(item)
                    else:
                        os.remove(item)
                    print(f"   🗑️ تم حذف: {os.path.basename(item)}")
                except Exception as e:
                    print(f"   ⚠️ تعذر حذف {item}: {str(e)}")

        print("   ✅ تم تنظيف الملفات المؤقتة")

    def print_summary(self, archive_path):
        """طباعة ملخص العملية"""
        print("\n" + "=" * 80)
        print("                        🎉 تم إكمال التحزيم بنجاح!")
        print("=" * 80)

        if archive_path:
            print(f"📦 الأرشيف النهائي: {os.path.basename(archive_path)}")
            print(f"📁 المسار: {archive_path}")

        print(f"📂 المجلد المحمول: {self.output_dir}")

        # إحصائيات
        program_dir = os.path.join(self.output_dir, "البرنامج")
        if os.path.exists(program_dir):
            size = self.get_directory_size(program_dir)
            files = self.count_files(program_dir)
            print(f"📊 حجم البرنامج: {size} MB")
            print(f"📄 عدد الملفات: {files}")

        print("\n🚀 كيفية الاستخدام:")
        print("   1. فك ضغط الأرشيف على أي جهاز")
        print("   2. شغل ملف 'تشغيل_البرنامج.bat'")
        print("   3. أو ادخل مجلد 'البرنامج' وشغل الملف التنفيذي")

        print("\n📋 الملفات المرفقة:")
        print("   • اقرأني.txt - تعليمات الاستخدام")
        print("   • متطلبات_النظام.txt - متطلبات التشغيل")
        print("   • معلومات_الإصدار.json - تفاصيل الإصدار")
        print("   • تشغيل_البرنامج.bat - تشغيل سريع")

        print("\n" + "=" * 80)

    def build(self):
        """تنفيذ عملية التحزيم الكاملة"""
        self.print_header()

        # فحص المتطلبات
        if not self.check_python_version():
            return False

        if not self.check_files_exist():
            return False

        # تثبيت المكتبات
        if not self.install_requirements():
            return False

        # إنشاء ملف التكوين
        spec_file = self.create_spec_file()
        if not spec_file:
            return False

        # بناء الملف التنفيذي
        if not self.build_executable(spec_file):
            return False

        # إنشاء الحزمة المحمولة
        if not self.create_portable_package():
            return False

        # إنشاء الأرشيف النهائي
        archive_path = self.create_final_archive()

        # تنظيف الملفات المؤقتة
        self.cleanup_build_files()

        # طباعة الملخص
        self.print_summary(archive_path)

        return True

def main():
    """الدالة الرئيسية"""
    builder = ProgramBuilder()

    try:
        success = builder.build()
        if success:
            print("\n🎉 تم إكمال عملية التحزيم بنجاح!")
            input("\n📝 اضغط Enter للخروج...")
        else:
            print("\n❌ فشلت عملية التحزيم!")
            input("\n📝 اضغط Enter للخروج...")
    except KeyboardInterrupt:
        print("\n\n⏹️ تم إيقاف العملية بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {str(e)}")
        input("\n📝 اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
