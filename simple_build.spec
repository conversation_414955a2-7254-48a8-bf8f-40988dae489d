# -*- mode: python ; coding: utf-8 -*-
# ملف spec مبسط لتجنب مشاكل التحزيم

block_cipher = None

a = Analysis(
    ['main_window.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('01.ico', '.'),  # الأيقونة في المجلد الرئيسي
        ('01.ico', '_internal'),  # نسخة إضافية في _internal للتوافق
        ('fonts/', 'fonts/'),
        ('logs/', 'logs/'),
        ('reports/', 'reports/'),
    ],
    hiddenimports=[
        # الوحدات الأساسية فقط
        'PyQt5.QtWidgets',
        'PyQt5.QtGui', 
        'PyQt5.QtCore',
        'sqlite3',
        'logging',
        # حل مشكلة jaraco
        'pkg_resources',
        'pkg_resources.py2_warn',
        'jaraco.text',
        'jaraco.functools',
        'more_itertools',
        'importlib_metadata',
        'zipp',
        'setuptools',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'matplotlib',
        'numpy',
        'pandas',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='school_system',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,  # إخفاء موجه الأوامر
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='01.ico',
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='school_system'
)
