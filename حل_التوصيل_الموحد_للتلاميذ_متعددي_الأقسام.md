# حل التوصيل الموحد للتلاميذ متعددي الأقسام

## المشكلة

بعض التلاميذ مسجلين في قسمين أو أكثر، وعند أداء الواجبات الشهرية أو واجبات التسجيل يتم طباعة توصيل منفصل لكل قسم، مما يؤدي إلى:

- **توصيلات متعددة** للتلميذ الواحد
- **تعقيد في المتابعة** المالية
- **استهلاك ورق** إضافي
- **صعوبة في المراجعة** والتدقيق

## الحل المطبق

### 🎯 **التوصيل الموحد بناءً على رمز التلميذ**

تم إضافة نظام توصيل موحد يجمع جميع سجلات التلميذ الواحد (بنفس الرمز) من جميع الأقسام في توصيل واحد.

## المميزات الجديدة

### 🖨️ **زر التوصيل الموحد**

#### الموقع:
```
📝 التسجيل | 💰 الواجبات الشهرية | 📊 التقارير | 🗑️ حذف تلميذ | 
🔍 استعلام الأداء | 🚫 إلغاء التنشيط | ↩️ إرجاع التلميذ | 
❌ إلغاء التحديد | 🔄 تحديث النموذج | 🖨️ توصيل موحد
```

#### الوظيفة:
- **اللون**: أخضر مائل للزرقة (`#16a085`)
- **النص**: "🖨️ توصيل موحد"
- **الوظيفة**: `show_unified_receipt_dialog()`

### 📋 **نافذة اختيار التوصيل**

عند الضغط على الزر تظهر نافذة تحتوي على:

#### 1. **حقل رمز التلميذ**
```
رمز التلميذ: [_____________]
             أدخل رمز التلميذ...
```

#### 2. **نوع التوصيل**
```
نوع التوصيل: [الواجبات الشهرية ▼]
              - الواجبات الشهرية
              - واجبات التسجيل
```

#### 3. **معلومات إضافية**
```
💡 هذا التوصيل سيجمع جميع سجلات التلميذ من جميع الأقسام المسجل بها
📋 سيتم عرض ملخص موحد بدلاً من توصيلات متعددة
```

#### 4. **أزرار التحكم**
```
[🖨️ طباعة التوصيل]  [❌ إلغاء]
```

## آلية العمل

### 🔍 **البحث والتجميع**

#### 1. **البحث بالرمز**
```sql
-- جلب معلومات التلميذ الأساسية
SELECT اسم_التلميذ, رمز_التلميذ, رقم_الهاتف_الأول, المؤسسة_الاصلية
FROM جدول_البيانات 
WHERE رمز_التلميذ = ?
```

#### 2. **جمع الواجبات الشهرية**
```sql
-- جمع جميع الواجبات الشهرية من جميع الأقسام
SELECT md.month, md.year, md.amount_required, md.amount_paid, 
       md.amount_remaining, md.payment_date, md.payment_status,
       md.القسم, md.اسم_الاستاذ, md.المادة, md.notes
FROM monthly_duties md
JOIN جدول_البيانات jb ON md.student_id = jb.id
WHERE jb.رمز_التلميذ = ?
ORDER BY md.year DESC, [ترتيب الأشهر]
```

#### 3. **جمع واجبات التسجيل**
```sql
-- جمع جميع واجبات التسجيل من جميع الأقسام
SELECT rf.payment_type, rf.amount_paid, rf.payment_date, 
       rf.payment_method, rf.القسم, rf.اسم_الاستاذ, 
       rf.المادة, rf.notes
FROM registration_fees rf
JOIN جدول_البيانات jb ON rf.student_id = jb.id
WHERE jb.رمز_التلميذ = ?
ORDER BY rf.payment_date DESC
```

### 📊 **التجميع والتلخيص**

#### للواجبات الشهرية:
- **تجميع حسب الشهر والسنة**
- **جمع المبالغ من جميع الأقسام**
- **عرض قائمة الأقسام المسجل بها**
- **حساب الإجماليات**

#### لواجبات التسجيل:
- **تجميع حسب نوع الدفعة**
- **جمع المبالغ من جميع الأقسام**
- **عرض تفاصيل كل دفعة**
- **حساب الإجمالي العام**

## نموذج التوصيل الموحد

### 🧾 **توصيل الواجبات الشهرية**

```
==================================================
                المؤسسة التعليمية
                    المدينة
==================================================
                هاتف: 0123456789

           توصيل موحد - الواجبات الشهرية
--------------------------------------------------
                    اسم التلميذ: أحمد محمد علي
                    رمز التلميذ: 12345
                    الهاتف: 0987654321
            المؤسسة الأصلية: المدرسة الابتدائية
--------------------------------------------------

تفاصيل الواجبات الشهرية:
--------------------------------------------------
                         الشهر: يناير 2024
              الأقسام: الرياضيات، العلوم، العربية
                    المطلوب: 600.00 درهم
                    المدفوع: 600.00 درهم
                    المتبقي: 0.00 درهم
                تاريخ الدفع: 2024-01-15
                      الحالة: مدفوع
------------------------------
                         الشهر: فبراير 2024
              الأقسام: الرياضيات، العلوم، العربية
                    المطلوب: 600.00 درهم
                    المدفوع: 400.00 درهم
                    المتبقي: 200.00 درهم
                تاريخ الدفع: 2024-02-10
                      الحالة: مدفوع جزئياً
------------------------------

==================================================
                   الإجمالي العام:
              إجمالي المطلوب: 1200.00 درهم
              إجمالي المدفوع: 1000.00 درهم
              إجمالي المتبقي: 200.00 درهم
==================================================
        تاريخ الطباعة: 2024-03-15 14:30:25
                    شكراً لكم
```

### 🧾 **توصيل واجبات التسجيل**

```
==================================================
                المؤسسة التعليمية
                    المدينة
==================================================
                هاتف: 0123456789

           توصيل موحد - واجبات التسجيل
--------------------------------------------------
                    اسم التلميذ: أحمد محمد علي
                    رمز التلميذ: 12345
                    الهاتف: 0987654321
            المؤسسة الأصلية: المدرسة الابتدائية
--------------------------------------------------

تفاصيل واجبات التسجيل:
    الأقسام المسجل بها: الرياضيات، العلوم، العربية
--------------------------------------------------
                    نوع الدفعة: رسوم التسجيل
              الأقسام: الرياضيات، العلوم، العربية
              إجمالي المبلغ: 1500.00 درهم

  الدفعة 1:
                    المبلغ: 500.00 درهم
                    التاريخ: 2024-01-10
                    الطريقة: نقداً
                    القسم: الرياضيات

  الدفعة 2:
                    المبلغ: 500.00 درهم
                    التاريخ: 2024-01-10
                    الطريقة: نقداً
                    القسم: العلوم

  الدفعة 3:
                    المبلغ: 500.00 درهم
                    التاريخ: 2024-01-10
                    الطريقة: نقداً
                    القسم: العربية
------------------------------

==================================================
                   الإجمالي العام:
              إجمالي المدفوع: 1500.00 درهم
                  عدد الأقسام: 3
==================================================
        تاريخ الطباعة: 2024-03-15 14:30:25
                    شكراً لكم
```

## الفوائد

### ✅ **للإدارة**
- **توفير الورق** - توصيل واحد بدلاً من متعددة
- **سهولة المراجعة** - جميع البيانات في مكان واحد
- **دقة المحاسبة** - إجماليات واضحة ومجمعة
- **توفير الوقت** - طباعة واحدة بدلاً من متعددة

### ✅ **للأولياء**
- **وضوح أكبر** - رؤية شاملة لجميع الأقسام
- **سهولة الحفظ** - توصيل واحد للاحتفاظ به
- **فهم أفضل** - إجماليات واضحة للمبالغ
- **متابعة أسهل** - تتبع الدفعات بشكل موحد

### ✅ **للنظام**
- **تقليل التعقيد** - منطق موحد للطباعة
- **مرونة أكبر** - يعمل مع أي عدد من الأقسام
- **صيانة أسهل** - كود منظم ومركزي
- **قابلية التوسع** - يمكن إضافة ميزات جديدة

## كيفية الاستخدام

### 📝 **خطوات الطباعة**

1. **افتح تبويب "اللوائح والأقسام"**
2. **اضغط على زر "🖨️ توصيل موحد"**
3. **أدخل رمز التلميذ** في الحقل المخصص
4. **اختر نوع التوصيل** (شهري أو تسجيل)
5. **اضغط على "🖨️ طباعة التوصيل"**
6. **ستظهر معاينة التوصيل** قبل الطباعة

### ⚠️ **ملاحظات مهمة**

- **رمز التلميذ مطلوب** - يجب إدخال رمز صحيح
- **البحث شامل** - يبحث في جميع الأقسام تلقائياً
- **التجميع تلقائي** - يجمع البيانات حسب الشهر/النوع
- **المعاينة متاحة** - يمكن مراجعة التوصيل قبل الطباعة

---

**هذا الحل يوفر طريقة موحدة وفعالة لطباعة توصيلات التلاميذ متعددي الأقسام!** 🎯
