#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
سكريپت إصلاح بسيط لمسارات قاعدة البيانات
يصحح فقط الملفات التي تحتاج إصلاح
"""

import os
import re
import glob

def fix_file_simple(file_path):
    """إصلاح ملف واحد بطريقة بسيطة وآمنة"""
    try:
        print(f"🔧 فحص الملف: {file_path}")
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        changes_made = False
        
        # التحقق من وجود استيراد get_db_path
        has_import = 'from db_path import get_db_path' in content
        
        # البحث عن استخدامات قاعدة البيانات
        db_patterns = [
            r'sqlite3\.connect\s*\(\s*["\']data\.db["\']\s*\)',
            r'db_path\s*=\s*["\']data\.db["\']',
            r'self\.db_path\s*=\s*["\']data\.db["\']'
        ]
        
        needs_fix = False
        for pattern in db_patterns:
            if re.search(pattern, content):
                needs_fix = True
                break
        
        if not needs_fix:
            print(f"  ℹ️ لا يحتاج إصلاح")
            return False
        
        # إضافة الاستيراد إذا لم يكن موجوداً
        if not has_import:
            # البحث عن مكان مناسب لإضافة الاستيراد
            lines = content.split('\n')
            import_added = False
            
            for i, line in enumerate(lines):
                if line.strip().startswith('import ') or line.strip().startswith('from '):
                    # إضافة الاستيراد بعد آخر استيراد
                    if i + 1 < len(lines) and not (lines[i + 1].strip().startswith('import ') or lines[i + 1].strip().startswith('from ')):
                        lines.insert(i + 1, 'from db_path import get_db_path')
                        import_added = True
                        changes_made = True
                        break
            
            if not import_added:
                # إضافة الاستيراد في بداية الملف بعد التعليقات
                for i, line in enumerate(lines):
                    if not line.strip().startswith('#') and line.strip():
                        lines.insert(i, 'from db_path import get_db_path')
                        changes_made = True
                        break
            
            content = '\n'.join(lines)
        
        # استبدال استخدامات قاعدة البيانات
        replacements = [
            (r'sqlite3\.connect\s*\(\s*["\']data\.db["\']\s*\)', 'sqlite3.connect(get_db_path())'),
            (r'db_path\s*=\s*["\']data\.db["\']', 'db_path=get_db_path()'),
            (r'self\.db_path\s*=\s*["\']data\.db["\']', 'self.db_path = get_db_path()')
        ]
        
        for pattern, replacement in replacements:
            if re.search(pattern, content):
                content = re.sub(pattern, replacement, content)
                changes_made = True
                print(f"  ✅ تم استبدال: {pattern}")
        
        # حفظ الملف إذا تم إجراء تغييرات
        if changes_made:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"  ✅ تم تحديث الملف")
            return True
        else:
            print(f"  ℹ️ لا توجد تغييرات مطلوبة")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في معالجة {file_path}: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء إصلاح مسارات قاعدة البيانات (الطريقة البسيطة)")
    print("=" * 60)
    
    # قائمة الملفات المهمة التي تحتاج إصلاح
    important_files = [
        'main_window.py',
        'sub252_window.py', 
        'sub262_window.py',
        'sub232_window.py',
        'attendance_processing_window.py'
    ]
    
    updated_count = 0
    for file_path in important_files:
        if os.path.exists(file_path):
            if fix_file_simple(file_path):
                updated_count += 1
        else:
            print(f"⚠️ الملف غير موجود: {file_path}")
        print()
    
    print("=" * 60)
    print(f"🎉 انتهى الإصلاح!")
    print(f"📊 تم تحديث {updated_count} من {len(important_files)} ملف")
    
    if updated_count > 0:
        print(f"\n💡 الخطوات التالية:")
        print(f"  1. اختبر البرنامج: python main_window.py")
        print(f"  2. إذا كان يعمل، احذف ملفات .backup")

if __name__ == "__main__":
    main()
