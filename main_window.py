# main_window.py
# -*- coding: utf-8 -*-

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QPushButton, QVBoxLayout,
                             QHBoxLayout, QFrame, QDesktopWidget,
                             QStackedWidget, QMessageBox, QLabel, QSpacerItem, QSizePolicy,
                             QTableWidget, QTabWidget, QTabBar, QDialog, QStyle)
from PyQt5.QtGui import QFont, QIcon, QColor, QPixmap
from PyQt5.QtCore import Qt, QTimer, QSize, QCoreApplication

# --- نظام التحكم في مستوى الرسائل ---
VERBOSE_MODE = False
SHOW_FONT_REGISTRATION = False
SHOW_CSS_WARNINGS = False

def debug_print(message, force=False):
    """طباعة الرسائل حسب مستوى التفصيل المحدد"""
    if VERBOSE_MODE or force:
        print(message)

def suppress_qt_warnings():
    """قمع تحذيرات Qt غير المهمة"""
    if not SHOW_CSS_WARNINGS:
        os.environ['QT_LOGGING_RULES'] = '*.debug=false;qt.qpa.*=false'

# تطبيق قمع التحذيرات
suppress_qt_warnings()

# إنشاء كومبوننت لشريط تبويب مزدوج الصف
class TwoRowTabComponent(QWidget):
    """مكون مخصص يستخدم صفين من QTabBar مع QStackedWidget للمحتوى"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("TwoRowTabComponent")

        # إنشاء التخطيط الرئيسي
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.setSpacing(0)

        # إنشاء إطار لشريط التبويب
        self.tab_frame = QFrame()
        self.tab_frame.setObjectName("TabFrame")
        self.tab_frame.setMinimumHeight(70)
       

        # تخطيط شريط التبويب
        self.tab_layout = QVBoxLayout(self.tab_frame)
        self.tab_layout.setContentsMargins(0, 0, 0, 0)
        self.tab_layout.setSpacing(5)

        # إنشاء شريط التبويب العلوي (أول 10 تبويبات)
        self.top_tab_bar = QTabBar()
        self.top_tab_bar.setObjectName("TopTabBar")
        self.top_tab_bar.setShape(QTabBar.RoundedNorth)
        self.top_tab_bar.setExpanding(False)
        self.top_tab_bar.setDrawBase(False)
        self.top_tab_bar.setCursor(Qt.PointingHandCursor)

        # إنشاء شريط التبويب السفلي (باقي التبويبات)
        self.bottom_tab_bar = QTabBar()
        self.bottom_tab_bar.setObjectName("BottomTabBar")
        self.bottom_tab_bar.setShape(QTabBar.RoundedNorth)
        self.bottom_tab_bar.setExpanding(False)
        self.bottom_tab_bar.setDrawBase(False)
        self.bottom_tab_bar.setCursor(Qt.PointingHandCursor)

        # إضافة أشرطة التبويب إلى التخطيط
        self.tab_layout.addWidget(self.top_tab_bar)
        self.tab_layout.addWidget(self.bottom_tab_bar)

        # إنشاء محتوى الويدجيت المكدس
        self.stacked_widget = QStackedWidget()
        self.stacked_widget.setObjectName("TabContent")

        # ربط إشارات تغيير التبويب بالدوال المعالجة
        self.top_tab_bar.currentChanged.connect(self._on_top_tab_changed)
        self.bottom_tab_bar.currentChanged.connect(self._on_bottom_tab_changed)

        # تخزين الإشارة الخارجية لتغيير التبويب
        self.current_changed_signal = None

        # إضافة المكونات إلى التخطيط الرئيسي
        self.layout.addWidget(self.tab_frame)
        self.layout.addWidget(self.stacked_widget, 1)

        # المتغيرات الداخلية
        self.first_row_count = 10
        self.previous_tab_index = 0
        self.tab_data = {}

    def _on_top_tab_changed(self, index):
        """معالجة تغيير تبويب في الصف العلوي"""
        if index < 0:  # إذا تم إلغاء تحديد جميع التبويبات
            return

        # الحصول على البيانات المرتبطة بالتبويب
        tab_data = self.top_tab_bar.tabData(index)

        if tab_data:
            # حفظ المؤشر السابق
            self.previous_tab_index = self.stacked_widget.currentIndex()

            # تعيين الويدجيت المكدس للتبويب المحدد
            self.stacked_widget.setCurrentIndex(index)

            # إلغاء تحديد جميع التبويبات في الصف السفلي
            self.bottom_tab_bar.blockSignals(True)
            self.bottom_tab_bar.setCurrentIndex(-1)
            self.bottom_tab_bar.blockSignals(False)

            # استدعاء الإشارة الخارجية إذا كانت موجودة
            if self.current_changed_signal:
                self.current_changed_signal(index, tab_data)

    def _on_bottom_tab_changed(self, index):
        """معالجة تغيير تبويب في الصف السفلي"""
        if index < 0:  # إذا تم إلغاء تحديد جميع التبويبات
            return

        # حساب المؤشر الفعلي (مع مراعاة التبويبات في الصف العلوي)
        actual_index = index + self.first_row_count

        # الحصول على البيانات المرتبطة بالتبويب
        tab_data = self.bottom_tab_bar.tabData(index)

        if tab_data:
            # حفظ المؤشر السابق
            self.previous_tab_index = self.stacked_widget.currentIndex()

            # تعيين الويدجيت المكدس للتبويب المحدد
            self.stacked_widget.setCurrentIndex(actual_index)

            # إلغاء تحديد جميع التبويبات في الصف العلوي
            self.top_tab_bar.blockSignals(True)
            self.top_tab_bar.setCurrentIndex(-1)
            self.top_tab_bar.blockSignals(False)

            # استدعاء الإشارة الخارجية إذا كانت موجودة
            if self.current_changed_signal:
                self.current_changed_signal(actual_index, tab_data)

    def addTab(self, page, text):
        """إضافة تبويب جديد وصفحة مرتبطة به"""
        # إضافة الصفحة إلى الويدجيت المكدس
        index = self.stacked_widget.addWidget(page)

        # تحديد أي صف سيتم إضافة التبويب إليه
        if index < self.first_row_count:
            # إضافة التبويب للصف العلوي
            tab_index = self.top_tab_bar.addTab(text)
            self.top_tab_bar.setTabData(tab_index, index)
        else:
            # إضافة التبويب للصف السفلي
            tab_index = self.bottom_tab_bar.addTab(text)
            self.bottom_tab_bar.setTabData(tab_index, index)

        return index

    def setTabData(self, index, data):
        """تعيين بيانات مخصصة للتبويب المحدد"""
        if index < self.first_row_count:
            self.top_tab_bar.setTabData(index, data)
        else:
            self.bottom_tab_bar.setTabData(index - self.first_row_count, data)

    def tabData(self, index):
        """استرجاع بيانات التبويب المحدد"""
        if index < self.first_row_count:
            return self.top_tab_bar.tabData(index)
        else:
            return self.bottom_tab_bar.tabData(index - self.first_row_count)

    def setCurrentIndex(self, index):
        """تعيين التبويب الحالي"""
        if index < 0 or index >= self.stacked_widget.count():
            return

        # إلغاء تحديد جميع التبويبات في كلا الصفين أولاً
        self.top_tab_bar.blockSignals(True)
        self.bottom_tab_bar.blockSignals(True)
        
        # إلغاء تحديد جميع التبويبات
        self.top_tab_bar.setCurrentIndex(-1)
        self.bottom_tab_bar.setCurrentIndex(-1)

        # تحديد الصف المناسب وتعيين التبويب النشط
        if index < self.first_row_count:
            self.top_tab_bar.setCurrentIndex(index)
        else:
            bottom_index = index - self.first_row_count
            self.bottom_tab_bar.setCurrentIndex(bottom_index)

        # إعادة تفعيل الإشارات
        self.top_tab_bar.blockSignals(False)
        self.bottom_tab_bar.blockSignals(False)

        # تعيين المحتوى المناسب
        self.stacked_widget.setCurrentIndex(index)

    def currentIndex(self):
        """الحصول على مؤشر التبويب الحالي"""
        return self.stacked_widget.currentIndex()

    def setTabEnabled(self, index, enabled):
        """تمكين أو تعطيل تبويب محدد"""
        if index < self.first_row_count:
            self.top_tab_bar.setTabEnabled(index, enabled)
        else:
            self.bottom_tab_bar.setTabEnabled(index - self.first_row_count, enabled)

    def setTabToolTip(self, index, tooltip):
        """تعيين تلميح أداة للتبويب المحدد"""
        if index < self.first_row_count:
            self.top_tab_bar.setTabToolTip(index, tooltip)
        else:
            self.bottom_tab_bar.setTabToolTip(index - self.first_row_count, tooltip)

    def count(self):
        """عدد التبويبات الإجمالي"""
        return self.stacked_widget.count()

    def blockSignals(self, block):
        """تعطيل أو تمكين الإشارات لجميع المكونات"""
        self.top_tab_bar.blockSignals(block)
        self.bottom_tab_bar.blockSignals(block)
        return super().blockSignals(block)

    def setStyleSheet(self, sheet):
        """تعيين ورقة الأنماط مع تنظيف الخصائص غير المدعومة"""
        # إزالة الخصائص غير المدعومة في PyQt5
        cleaned_sheet = sheet.replace('box-shadow:', '/* box-shadow:').replace('cursor:', '/* cursor:')
        super().setStyleSheet(cleaned_sheet)

    def connectCurrentChanged(self, slot):
        """ربط إشارة تغيير التبويب بدالة خارجية"""
        self.current_changed_signal = slot

# تعريف نافذة مؤقتة بسيطة
class PlaceholderWindow(QWidget):
    def __init__(self, title="مؤقت", message="المحتوى غير متوفر", db=None, academic_year=None, parent=None, **kwargs):
        super().__init__(parent)
        layout = QVBoxLayout(self)
        layout.setAlignment(Qt.AlignCenter)
        self.label = QLabel(f"<h2>{title}</h2><p>{message}</p>")
        self.label.setAlignment(Qt.AlignCenter)
        self.label.setFont(QFont("Calibri", 14))
        self.label.setWordWrap(True)
        layout.addWidget(self.label)
        self.setWindowTitle(title)

class MainWindow(QMainWindow):
    def __init__(self, auto_show=False):
        super().__init__()
        self.setWindowTitle("نظام إدارة المدرسة")
        self.setLayoutDirection(Qt.RightToLeft)
        self.previous_tab_index = 0

        # تقليل رسائل التشخيص
        self.setup_logging()

        # تحديد مسار قاعدة البيانات داخل مجلد البرنامج
        # للبرامج المحزمة، نحتاج للحصول على مجلد البرنامج التنفيذي وليس _internal
        if getattr(sys, 'frozen', False):
            # البرنامج محزم بـ PyInstaller
            self.db_folder = os.path.dirname(sys.executable)
            print(f"INFO: البرنامج محزم - مجلد البرنامج: {self.db_folder}")
        else:
            # البرنامج يعمل من الكود المصدري
            self.db_folder = os.path.dirname(os.path.abspath(__file__))
            print(f"INFO: البرنامج من الكود المصدري - مجلد البرنامج: {self.db_folder}")

        # تعيين أيقونة البرنامج
        icon_path = os.path.join(self.db_folder, "01.ico")
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))

        # إنشاء اتصال قاعدة البيانات بسيط - إزالة الاستدعاءات المعقدة
        self.db_connection = None
        self.current_academic_year = "2024/2025"  # قيمة افتراضية بسيطة
        
        # إنشاء اتصال قاعدة بيانات بسيط محلياً - داخل مجلد البرنامج
        try:
            import sqlite3
            self.db_path = os.path.join(self.db_folder, "data.db")  # في نفس مجلد البرنامج
            if not os.path.exists(self.db_path):
                # إنشاء قاعدة بيانات فارغة إذا لم تكن موجودة
                conn = sqlite3.connect(self.db_path)
                conn.close()
                print(f"INFO: تم إنشاء قاعدة بيانات جديدة: {self.db_path}")
            
            # لا نقوم بحفظ اتصال مفتوح، بل نحفظ المسار فقط
            self.db_connection = None  # تم تعديل هذا
            print(f"INFO: تم تحديد مسار قاعدة البيانات: {self.db_path}")
        except Exception as e:
            print(f"WARNING: فشل تحديد مسار قاعدة البيانات: {e}")
            self.db_path = os.path.join(self.db_folder, "data.db")

        self.menuBar().setVisible(False)

        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)

        self.main_layout = QVBoxLayout(self.central_widget)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)

        self._create_top_navbar()
        self._setup_content_area()

        self.main_layout.addWidget(self.navbar_frame)
        self.main_layout.addWidget(self.content_area, 1)

        print("INFO: إنشاء النوافذ الفرعية...")
        self._create_windows()
        print("INFO: ربط أزرار التنقل بالنوافذ...")
        self._link_navbar_buttons()

        # تحديد النافذة الافتراضية - تبسيط الكود المعقد
        if self.tabWidget.count() > 0 and self.content_area.count() > 0:
            self.tabWidget.setCurrentIndex(0)
            self.content_area.setCurrentIndex(0)
            self.previous_tab_index = 0
            print("INFO: تم تحديد النافذة الافتراضية.")

        # عرض النافذة فقط إذا كان مطلوباً
        if auto_show:
            self.showMaximized()
            print("INFO: تم عرض النافذة الرئيسية (Maximized).")

        # متغيرات التحديث
        self.last_table_update_time = 0
        self.app_version = "1.0.0"

        # إضافة ملصق لعرض حالة التحميل
        self.loading_label = QLabel("جاري التحميل...", self)
        self.loading_label.setAlignment(Qt.AlignCenter)
        self.loading_label.setStyleSheet("background-color: rgba(0, 0, 0, 150); color: white; font-size: 16pt; padding: 20px; border-radius: 10px;")
        self.loading_label.hide()

    def setup_logging(self):
        """إعداد نظام التسجيل لتقليل الرسائل المكررة"""
        self.logged_messages = set()
        
    def log_once(self, message, level="INFO"):
        """طباعة الرسالة مرة واحدة فقط"""
        if message not in self.logged_messages:
            self.logged_messages.add(message)
            if VERBOSE_MODE:
                print(f"{level}: {message}")

    def check_for_updates(self):
        """التحقق من وجود تحديثات للبرنامج - نسخة مبسطة"""
        pass  # تم تعطيل التحقق من التحديثات مؤقتاً

    def _create_top_navbar(self):
        """إنشاء شريط التبويب العلوي المبسطة"""
        self.navbar_frame = QFrame()
        self.navbar_frame.setObjectName("NavBarFrame")
        
        navbar_style = """
            QFrame#NavBarFrame {
                background-color: #00382E;
                border-bottom: 1px solid #00382E;
                height: 40px;
                min-height: 40px;
                max-height: 40px;
                margin: 0;
                padding: 10;
            }
        """
        self.navbar_frame.setStyleSheet(navbar_style)

        navbar_layout = QVBoxLayout(self.navbar_frame)
        navbar_layout.setContentsMargins(0, 0, 0, 0)
        navbar_layout.setSpacing(0)

        self.tabWidget = TwoRowTabComponent()
        self.tabWidget.setObjectName("MainTabBar")

        clean_tab_style = """
            QTabBar {
                background-color: transparent;
            }
            QTabBar::tab {
                background-color: #B22222;
                color: #FFFFFF;
                font-family: 'Calibri';
                font-size: 11pt;
                font-weight: bold;
                min-width: 120px;
                height: 30px;
                padding: 1px 5px;
                margin: 1px 1px;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
                text-align: center;
            }
            QTabBar::tab:selected {
                background-color: #8B0000;
                color: white;
                border-bottom: 3px solid #CD5C5C;
            }
            QTabBar::tab:hover:!selected {
                background-color: #DC143C;
                color: white;
            }
            QTabBar::tab:disabled {
                color: #A9A9A9;
                background-color: #D3D3D3;
            }
        """
        
        self.tabWidget.setStyleSheet(clean_tab_style)        # قائمة التبويبات المحدثة
        self.navbar_items = [
            ("النافذة الرئيسية", "main_window"),
            ("بيانات المؤسسة", "institution_data"),
            ("تهيئة البرنامج", "program_init"),
            ("الأساتذة والمواد", "teachers_subjects"),  # نقل الأساتذة والمواد بعد تهيئة البرنامج
            ("اللوائح والأقسام", "lists_sections"),  # تغيير من إدارة الامتحانات
            ("مسك الغياب ومعالجته", "attendance_processing"),  # التبويب الجديد لمسك الغياب
            ("الإدارة المالية", "financial_management"),  # تبويب الإدارة المالية الجديد
            ("تسجيل الخروج", "logout_action")
        ]

        self.tab_pages = {}
        self.navbar_buttons = {}

        for index, (text, window_key) in enumerate(self.navbar_items):
            tab_page = QWidget()
            tab_page.setObjectName(f"TabPage_{window_key}")
            self.tab_pages[window_key] = tab_page

            tab_index = self.tabWidget.addTab(tab_page, text)
            self.tabWidget.setTabData(tab_index, window_key)
            self.navbar_buttons[window_key] = {"tab_index": tab_index}

        navbar_layout.addWidget(self.tabWidget)
        self.tabWidget.connectCurrentChanged(self._on_tab_data_changed)

    def _on_tab_data_changed(self, index, window_key):
        """معالجة حدث تغيير التبويب"""
        if window_key == "logout_action":
            self.tabWidget.blockSignals(True)
            self.tabWidget.setCurrentIndex(self.previous_tab_index)
            self.tabWidget.blockSignals(False)

            should_quit = self.show_logout_confirmation_dialog()
            if should_quit:
                QCoreApplication.instance().quit()

        elif window_key in self.windows:
            print(f"INFO: التبديل إلى النافذة: {window_key}")
            window_widget = self.windows[window_key]

            # التحقق من أن الكائن لم يتم حذفه
            try:
                # محاولة الوصول إلى خاصية بسيطة للتأكد من أن الكائن ما زال صالحاً
                _ = window_widget.objectName()
            except RuntimeError:
                print(f"ERROR: الكائن {window_key} تم حذفه من الذاكرة - إعادة إنشاء النافذة...")
                # إعادة إنشاء النافذة المحذوفة
                self._recreate_deleted_window(window_key)
                window_widget = self.windows.get(window_key)
                if not window_widget:
                    print(f"ERROR: فشل في إعادة إنشاء النافذة {window_key}")
                    return

            # تم إزالة المعالجة الخاصة لنافذة اللوائح والأقسام
            # الآن ستفتح كنافذة مدمجة مثل باقي النوافذ

            # عرض النافذة المطلوبة
            self.show_window(window_widget, window_key)
            self.previous_tab_index = index

        else:
            # في حالة عدم وجود النافذة، العودة للتبويب السابق
            self.tabWidget.blockSignals(True)
            self.tabWidget.setCurrentIndex(self.previous_tab_index)
            self.tabWidget.blockSignals(False)

    def _recreate_deleted_window(self, window_key):
        """إعادة إنشاء النافذة المحذوفة"""
        try:
            print(f"INFO: محاولة إعادة إنشاء النافذة {window_key}...")

            # لا توجد نوافذ تحتاج إعادة إنشاء حالياً
            print(f"WARNING: لا يمكن إعادة إنشاء النافذة {window_key} - غير مدعومة")

        except Exception as e:
            print(f"ERROR: خطأ عام في إعادة إنشاء النافذة {window_key}: {e}")

    def _link_navbar_buttons(self):
        """ربط أزرار شريط التنقل المبسطة"""
        for window_key, tab_data in self.navbar_buttons.items():
            tab_index = tab_data["tab_index"]

            if window_key == "logout_action":
                self.tabWidget.setTabEnabled(tab_index, True)
                self.tabWidget.setTabToolTip(tab_index, "تسجيل الخروج من البرنامج")
            elif window_key not in self.windows:
                self.tabWidget.setTabEnabled(tab_index, False)
                self.tabWidget.setTabToolTip(tab_index, "الوحدة غير متوفرة")

    def show_window(self, window_widget, window_key_debug=None):
        """عرض الويدجت المحدد في منطقة المحتوى"""
        try:
            if window_widget and isinstance(window_widget, QWidget):
                # التحقق من أن الكائن لم يتم حذفه
                try:
                    _ = window_widget.objectName()
                except RuntimeError:
                    return

                current_index_in_stack = self.content_area.indexOf(window_widget)
                if current_index_in_stack != -1:
                    self.content_area.setCurrentIndex(current_index_in_stack)
        except Exception:
            pass

    def _setup_content_area(self):
        """إعداد منطقة عرض المحتوى الرئيسية"""
        self.content_area = QStackedWidget()
        self.content_area.setObjectName("ContentArea")
        self.content_area.setStyleSheet("QWidget#ContentArea { background-color: #ECEFF1; margin-top: 0; }")
        self.content_area.setContentsMargins(0, 0, 0, 0)

    def _create_windows(self):
        """إنشاء النوافذ الفرعية المبسطة"""
        self.windows = {}

        # إنشاء النافذة الرئيسية من sub01_window
        try:
            from sub01_window import Sub01Window
            main_window = Sub01Window(parent=self)
            
            # تحويل النافذة إلى ويدجيت مدمج
            main_window.setWindowFlags(Qt.Widget)
            main_window.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
            
            print("INFO: تم إنشاء النافذة الرئيسية من sub01_window بنجاح")
        except ImportError as e:
            print(f"WARNING: فشل استيراد sub01_window: {e}")
            # في حالة فشل الاستيراد، استخدام نافذة مؤقتة
            main_window = PlaceholderWindow(
                title="النافذة الرئيسية",
                message="تعذر تحميل النافذة الرئيسية\n\nتأكد من وجود ملف sub01_window.py",
                parent=self
            )
        except Exception as e:
            print(f"ERROR: خطأ في إنشاء النافذة الرئيسية: {e}")
            # في حالة حدوث خطأ آخر، استخدام نافذة مؤقتة
            main_window = PlaceholderWindow(
                title="النافذة الرئيسية",
                message=f"حدث خطأ في تحميل النافذة الرئيسية:\n{str(e)}",
                parent=self
            )
  

        # إنشاء نافذة بيانات المؤسسة من sub2_window
        try:
            # محاولة استيراد الكلاسات المحتملة من sub2_window
            import sub2_window
            
            # البحث عن الكلاس المناسب للنافذة (تجاهل الكلاسات المدمجة في PyQt)
            institution_window_class = None
            excluded_classes = {
                'QWidget', 'QMainWindow', 'QDialog', 'QComboBox', 'QPushButton',
                'QLabel', 'QLineEdit', 'QTextEdit', 'QFrame', 'QVBoxLayout',
                'QHBoxLayout', 'QGridLayout', 'QApplication', 'QCheckBox',
                'QRadioButton', 'QSpinBox', 'QDateEdit', 'QTimeEdit',
                'QDialogButtonBox', 'QTableWidget', 'QTabWidget', 'QTabBar'
            }
            
            # البحث عن أول كلاس مخصص اسمه يبدأ بـ "Institution" أو يحتوي على "Institution" أو "Data"
            for attr_name in dir(sub2_window):
                if not attr_name.startswith('_'):
                    attr = getattr(sub2_window, attr_name)
                    if (isinstance(attr, type) and
                        hasattr(attr, '__bases__') and
                        attr.__name__ not in excluded_classes and
                        any(base.__name__ in ['QWidget', 'QMainWindow', 'QDialog'] for base in attr.__bases__) and
                        (
                            attr.__name__.lower().startswith("institution")
                            or "institution" in attr.__name__.lower()
                            or "data" in attr.__name__.lower()
                            or "info" in attr.__name__.lower()
                        )):
                        institution_window_class = attr
                        break

            # إذا لم نجد كلاساً مخصصاً، ابحث عن أي كلاس اسمه ينتهي بـ "Window" أو "Widget"
            if not institution_window_class:
                for attr_name in dir(sub2_window):
                    if not attr_name.startswith('_'):
                        attr = getattr(sub2_window, attr_name)
                        if (isinstance(attr, type) and
                            hasattr(attr, '__bases__') and
                            attr.__name__ not in excluded_classes and
                            any(base.__name__ in ['QWidget', 'QMainWindow', 'QDialog'] for base in attr.__bases__) and
                            (attr.__name__.endswith("Window") or attr.__name__.endswith("Widget"))):
                            institution_window_class = attr
                            break

            # إذا لم نجد شيئاً، ابحث عن أول كلاس مخصص في الملف ليس من PyQt
            if not institution_window_class:
                for attr_name in dir(sub2_window):
                    if not attr_name.startswith('_') and not attr_name.startswith('Q'):
                        attr = getattr(sub2_window, attr_name)
                        if (isinstance(attr, type) and
                            hasattr(attr, '__module__') and
                            attr.__module__ == 'sub2_window' and
                            attr.__name__ not in excluded_classes):
                            institution_window_class = attr
                            break

            if institution_window_class:
                try:
                    institution_data_window = institution_window_class(parent=self)
                    institution_data_window.setWindowFlags(Qt.Widget)
                    institution_data_window.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
                    print(f"INFO: تم إنشاء نافذة بيانات المؤسسة من {institution_window_class.__name__} بنجاح")
                except Exception as init_error:
                    print(f"WARNING: فشل في إنشاء نافذة من {institution_window_class.__name__}: {init_error}")
                    raise ImportError(f"فشل في إنشاء الكلاس {institution_window_class.__name__}")
            else:
                raise ImportError("لم يتم العثور على كلاس نافذة مناسب في sub2_window")
                
        except ImportError as e:
            print(f"WARNING: فشل استيراد sub2_window: {e}")
            # في حالة فشل الاستيراد، استخدام نافذة مؤقتة
            institution_data_window = PlaceholderWindow(
                title="بيانات المؤسسة",
                message="تعذر تحميل نافذة بيانات المؤسسة\n\nتأكد من وجود ملف sub2_window.py والكلاس المناسب",
                parent=self
            )
        except Exception as e:
            print(f"ERROR: خطأ في إنشاء نافذة بيانات المؤسسة: {e}")
            # في حالة حدوث خطأ آخر، استخدام نافذة مؤقتة
            institution_data_window = PlaceholderWindow(
                title="بيانات المؤسسة",
                message=f"حدث خطأ في تحميل نافذة بيانات المؤسسة:\n{str(e)}",
                parent=self
            )
        
        # إنشاء نافذة تهيئة البرنامج من sub8_window
        try:
            from sub8_window import Sub8Window
            program_init_window = Sub8Window(parent=self)
            program_init_window.setWindowFlags(Qt.Widget)
            program_init_window.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
            print("INFO: تم إنشاء نافذة تهيئة البرنامج من sub8_window بنجاح")
        except ImportError as e:
            print(f"WARNING: فشل استيراد sub8_window: {e}")
            program_init_window = PlaceholderWindow(
                title="تهيئة البرنامج",
                message="تعذر تحميل نافذة تهيئة البرنامج\n\nتأكد من وجود ملف sub8_window.py",
                parent=self
            )
        except Exception as e:
            print(f"ERROR: خطأ في إنشاء نافذة تهيئة البرنامج: {e}")
            program_init_window = PlaceholderWindow(
                title="تهيئة البرنامج",
                message=f"حدث خطأ في تحميل نافذة تهيئة البرنامج:\n{str(e)}",
                parent=self
            )

        # إنشاء نافذة اللوائح والأقسام من sub252_window
        try:
            print("INFO: بدء استيراد sub252_window...")
            
            # محاولة استيراد الكلاسات المحتملة من sub252_window
            import sub252_window
            
            # البحث عن الكلاس المناسب للنافذة (تجاهل الكلاسات المدمجة في PyQt)
            lists_window_class = None
            excluded_classes = {
                'QWidget', 'QMainWindow', 'QDialog', 'QComboBox', 'QPushButton',
                'QLabel', 'QLineEdit', 'QTextEdit', 'QFrame', 'QVBoxLayout',
                'QHBoxLayout', 'QGridLayout', 'QApplication', 'QCheckBox',
                'QRadioButton', 'QSpinBox', 'QDateEdit', 'QTimeEdit',
                'QDialogButtonBox', 'QTableWidget', 'QTabWidget', 'QTabBar'
            }
            
            # البحث عن أول كلاس مخصص اسمه يحتوي على "Window" أو "Widget"
            for attr_name in dir(sub252_window):
                if not attr_name.startswith('_'):
                    attr = getattr(sub252_window, attr_name)
                    if (isinstance(attr, type) and
                        hasattr(attr, '__bases__') and
                        attr.__name__ not in excluded_classes and
                        any(base.__name__ in ['QWidget', 'QMainWindow', 'QDialog'] for base in attr.__bases__) and
                        (attr.__name__.endswith("Window") or attr.__name__.endswith("Widget"))):
                        lists_window_class = attr
                        print(f"INFO: تم العثور على الكلاس: {attr.__name__}")
                        break

            # إذا لم نجد شيئاً، ابحث عن أي كلاس مخصص في الملف ليس من PyQt
            if not lists_window_class:
                for attr_name in dir(sub252_window):
                    if not attr_name.startswith('_') and not attr_name.startswith('Q'):
                        attr = getattr(sub252_window, attr_name)
                        if (isinstance(attr, type) and
                            hasattr(attr, '__module__') and
                            attr.__module__ == 'sub252_window' and
                            attr.__name__ not in excluded_classes):
                            lists_window_class = attr
                            print(f"INFO: تم العثور على الكلاس البديل: {attr.__name__}")
                            break

            if lists_window_class:
                print(f"INFO: استخدام الكلاس {lists_window_class.__name__} من sub252_window")
                
                # إنشاء مسار قاعدة البيانات
                db_path = self.db_path  # استخدام المسار المحدد مسبقاً
                print(f"INFO: مسار قاعدة البيانات المحدد: {db_path}")
                
                # محاولة إنشاء النافذة مع معاملات مختلفة
                print(f"INFO: إنشاء كائن {lists_window_class.__name__}...")
                try:
                    # محاولة إنشاء النافذة مع معاملات كاملة
                    lists_sections_window = lists_window_class(
                        parent=self, 
                        db=None,
                        academic_year=self.current_academic_year,
                        db_path=db_path
                    )
                except TypeError:
                    try:
                        # محاولة إنشاء النافذة مع معاملات أقل
                        lists_sections_window = lists_window_class(parent=self)
                    except TypeError:
                        # محاولة إنشاء النافذة بدون معاملات
                        lists_sections_window = lists_window_class()
                        lists_sections_window.setParent(self)
                
                print(f"INFO: تم إنشاء كائن {lists_window_class.__name__} بنجاح")
                
                # التحقق من نوع النافذة قبل التحويل
                print(f"INFO: نوع النافذة قبل التحويل: {type(lists_sections_window).__name__}")
                print(f"INFO: الفئة الأب للنافذة: {[base.__name__ for base in type(lists_sections_window).__bases__]}")
                
                # تحويل النافذة إلى ويدجيت مدمج
                print("INFO: تحويل النافذة إلى ويدجيت مدمج...")
                lists_sections_window.setWindowFlags(Qt.Widget)
                lists_sections_window.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
                print("INFO: تم تحديد خصائص النافذة بنجاح")
                
                # التحقق من خصائص النافذة
                if hasattr(lists_sections_window, 'table'):
                    print("INFO: تم العثور على جدول البيانات في النافذة")
                else:
                    print("WARNING: لم يتم العثور على جدول البيانات في النافذة")
                    
                if hasattr(lists_sections_window, 'load_data'):
                    print("INFO: تم العثور على دالة تحميل البيانات")
                else:
                    print("WARNING: لم يتم العثور على دالة تحميل البيانات")
                
                # إخفاء شريط القوائم وشريط الحالة إذا كانا موجودين
                if hasattr(lists_sections_window, 'menuBar'):
                    print("INFO: إخفاء شريط القوائم...")
                    lists_sections_window.menuBar().setVisible(False)
                else:
                    print("INFO: النافذة لا تحتوي على شريط قوائم (مناسب للتكامل)")
                    
                if hasattr(lists_sections_window, 'statusBar'):
                    print("INFO: إخفاء شريط الحالة...")
                    lists_sections_window.statusBar().setVisible(False)
                else:
                    print("INFO: النافذة لا تحتوي على شريط حالة (مناسب للتكامل)")
                
                # التحقق من حجم النافذة
                window_size = lists_sections_window.size()
                print(f"INFO: حجم النافذة: {window_size.width()} x {window_size.height()}")
                
                # التحقق من اتجاه التخطيط
                layout_direction = lists_sections_window.layoutDirection()
                print(f"INFO: اتجاه التخطيط: {'من اليمين إلى اليسار' if layout_direction == Qt.RightToLeft else 'من اليسار إلى اليمين'}")
                
                print("INFO: تم إنشاء نافذة اللوائح والأقسام من sub252_window بنجاح")
            else:
                raise ImportError("لم يتم العثور على كلاس نافذة مناسب في sub252_window")
            
        except ImportError as e:
            print(f"ERROR: فشل استيراد sub252_window: {e}")
            print("DIAGNOSTIC: الملفات المطلوبة:")
            print("  - sub252_window.py")
            print("  - أي كلاس ينتهي بـ Window أو Widget")
            
            lists_sections_window = PlaceholderWindow(
                title="اللوائح والأقسام",
                message="تعذر تحميل نافذة اللوائح والأقسام\n\nتأكد من وجود ملف sub252_window.py وكلاس نافذة مناسب",
                parent=self
            )
            print("INFO: تم إنشاء نافذة مؤقتة للوائح والأقسام")
            
        except Exception as e:
            print(f"ERROR: خطأ في إنشاء نافذة اللوائح والأقسام: {e}")
            print(f"ERROR TYPE: {type(e).__name__}")
            print(f"ERROR DETAILS: {str(e)}")
            
            # تشخيص إضافي للخطأ
            if "database" in str(e).lower():
                print("DIAGNOSTIC: الخطأ متعلق بقاعدة البيانات")
                print(f"DIAGNOSTIC: مسار قاعدة البيانات: {self.db_path}")
                print(f"DIAGNOSTIC: وجود ملف قاعدة البيانات: {os.path.exists(self.db_path)}")
            elif "init" in str(e).lower() or "__init__" in str(e).lower():
                print("DIAGNOSTIC: الخطأ في دالة التهيئة (__init__)")
                print("DIAGNOSTIC: تحقق من معاملات دالة التهيئة في الكلاس")
            elif "parent" in str(e).lower():
                print("DIAGNOSTIC: الخطأ متعلق بالنافذة الأب")
                print(f"DIAGNOSTIC: نوع النافذة الأب: {type(self).__name__}")
            
            lists_sections_window = PlaceholderWindow(
                title="اللوائح والأقسام",
                message=f"حدث خطأ في تحميل نافذة اللوائح والأقسام:\n{str(e)}",
                parent=self
            )
            print("INFO: تم إنشاء نافذة مؤقتة بديلة للوائح والأقسام")

        # إنشاء نافذة الأساتذة والمواد من sub262_window
        try:
            from sub262_window import SubjectsTeachersWindow
            teachers_subjects_window = SubjectsTeachersWindow(parent=self, db_path=self.db_path)
            
            # تحويل النافذة إلى ويدجيت مدمج
            teachers_subjects_window.setWindowFlags(Qt.Widget)
            teachers_subjects_window.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
            
            # إخفاء شريط القوائم وشريط الحالة إذا كانا موجودين
            if hasattr(teachers_subjects_window, 'menuBar'):
                teachers_subjects_window.menuBar().setVisible(False)
            
            if hasattr(teachers_subjects_window, 'statusBar'):
                teachers_subjects_window.statusBar().setVisible(False)
            print("INFO: تم إنشاء نافذة الأساتذة والمواد من sub262_window بنجاح")
            
        except ImportError as e:
            print(f"WARNING: فشل استيراد sub262_window: {e}")
            teachers_subjects_window = PlaceholderWindow(
                title="الأساتذة والمواد",
                message="تعذر تحميل نافذة الأساتذة والمواد\n\nتأكد من وجود ملف sub262_window.py",
                parent=self
            )
        except Exception as e:
            print(f"ERROR: خطأ في إنشاء نافذة الأساتذة والمواد: {e}")
            teachers_subjects_window = PlaceholderWindow(
                title="الأساتذة والمواد",
                message=f"حدث خطأ في تحميل نافذة الأساتذة والمواد:\n{str(e)}",
                parent=self
            )



        # إنشاء نافذة مسك الغياب ومعالجته من attendance_processing_window
        try:
            print("INFO: بدء استيراد attendance_processing_window...")
            
            # محاولة استيراد الكلاسات المحتملة من attendance_processing_window
            import attendance_processing_window
            
            # البحث عن الكلاس المناسب للنافذة (تجاهل الكلاسات المدمجة في PyQt)
            attendance_window_class = None
            excluded_classes = {
                'QWidget', 'QMainWindow', 'QDialog', 'QComboBox', 'QPushButton',
                'QLabel', 'QLineEdit', 'QTextEdit', 'QFrame', 'QVBoxLayout',
                'QHBoxLayout', 'QGridLayout', 'QApplication', 'QCheckBox',
                'QRadioButton', 'QSpinBox', 'QDateEdit', 'QTimeEdit',
                'QDialogButtonBox', 'QTableWidget', 'QTabWidget', 'QTabBar'
            }
            
            # البحث عن أول كلاس مخصص اسمه يحتوي على "Attendance" أو "Processing"
            for attr_name in dir(attendance_processing_window):
                if not attr_name.startswith('_'):
                    attr = getattr(attendance_processing_window, attr_name)
                    if (isinstance(attr, type) and
                        hasattr(attr, '__bases__') and
                        attr.__name__ not in excluded_classes and
                        any(base.__name__ in ['QWidget', 'QMainWindow', 'QDialog'] for base in attr.__bases__) and
                        (
                            "attendance" in attr.__name__.lower()
                            or "processing" in attr.__name__.lower()
                            or "غياب" in attr.__name__.lower()
                        )):
                        attendance_window_class = attr
                        print(f"INFO: تم العثور على كلاس الحضور والغياب: {attr.__name__}")
                        break

            # إذا لم نجد كلاساً مخصصاً، ابحث عن أي كلاس اسمه ينتهي بـ "Window" أو "Widget"
            if not attendance_window_class:
                for attr_name in dir(attendance_processing_window):
                    if not attr_name.startswith('_'):
                        attr = getattr(attendance_processing_window, attr_name)
                        if (isinstance(attr, type) and
                            hasattr(attr, '__bases__') and
                            attr.__name__ not in excluded_classes and
                            any(base.__name__ in ['QWidget', 'QMainWindow', 'QDialog'] for base in attr.__bases__) and
                            (attr.__name__.endswith("Window") or attr.__name__.endswith("Widget"))):
                            attendance_window_class = attr
                            print(f"INFO: تم العثور على الكلاس: {attr.__name__}")
                            break

            # إذا لم نجد شيئاً، ابحث عن أول كلاس مخصص في الملف ليس من PyQt
            if not attendance_window_class:
                for attr_name in dir(attendance_processing_window):
                    if not attr_name.startswith('_') and not attr_name.startswith('Q'):
                        attr = getattr(attendance_processing_window, attr_name)
                        if (isinstance(attr, type) and
                            hasattr(attr, '__module__') and
                            attr.__module__ == 'attendance_processing_window' and
                            attr.__name__ not in excluded_classes):
                            attendance_window_class = attr
                            print(f"INFO: تم العثور على الكلاس البديل: {attr.__name__}")
                            break

            if attendance_window_class:
                print(f"INFO: استخدام الكلاس {attendance_window_class.__name__} من attendance_processing_window")
                
                # إنشاء مسار قاعدة البيانات
                db_path = self.db_path  # استخدام المسار المحدد مسبقاً
                print(f"INFO: مسار قاعدة البيانات للحضور والغياب: {db_path}")
                
                # محاولة إنشاء النافذة مع معاملات مختلفة
                print(f"INFO: إنشاء كائن {attendance_window_class.__name__}...")
                try:
                    # محاولة إنشاء النافذة مع معاملات كاملة
                    attendance_processing_window = attendance_window_class(
                        parent=self, 
                        db_path=db_path,
                        academic_year=self.current_academic_year
                    )
                except TypeError:
                    try:
                        # محاولة إنشاء النافذة مع معاملات أقل
                        attendance_processing_window = attendance_window_class(parent=self, db_path=db_path)
                    except TypeError:
                        try:
                            # محاولة إنشاء النافذة مع parent فقط
                            attendance_processing_window = attendance_window_class(parent=self)
                        except TypeError:
                            # محاولة إنشاء النافذة بدون معاملات
                            attendance_processing_window = attendance_window_class()
                            attendance_processing_window.setParent(self)
                
                print(f"INFO: تم إنشاء كائن {attendance_window_class.__name__} بنجاح")
                
                # تحويل النافذة إلى ويدجيت مدمج
                print("INFO: تحويل نافذة الحضور والغياب إلى ويدجيت مدمج...")
                attendance_processing_window.setWindowFlags(Qt.Widget)
                attendance_processing_window.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
                
                # إخفاء شريط القوائم وشريط الحالة إذا كانا موجودين
                if hasattr(attendance_processing_window, 'menuBar'):
                    print("INFO: إخفاء شريط القوائم لنافذة الحضور والغياب...")
                    attendance_processing_window.menuBar().setVisible(False)
                    
                if hasattr(attendance_processing_window, 'statusBar'):
                    print("INFO: إخفاء شريط الحالة لنافذة الحضور والغياب...")
                    attendance_processing_window.statusBar().setVisible(False)
                
                print("INFO: تم إنشاء نافذة مسك الغياب ومعالجته من attendance_processing_window بنجاح")
            else:
                raise ImportError("لم يتم العثور على كلاس نافذة مناسب في attendance_processing_window")
            
        except ImportError as e:
            print(f"ERROR: فشل استيراد attendance_processing_window: {e}")
            print("DIAGNOSTIC: الملفات المطلوبة:")
            print("  - attendance_processing_window.py")
            print("  - أي كلاس ينتهي بـ Window أو Widget أو يحتوي على Attendance")
            
            attendance_processing_window = PlaceholderWindow(
                title="مسك الغياب ومعالجته",
                message="تعذر تحميل نافذة مسك الغياب ومعالجته\n\nتأكد من وجود ملف attendance_processing_window.py وكلاس نافذة مناسب",
                parent=self
            )
            print("INFO: تم إنشاء نافذة مؤقتة لمسك الغياب ومعالجته")
            
        except Exception as e:
            print(f"ERROR: خطأ في إنشاء نافذة مسك الغياب ومعالجته: {e}")
            print(f"ERROR TYPE: {type(e).__name__}")
            print(f"ERROR DETAILS: {str(e)}")
            
            attendance_processing_window = PlaceholderWindow(
                title="مسك الغياب ومعالجته",
                message=f"حدث خطأ في تحميل نافذة مسك الغياب ومعالجته:\n{str(e)}",
                parent=self
            )
            print("INFO: تم إنشاء نافذة مؤقتة بديلة لمسك الغياب ومعالجته")

        # إنشاء نافذة الإدارة المالية من financial_system_launcher
        try:
            print("INFO: بدء استيراد financial_system_launcher...")

            from financial_system_launcher import FinancialSystemLauncher
            financial_management_window = FinancialSystemLauncher(parent=self)

            # تحويل النافذة إلى ويدجيت مدمج
            financial_management_window.setWindowFlags(Qt.Widget)
            financial_management_window.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

            # إخفاء شريط القوائم وشريط الحالة إذا كانا موجودين
            if hasattr(financial_management_window, 'menuBar'):
                financial_management_window.menuBar().setVisible(False)

            if hasattr(financial_management_window, 'statusBar'):
                financial_management_window.statusBar().setVisible(False)

            print("INFO: تم إنشاء نافذة الإدارة المالية من financial_system_launcher بنجاح")

        except ImportError as e:
            print(f"WARNING: فشل استيراد financial_system_launcher: {e}")
            financial_management_window = PlaceholderWindow(
                title="الإدارة المالية",
                message="تعذر تحميل نافذة الإدارة المالية\n\nتأكد من وجود ملف financial_system_launcher.py",
                parent=self
            )
        except Exception as e:
            print(f"ERROR: خطأ في إنشاء نافذة الإدارة المالية: {e}")
            financial_management_window = PlaceholderWindow(
                title="الإدارة المالية",
                message=f"حدث خطأ في تحميل نافذة الإدارة المالية:\n{str(e)}",
                parent=self
            )

        # إضافة النوافذ إلى قاموس النوافذ
        self.windows["main_window"] = main_window
        self.windows["institution_data"] = institution_data_window
        self.windows["program_init"] = program_init_window
        self.windows["teachers_subjects"] = teachers_subjects_window
        self.windows["lists_sections"] = lists_sections_window
        self.windows["attendance_processing"] = attendance_processing_window
        self.windows["financial_management"] = financial_management_window
        
        print("INFO: بدء إضافة النوافذ إلى منطقة المحتوى...")
        self.content_area.addWidget(main_window)
        print("INFO: تم إضافة النافذة الرئيسية")
        
        self.content_area.addWidget(institution_data_window)
        print("INFO: تم إضافة نافذة بيانات المؤسسة")
        self.content_area.addWidget(program_init_window)
        print("INFO: تم إضافة نافذة تهيئة البرنامج")
        
        self.content_area.addWidget(teachers_subjects_window)
        print("INFO: تم إضافة نافذة الأساتذة والمواد")

        self.content_area.addWidget(lists_sections_window)
        print("INFO: تم إضافة نافذة اللوائح والأقسام")

        self.content_area.addWidget(attendance_processing_window)
        print("INFO: تم إضافة نافذة مسك الغياب ومعالجته")

        self.content_area.addWidget(financial_management_window)
        print("INFO: تم إضافة نافذة الإدارة المالية")

    def refresh_all_windows(self):
        """تحديث النوافذ - نسخة مبسطة"""
        print("تحديث النوافذ...")

    def get_unified_academic_year(self):
        """إرجاع السنة الدراسية الافتراضية"""
        return self.current_academic_year

    def show_logout_confirmation_dialog(self):
        """عرض نافذة تسجيل الخروج المحسنة مع خيارات الصيانة"""
        try:
            from PyQt5.QtWidgets import (QProgressBar, QCheckBox, QGroupBox,
                                       QTextEdit, QScrollArea, QFrame)

            # إنشاء نافذة تسجيل الخروج المحسنة
            logout_dialog = QDialog(self)
            logout_dialog.setWindowTitle("🚪 تسجيل الخروج من البرنامج")
            logout_dialog.setFixedSize(600, 500)
            logout_dialog.setLayoutDirection(Qt.RightToLeft)

            # إضافة أيقونة البرنامج
            try:
                app_icon = QIcon("01.ico")
                logout_dialog.setWindowIcon(app_icon)
            except:
                pass

            # تطبيق الأنماط
            logout_dialog.setStyleSheet("""
                QDialog {
                    background-color: #f8f9fa;
                }
                QGroupBox {
                    font-weight: bold;
                    border: 2px solid #dee2e6;
                    border-radius: 8px;
                    margin: 10px 0;
                    padding-top: 10px;
                }
                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 10px;
                    padding: 0 5px 0 5px;
                    background-color: #f8f9fa;
                }
                QCheckBox {
                    font-size: 12px;
                    padding: 5px;
                }
                QCheckBox::indicator {
                    width: 18px;
                    height: 18px;
                }
                QCheckBox::indicator:checked {
                    background-color: #28a745;
                    border: 2px solid #1e7e34;
                }
                QPushButton {
                    font-size: 12px;
                    font-weight: bold;
                    padding: 10px 20px;
                    border-radius: 6px;
                    margin: 5px;
                }
            """)

            main_layout = QVBoxLayout(logout_dialog)

            # العنوان الرئيسي
            title_label = QLabel("🚪 تسجيل الخروج من البرنامج")
            title_label.setFont(QFont("Calibri", 16, QFont.Bold))
            title_label.setAlignment(Qt.AlignCenter)
            title_label.setStyleSheet("""
                QLabel {
                    background-color: #dc3545;
                    color: white;
                    padding: 15px;
                    border-radius: 8px;
                    margin-bottom: 10px;
                }
            """)
            main_layout.addWidget(title_label)

            # رسالة التأكيد
            message_label = QLabel("هل أنت متأكد من الخروج من البرنامج؟")
            message_label.setAlignment(Qt.AlignCenter)
            message_label.setFont(QFont("Calibri", 14))
            message_label.setStyleSheet("padding: 10px; color: #495057;")
            main_layout.addWidget(message_label)

            # مجموعة خيارات الصيانة
            maintenance_group = QGroupBox("🔧 خيارات الصيانة قبل الخروج")
            maintenance_group.setFont(QFont("Calibri", 12, QFont.Bold))
            maintenance_layout = QVBoxLayout(maintenance_group)

            # خيارات الصيانة
            self.backup_checkbox = QCheckBox("📦 عمل نسخة احتياطية من قاعدة البيانات")
            self.backup_checkbox.setChecked(True)  # مفعل افتراضياً
            maintenance_layout.addWidget(self.backup_checkbox)

            self.cleanup_checkbox = QCheckBox("🧹 تنظيف البرنامج من الرسائل التشخيصية والعمليات المحفوظة")
            self.cleanup_checkbox.setChecked(True)  # مفعل افتراضياً
            maintenance_layout.addWidget(self.cleanup_checkbox)

            self.reports_cleanup_checkbox = QCheckBox("📄 حذف جميع التقارير المؤقتة (PDF وغيرها)")
            self.reports_cleanup_checkbox.setChecked(True)  # مفعل افتراضياً
            maintenance_layout.addWidget(self.reports_cleanup_checkbox)

            self.database_optimize_checkbox = QCheckBox("⚡ ضغط وإصلاح قاعدة البيانات")
            self.database_optimize_checkbox.setChecked(True)  # مفعل افتراضياً
            maintenance_layout.addWidget(self.database_optimize_checkbox)

            main_layout.addWidget(maintenance_group)

            # منطقة عرض التقدم
            self.progress_group = QGroupBox("📊 تقدم العمليات")
            self.progress_group.setFont(QFont("Calibri", 12, QFont.Bold))
            self.progress_group.setVisible(False)  # مخفية في البداية
            progress_layout = QVBoxLayout(self.progress_group)

            self.progress_bar = QProgressBar()
            self.progress_bar.setRange(0, 100)
            self.progress_bar.setValue(0)
            progress_layout.addWidget(self.progress_bar)

            self.progress_label = QLabel("جاهز...")
            self.progress_label.setAlignment(Qt.AlignCenter)
            self.progress_label.setFont(QFont("Calibri", 11))
            progress_layout.addWidget(self.progress_label)

            # منطقة عرض السجل
            self.log_text = QTextEdit()
            self.log_text.setMaximumHeight(100)
            self.log_text.setFont(QFont("Consolas", 9))
            self.log_text.setStyleSheet("""
                QTextEdit {
                    background-color: #f8f9fa;
                    border: 1px solid #dee2e6;
                    border-radius: 4px;
                    padding: 5px;
                }
            """)
            progress_layout.addWidget(self.log_text)

            main_layout.addWidget(self.progress_group)

            # أزرار التحكم
            button_layout = QHBoxLayout()

            # زر تنفيذ العمليات والخروج
            self.execute_exit_button = QPushButton("✅ تنفيذ العمليات والخروج")
            self.execute_exit_button.setStyleSheet("""
                QPushButton {
                    background-color: #28a745;
                    color: white;
                }
                QPushButton:hover {
                    background-color: #218838;
                }
            """)
            self.execute_exit_button.clicked.connect(self.execute_maintenance_and_exit)
            button_layout.addWidget(self.execute_exit_button)

            # زر الخروج المباشر
            direct_exit_button = QPushButton("🚪 خروج مباشر")
            direct_exit_button.setStyleSheet("""
                QPushButton {
                    background-color: #ffc107;
                    color: #212529;
                }
                QPushButton:hover {
                    background-color: #e0a800;
                }
            """)
            direct_exit_button.clicked.connect(logout_dialog.accept)
            button_layout.addWidget(direct_exit_button)

            # زر الإلغاء
            cancel_button = QPushButton("❌ إلغاء")
            cancel_button.setStyleSheet("""
                QPushButton {
                    background-color: #6c757d;
                    color: white;
                }
                QPushButton:hover {
                    background-color: #545b62;
                }
            """)
            cancel_button.clicked.connect(logout_dialog.reject)
            button_layout.addWidget(cancel_button)

            main_layout.addLayout(button_layout)

            # حفظ مرجع للحوار
            self.logout_dialog = logout_dialog

            result = logout_dialog.exec_()
            return result == QDialog.Accepted

        except Exception as e:
            print(f"خطأ في عرض نافذة تسجيل الخروج: {e}")
            return True

    def execute_maintenance_and_exit(self):
        """تنفيذ عمليات الصيانة المختارة ثم الخروج"""
        try:
            import sqlite3
            import shutil
            import zipfile
            import tempfile
            import datetime
            import glob
            import gc
            from PyQt5.QtCore import QTimer

            # إظهار منطقة التقدم
            self.progress_group.setVisible(True)
            self.logout_dialog.setFixedSize(600, 650)  # توسيع النافذة

            # تعطيل الأزرار أثناء التنفيذ
            self.execute_exit_button.setEnabled(False)

            total_steps = 0
            current_step = 0

            # حساب عدد الخطوات المطلوبة
            if self.backup_checkbox.isChecked():
                total_steps += 1
            if self.cleanup_checkbox.isChecked():
                total_steps += 1
            if self.reports_cleanup_checkbox.isChecked():
                total_steps += 1
            if self.database_optimize_checkbox.isChecked():
                total_steps += 1

            if total_steps == 0:
                self.logout_dialog.accept()
                return

            step_percentage = 100 / total_steps

            # تحديث السجل
            def log_message(message):
                self.log_text.append(f"[{datetime.datetime.now().strftime('%H:%M:%S')}] {message}")
                self.log_text.ensureCursorVisible()
                QApplication.processEvents()

            def update_progress(step_name):
                nonlocal current_step
                current_step += 1
                progress = int(current_step * step_percentage)
                self.progress_bar.setValue(progress)
                self.progress_label.setText(f"{step_name} ({current_step}/{total_steps})")
                QApplication.processEvents()

            log_message("🚀 بدء عمليات الصيانة...")

            # 1. عمل نسخة احتياطية
            if self.backup_checkbox.isChecked():
                try:
                    update_progress("عمل نسخة احتياطية")
                    log_message("📦 بدء عمل النسخة الاحتياطية...")

                    # إنشاء مجلد النسخ الاحتياطي
                    desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
                    main_folder = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة")
                    backup_folder = os.path.join(main_folder, "النسخ الاحتياطي للبرنامج")

                    if not os.path.exists(backup_folder):
                        os.makedirs(backup_folder)

                    # توليد اسم النسخة الاحتياطية
                    current_datetime = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
                    backup_name = f"database_backup_{current_datetime}"
                    backup_sqlite = os.path.join(backup_folder, f"{backup_name}.sqlite")
                    backup_zip = os.path.join(backup_folder, f"{backup_name}.zip")

                    # إنشاء النسخة الاحتياطية
                    conn = sqlite3.connect(self.db_path)
                    backup_conn = sqlite3.connect(backup_sqlite)
                    conn.backup(backup_conn)
                    backup_conn.close()
                    conn.close()

                    # ضغط النسخة الاحتياطية
                    with zipfile.ZipFile(backup_zip, 'w', zipfile.ZIP_DEFLATED) as zipf:
                        zipf.write(backup_sqlite, os.path.basename(backup_sqlite))

                    # حذف الملف المؤقت
                    os.remove(backup_sqlite)

                    backup_size_kb = os.path.getsize(backup_zip) / 1024
                    size_text = f"{backup_size_kb:.2f} KB"
                    if backup_size_kb >= 1024:
                        size_text = f"{backup_size_kb/1024:.2f} MB"

                    log_message(f"✅ تم إنشاء النسخة الاحتياطية ({size_text})")

                except Exception as e:
                    log_message(f"❌ خطأ في النسخة الاحتياطية: {str(e)}")

            # 2. تنظيف الرسائل التشخيصية
            if self.cleanup_checkbox.isChecked():
                try:
                    update_progress("تنظيف الرسائل التشخيصية")
                    log_message("🧹 بدء تنظيف الرسائل التشخيصية...")

                    # تنظيف الذاكرة
                    gc.collect()

                    # تنظيف المؤشرات المتراكمة
                    for _ in range(10):
                        try:
                            QApplication.restoreOverrideCursor()
                        except:
                            break

                    # تنظيف الملفات المؤقتة
                    temp_files_cleaned = 0
                    temp_patterns = [
                        "*.tmp", "*.temp", "*.log", "*.cache",
                        "__pycache__/*", "*.pyc", "*.pyo"
                    ]

                    for pattern in temp_patterns:
                        for file_path in glob.glob(pattern):
                            try:
                                if os.path.isfile(file_path):
                                    os.remove(file_path)
                                    temp_files_cleaned += 1
                                elif os.path.isdir(file_path):
                                    shutil.rmtree(file_path, ignore_errors=True)
                                    temp_files_cleaned += 1
                            except:
                                pass

                    log_message(f"✅ تم تنظيف {temp_files_cleaned} ملف مؤقت")

                except Exception as e:
                    log_message(f"❌ خطأ في التنظيف: {str(e)}")

            # 3. حذف التقارير المؤقتة
            if self.reports_cleanup_checkbox.isChecked():
                try:
                    update_progress("حذف التقارير المؤقتة")
                    log_message("📄 بدء حذف التقارير المؤقتة...")

                    reports_cleaned = 0
                    report_patterns = [
                        "*.pdf", "تقرير_*.pdf", "report_*.pdf",
                        "تقرير_*.docx", "تقرير_*.xlsx"
                    ]

                    # حذف التقارير من المجلد الحالي
                    for pattern in report_patterns:
                        for file_path in glob.glob(pattern):
                            try:
                                if os.path.isfile(file_path):
                                    os.remove(file_path)
                                    reports_cleaned += 1
                            except:
                                pass

                    # حذف التقارير من مجلد التقارير على سطح المكتب
                    desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
                    reports_folder = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة")

                    if os.path.exists(reports_folder):
                        for root, dirs, files in os.walk(reports_folder):
                            for file in files:
                                if file.endswith(('.pdf', '.docx', '.xlsx')) and not file.startswith('database_backup'):
                                    try:
                                        file_path = os.path.join(root, file)
                                        os.remove(file_path)
                                        reports_cleaned += 1
                                    except:
                                        pass

                    log_message(f"✅ تم حذف {reports_cleaned} تقرير مؤقت")

                except Exception as e:
                    log_message(f"❌ خطأ في حذف التقارير: {str(e)}")

            # 4. ضغط وإصلاح قاعدة البيانات
            if self.database_optimize_checkbox.isChecked():
                try:
                    update_progress("ضغط وإصلاح قاعدة البيانات")
                    log_message("⚡ بدء ضغط وإصلاح قاعدة البيانات...")

                    # الحصول على حجم قاعدة البيانات قبل الضغط
                    original_size = os.path.getsize(self.db_path) / 1024  # KB

                    # فتح اتصال بقاعدة البيانات
                    conn = sqlite3.connect(self.db_path)

                    # فحص سلامة قاعدة البيانات
                    cursor = conn.cursor()
                    cursor.execute("PRAGMA integrity_check")
                    integrity_result = cursor.fetchone()[0]

                    if integrity_result == "ok":
                        log_message("✅ فحص سلامة قاعدة البيانات: ناجح")
                    else:
                        log_message(f"⚠️ فحص سلامة قاعدة البيانات: {integrity_result}")

                    # ضغط قاعدة البيانات
                    cursor.execute("VACUUM")
                    conn.commit()
                    conn.close()

                    # الحصول على حجم قاعدة البيانات بعد الضغط
                    new_size = os.path.getsize(self.db_path) / 1024  # KB
                    saved_space = original_size - new_size

                    log_message(f"✅ تم ضغط قاعدة البيانات")
                    log_message(f"📊 الحجم الأصلي: {original_size:.2f} KB")
                    log_message(f"📊 الحجم الجديد: {new_size:.2f} KB")
                    log_message(f"💾 المساحة المحفوظة: {saved_space:.2f} KB")

                except Exception as e:
                    log_message(f"❌ خطأ في ضغط قاعدة البيانات: {str(e)}")

            # إكمال العمليات
            self.progress_bar.setValue(100)
            self.progress_label.setText("✅ تم إكمال جميع العمليات بنجاح")
            log_message("🎉 تم إكمال جميع عمليات الصيانة بنجاح!")
            log_message("🚪 جاهز للخروج من البرنامج...")

            # انتظار قصير لعرض النتائج
            QTimer.singleShot(2000, self.logout_dialog.accept)

        except Exception as e:
            if hasattr(self, 'log_text'):
                self.log_text.append(f"❌ خطأ عام في عمليات الصيانة: {str(e)}")
            print(f"خطأ في عمليات الصيانة: {e}")
            self.logout_dialog.accept()

    def closeEvent(self, event):
        """معالجة حدث إغلاق النافذة"""
        try:
            should_quit = self.show_logout_confirmation_dialog()
            if not should_quit:
                event.ignore()
                return

            # تنظيف نهائي قبل الإغلاق
            try:
                # تنظيف المؤشرات المتراكمة
                for _ in range(5):
                    try:
                        QApplication.restoreOverrideCursor()
                    except:
                        break

                # تنظيف الذاكرة
                import gc
                gc.collect()

            except:
                pass

            event.accept()
        except Exception as e:
            print(f"خطأ أثناء معالجة إغلاق النافذة: {e}")
            event.accept()

if __name__ == "__main__":
    QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    window = MainWindow(auto_show=True)
    window.show()
    exit_code = app.exec_()
    sys.exit(exit_code)