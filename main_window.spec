# -*- mode: python ; coding: utf-8 -*-

block_cipher = None
datas=[
    # قاعدة البيانات ستكون في مجلد البرنامج الرئيسي وليس داخل _internal
    ('01.ico', '.'),
    # ملفات النوافذ الرئيسية
    ('main_window.py', '.'),
    ('app.py', '.'),
    ('sub0_window.py', '.'),
    ('sub3_window.py', '.'),
    ('sub4_window.py', '.'),
    ('sub9_window.py', '.'),
    ('sub10_window.py', '.'),
    ('sub11_window.py', '.'),
    ('sub12_window.py', '.'),
    ('sub13_window.py', '.'),
    ('sub14_window.py', '.'),
    ('sub16_window.py', '.'),
    ('sub17_window.py', '.'),
    ('sub18_window.py', '.'),
    ('sub19_window.py', '.'),
    ('sub20_window.py', '.'),
    ('sub21_window.py', '.'),
    ('sub22_window.py', '.'),
    ('sub23_window.py', '.'),  # إضافة جديدة
    # ملفات الطباعة
    ('print_test.py', '.'),
    ('print1.py', '.'),
    ('print2_test.py', '.'),
    ('print5.py', '.'),
    ('print5_test.py', '.'),
    ('print6.py', '.'),
    ('thermal_image_print.py', '.'),
    # المجلدات
    ('fonts/', 'fonts/'),
    ('logs/', 'logs/'),
    # إنشاء المجلدات المطلوبة للتقارير
    ('تقارير برنامج المعين في الحراسة العامة/', 'تقارير برنامج المعين في الحراسة العامة/'),
    ('تقارير برنامج المعين في الحراسة العامة/لوائح الغياب/', 'تقارير برنامج المعين في الحراسة العامة/لوائح الغياب/'),
    # تم تعليق المجلدات غير الموجودة
    # ('تصدير/', 'تصدير/'),
    # ('تقارير_زيارة_الطبيب/', 'تقارير_زيارة_الطبيب/'),
    # ('تقارير_سجلات_المخالفات_html/', 'تقارير_سجلات_المخالفات_html/'),
    # ('تقارير_سجلات_ورقة_الدخول/', 'تقارير_سجلات_ورقة_الدخول/'),
    # ('طلبات_الشواهد_المدرسية/', 'طلبات_الشواهد_المدرسية/'),
    # ('المخالفات/', 'المخالفات/'),
    # ملفات إضافية
    ('simple6_window.py', '.'),
    ('Amiri-Bold.ttf', '.'),
    ('Amiri-Regular.ttf', '.'),
    ('database_utils.py', '.'),
    ('database_initializer.py', '.'),
    ('embed_all_windows.py', '.'),
    ('fix_parameter_mismatch.py', '.'),
    ('help_texts.py', '.'),
    ('module_loader.py', '.'),
    ('violations_table.py', '.'),
    ('absence_reports.py', '.'),
    ('attendance_report.py', '.'),
    ('default_settings_window.py', '.'),
    ('styles.py', '.'),
    ('second_semester_helper.py', '.'),  # إضافة جديدة
    # ('data.db', '.'),  # تم تعليق قاعدة البيانات لتكون خارج الملف التنفيذي
]

a = Analysis(
    ['app.py'],
    pathex=[r'c:\Users\<USER>\Desktop\taheri33'],
    binaries=[],
    datas=datas,
    # إضافة خيارات لتحسين التوافق
    hiddenimports=[
        # النوافذ الرئيسية
        'main_window',
        'app',
        'sub0_window',
        'sub1_window',
        'sub2_window',
        'sub3_window',
        'sub4_window',
        'sub5_window',
        'sub6_window',
        'sub7_window',
        'sub8_window',
        'sub9_window',
        'sub10_window',
        'sub11_window',
        'sub12_window',
        'sub13_window',
        'sub14_window',
        'sub16_window',
        'sub17_window',
        'sub18_window',
        'sub19_window',
        'sub20_window',
        'sub21_window',
        'sub22_window',
        'sub23_window',  # إضافة جديدة
        'sub24_window',
        'sub25_window',
        'sub26_window',
        'sub27_window',
        'sub100_window',
        # وحدات الطباعة
        'print_test',
        'print0',
        'print1',
        'print2',
        'print2_test',
        'print3',
        'print4',
        'print5',
        'print5_test',
        'print6',
        'print7',
        'print8',
        'print9',
        'print10',
        'thermal_image_print',
        'thermal_printer',
        'print_student_record_improved',
        'print_violation_report',
        # وحدات إضافية
        'simple6_window',
        'embed_all_windows',
        'module_loader',
        'fix_parameter_mismatch',
        'database_utils',
        'database_initializer',
        'help_texts',
        'violations_table',
        'absence_reports',
        'attendance_report',
        'default_settings_window',
        'styles',
        'custom_messages',
        'arabic_pdf_report',
        'professional_exam_table',
        'second_semester_helper',  # تأكيد على تضمين هذا الملف
        'set_default_printer',
        'split_attendance_report',
        'student_card_ui',
        'templates_manager',
        # مكتبات خارجية
        'PIL',
        'PIL.Image',
        'PIL.ImageDraw',
        'PIL.ImageFont',
        'PIL.ImageWin',
        'arabic_reshaper',
        'bidi.algorithm',
        'win32print',
        'win32ui',
        'logging',
        'sqlite3',
        'PyQt5.QtWidgets',
        'PyQt5.QtGui',
        'PyQt5.QtCore',
        'PyQt5.QtPrintSupport',
        # أضف أي وحدات أخرى مطلوبة هنا
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

# وضع "one-folder" (مجلد واحد)
exe = EXE(
    pyz,
    a.scripts,
    [],  # فارغة لوضع المجلد الواحد - لا تضع a.binaries و a.zipfiles و a.datas هنا
    exclude_binaries=True,  # مهم! يجب أن تكون True للمجلد الواحد
    name='Taheri33',
    debug=False,  # تغيير إلى False لإيقاف وضع التصحيح
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,  # تغيير إلى False لإخفاء نافذة موجه الأوامر
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch='x86',  # تحديد بنية 32 بت للتوافق مع جميع الأنظمة
    codesign_identity=None,
    entitlements_file=None,
    icon='01.ico',
)

# جمع جميع الملفات في مجلد واحد
coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='Taheri33'
)
