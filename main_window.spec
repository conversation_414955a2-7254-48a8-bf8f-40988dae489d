# -*- mode: python ; coding: utf-8 -*-

block_cipher = None
datas=[
    # قاعدة البيانات ستكون في مجلد البرنامج الرئيسي وليس داخل _internal
    ('01.ico', '.'),
    # ملفات النوافذ الرئيسية الموجودة فعلياً
    ('main_window.py', '.'),
    ('sub01_window.py', '.'),
    ('sub2_window.py', '.'),
    ('sub3_window.py', '.'),
    ('sub8_window.py', '.'),
    ('sub100_window.py', '.'),
    ('sub232_window.py', '.'),
    ('sub252_window.py', '.'),
    ('sub262_window.py', '.'),
    # ملفات النظام المالي والإدارة
    ('attendance_processing_window.py', '.'),
    ('budget_planning_window.py', '.'),
    ('expense_management_window.py', '.'),
    ('cash_flow_window.py', '.'),
    ('financial_system_launcher.py', '.'),
    ('monthly_duties_window.py', '.'),
    ('archived_accounts_manager.py', '.'),
    ('check_database.py', '.'),
    ('build_executable.py', '.'),
    # ملفات الطباعة والتقارير الموجودة فعلياً
    ('print101.py', '.'),
    ('print111.py', '.'),
    ('print144.py', '.'),
    ('print_registration_fees.py', '.'),
    ('print_registration_fees_monthly_style.py', '.'),
    ('print_registration_fees_simple.py', '.'),
    ('print_section_monthly.py', '.'),
    ('print_section_yearly.py', '.'),
    ('attendance_sheet_report.py', '.'),
    ('daily_attendance_sheet_report.py', '.'),
    # المجلدات الموجودة فعلياً
    ('fonts/', 'fonts/'),
    ('logs/', 'logs/'),
    ('reports/', 'reports/'),
    # ('data.db', '.'),  # تم تعليق قاعدة البيانات لتكون خارج الملف التنفيذي
]

a = Analysis(
    ['main_window.py'],  # تغيير نقطة الدخول إلى main_window.py
    pathex=[r'c:\Users\<USER>\Desktop\taheri10'],  # تحديث المسار
    binaries=[],
    datas=datas,
    # إضافة خيارات لتحسين التوافق
    hiddenimports=[
        # النوافذ الرئيسية الموجودة فعلياً
        'main_window',
        'sub01_window',
        'sub2_window',
        'sub3_window',
        'sub8_window',
        'sub100_window',
        'sub232_window',
        'sub252_window',
        'sub262_window',
        # وحدات النظام المالي والإدارة
        'attendance_processing_window',
        'budget_planning_window',
        'expense_management_window',
        'cash_flow_window',
        'financial_system_launcher',
        'monthly_duties_window',
        'archived_accounts_manager',
        'check_database',
        'build_executable',
        # وحدات الطباعة والتقارير الموجودة فعلياً
        'print101',
        'print111',
        'print144',
        'print_registration_fees',
        'print_registration_fees_monthly_style',
        'print_registration_fees_simple',
        'print_section_monthly',
        'print_section_yearly',
        'attendance_sheet_report',
        'daily_attendance_sheet_report',
        # مكتبات خارجية أساسية
        'PyQt5',
        'PyQt5.QtWidgets',
        'PyQt5.QtGui',
        'PyQt5.QtCore',
        'PyQt5.QtPrintSupport',
        'sqlite3',
        'logging',
        'datetime',
        'os',
        'sys',
        'tempfile',
        'shutil',
        'zipfile',
        # مكتبات PDF والطباعة
        'fpdf2',
        'arabic_reshaper',
        'bidi',
        'bidi.algorithm',
        # مكتبات Excel والبيانات
        'openpyxl',
        'openpyxl.workbook',
        'openpyxl.worksheet',
        'openpyxl.styles',
        # مكتبات الرسوم البيانية (اختيارية)
        'matplotlib',
        'matplotlib.pyplot',
        'numpy',
        # مكتبات الصور
        'PIL',
        'PIL.Image',
        'PIL.ImageDraw',
        'PIL.ImageFont',
        # مكتبات Windows (للطباعة)
        'win32print',
        'win32ui',
        'win32con',
        'win32api',
        # إصلاح مشكلة pkg_resources
        'pkg_resources',
        'pkg_resources.py2_warn',
        'jaraco',
        'jaraco.text',
        'jaraco.functools',
        'jaraco.context',
        'jaraco.collections',
        'more_itertools',
        'zipp',
        'importlib_metadata',
        # مكتبات setuptools المطلوبة
        'setuptools',
        'setuptools.msvc',
        'distutils',
        'distutils.util',
        # أضف أي وحدات أخرى مطلوبة هنا
    ],
    hookspath=['.'],  # استخدام الـ hooks المخصصة في المجلد الحالي
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # استبعاد الوحدات غير المطلوبة لتقليل حجم الملف التنفيذي
        'tkinter',
        'unittest',
        'test',
        'pip',
        'wheel',
        # استبعاد وحدات التطوير غير المطلوبة
        'pytest',
        'sphinx',
        'docutils',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

# وضع "one-folder" (مجلد واحد) - الأفضل للبرامج المعقدة
exe = EXE(
    pyz,
    a.scripts,
    [],  # فارغة لوضع المجلد الواحد - لا تضع a.binaries و a.zipfiles و a.datas هنا
    exclude_binaries=True,  # مهم! يجب أن تكون True للمجلد الواحد
    name='نظام_إدارة_المدرسة',  # اسم أكثر وضوحاً
    debug=False,  # تغيير إلى False لإيقاف وضع التصحيح
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,  # ضغط الملفات لتقليل الحجم
    console=False,  # تغيير إلى False لإخفاء نافذة موجه الأوامر
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,  # تلقائي - يختار البنية المناسبة للنظام
    codesign_identity=None,
    entitlements_file=None,
    icon='01.ico',  # أيقونة البرنامج
)

# جمع جميع الملفات في مجلد واحد
coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,  # ضغط الملفات
    upx_exclude=[
        # استبعاد ملفات معينة من الضغط إذا كانت تسبب مشاكل
        'vcruntime140.dll',
        'python3.dll',
        'python39.dll',
    ],
    name='نظام_إدارة_المدرسة'  # اسم المجلد النهائي
)
