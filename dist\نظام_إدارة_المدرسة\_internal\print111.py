#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف طباعة سجلات الأساتذة - print111.py
مطابق لتصميم print101.py مع التخصص في طباعة سجلات الأساتذة
"""

import os
import sys
import sqlite3
import traceback
from datetime import datetime
import subprocess

# ================= إعدادات خاصة بتقارير الأساتذة =================
# الجدول الرئيسي: سجلات الأساتذة - ترتيب معكوس
COL_WIDTHS_TEACHERS = [25, 30, 30, 40, 50, 15]  # 6 أعمدة
TABLE_HEADERS = ['نسبة الواجبات', 'المجموعة', 'القسم', 'المادة', 'اسم الأستاذ', 'رقم']

# إعدادات ارتفاعات الصفوف
ROW_HEIGHT_DATA = 8   # ارتفاع صفوف البيانات
ROW_HEIGHT_HEADER = 12  # ارتفاع صف رأس الجدول

# إعدادات الهوامش
PAGE_MARGIN_TOP = 0.2
PAGE_MARGIN_BOTTOM = 0.2
PAGE_MARGIN_LEFT = 10
PAGE_MARGIN_RIGHT = 10

PT_TO_MM = 0.3528
LOGO_W_PT, LOGO_H_PT = 200, 80
TITLE_H_PT = 40
LOGO_W = LOGO_W_PT * PT_TO_MM
LOGO_H = LOGO_H_PT * PT_TO_MM

try:
    from fpdf import FPDF
    import arabic_reshaper
    from bidi.algorithm import get_display
except ImportError:
    subprocess.check_call([sys.executable, "-m", "pip", "install", "fpdf2", "arabic-reshaper", "python-bidi"])
    from fpdf import FPDF
    import arabic_reshaper
    from bidi.algorithm import get_display

class ArabicPDF(FPDF):
    def __init__(self):
        super().__init__('P','mm','A4')  # صفحة A4 اتجاه عمودي
        self.set_margins(PAGE_MARGIN_LEFT, PAGE_MARGIN_TOP, PAGE_MARGIN_RIGHT)
        self.set_auto_page_break(auto=True, margin=PAGE_MARGIN_BOTTOM)
        fonts_dir = os.path.join(os.path.dirname(__file__), 'fonts')
        
        # إضافة خطوط Calibri إذا كانت متوفرة
        calibri_path = os.path.join(fonts_dir, 'calibri.ttf')
        calibri_bold_path = os.path.join(fonts_dir, 'calibrib.ttf')
        
        if os.path.exists(calibri_path):
            self.add_font('Calibri', '', calibri_path)
            self.calibri_available = True
        else:
            self.calibri_available = False
            
        if os.path.exists(calibri_bold_path):
            self.add_font('Calibri', 'B', calibri_bold_path)
            self.calibri_bold_available = True
        else:
            self.calibri_bold_available = False
        
        # إضافة خطوط Arial كبديل
        arial_path = os.path.join(fonts_dir, 'arial.ttf')
        arial_bold_path = os.path.join(fonts_dir, 'arialbd.ttf')
        
        if os.path.exists(arial_path):
            self.add_font('Arial', '', arial_path)
        if os.path.exists(arial_bold_path):
            self.add_font('Arial', 'B', arial_bold_path)
            
        # تعيين الخط الافتراضي
        if self.calibri_available:
            self.set_font('Calibri', '', 13)
        else:
            self.set_font('Arial', '', 13)
        self.set_line_width(0.4)

    def set_title_font(self, size=15):
        """تعيين خط العناوين - مكافئ QFont("Calibri", 15, QFont.Bold)"""
        if self.calibri_bold_available:
            self.set_font('Calibri', 'B', size)
        else:
            self.set_font('Arial', 'B', size)

    def set_detail_font(self, size=13):
        """تعيين خط التفاصيل - مكافئ QFont("Calibri", 13, QFont.Bold)"""
        if self.calibri_bold_available:
            self.set_font('Calibri', 'B', size)
        else:
            self.set_font('Arial', 'B', size)

    def set_normal_font(self, size=12):
        """تعيين الخط العادي"""
        if self.calibri_available:
            self.set_font('Calibri', '', size)
        else:
            self.set_font('Arial', '', size)

    def ar_text(self, txt: str) -> str:
        """تحويل النص العربي ليتم عرضه بشكل صحيح"""
        reshaped = arabic_reshaper.reshape(str(txt))
        return get_display(reshaped)

class TeachersRegistryPrinter:
    """فئة طباعة سجلات الأساتذة"""
    
    def __init__(self, db_path="data.db"):
        self.db_path = db_path
    
    def get_institution_data(self):
        """الحصول على بيانات المؤسسة"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("SELECT ImagePath1, المؤسسة FROM بيانات_المؤسسة LIMIT 1")
            result = cursor.fetchone()
            
            conn.close()
            
            if result:
                logo_path = result[0] if result[0] and os.path.exists(result[0]) else None
                institution_name = result[1] or "المؤسسة"
                return logo_path, institution_name
            else:
                return None, "المؤسسة"
                
        except Exception as e:
            print(f"خطأ في الحصول على بيانات المؤسسة: {str(e)}")
            return None, "المؤسسة"
    
    def get_teachers_data(self):
        """الحصول على بيانات الأساتذة من جدول_المواد_والاقسام"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # الحصول على البيانات من جدول_المواد_والاقسام - ترتيب معكوس
            cursor.execute("""
                SELECT
                    id,
                    نسبة_الواجبات,
                    المجموعة,
                    القسم,
                    المادة,
                    اسم_الاستاذ
                FROM جدول_المواد_والاقسام
                WHERE اسم_الاستاذ IS NOT NULL AND اسم_الاستاذ != ''
                ORDER BY اسم_الاستاذ ASC
            """)
            
            teachers_data = cursor.fetchall()
            conn.close()
            
            return teachers_data
            
        except Exception as e:
            print(f"خطأ في الحصول على بيانات الأساتذة: {str(e)}")
            return []
    
    def generate_report(self, output_path):
        """إنشاء تقرير سجلات الأساتذة"""
        try:
            # الحصول على البيانات
            logo_path, institution_name = self.get_institution_data()
            teachers_data = self.get_teachers_data()
            
            if not teachers_data:
                print("لا توجد بيانات أساتذة للطباعة")
                return False
            
            # إنشاء ملف PDF
            pdf = ArabicPDF()
            margin = 10
            usable_w = pdf.w - 2 * margin
            
            pdf.add_page()
            y = pdf.get_y()
            
            # إضافة الشعار
            if logo_path:
                x_logo = (pdf.w - LOGO_W) / 2
                pdf.image(logo_path, x=x_logo, y=y, w=LOGO_W, h=LOGO_H)
            y += LOGO_H + 5
            
            # اسم المؤسسة
            pdf.set_title_font(15)  # QFont("Calibri", 15, QFont.Bold)
            pdf.set_text_color(0, 51, 102)
            pdf.set_xy(margin, y)
            pdf.cell(usable_w, 10, pdf.ar_text(institution_name), border=0, align='C')
            y += 15
            
            # عنوان التقرير
            pdf.set_title_font(15)  # QFont("Calibri", 15, QFont.Bold)
            pdf.set_text_color(0, 51, 102)
            pdf.set_xy(margin, y)
            pdf.cell(usable_w, 12, pdf.ar_text("جدول سجلات الأساتذة"), border=1, align='C')
            pdf.set_text_color(0, 0, 0)
            y += 17
            
            # رسم رأس الجدول
            pdf.set_detail_font(13)  # QFont("Calibri", 13, QFont.Bold) - خط رؤوس الجدول
            pdf.set_fill_color(200, 220, 255)  # خلفية زرقاء فاتحة
            
            x = margin
            for i, header in enumerate(TABLE_HEADERS):
                pdf.set_xy(x, y)
                pdf.cell(COL_WIDTHS_TEACHERS[i], ROW_HEIGHT_HEADER, pdf.ar_text(header), border=1, align='C', fill=True)
                x += COL_WIDTHS_TEACHERS[i]
            
            y += ROW_HEIGHT_HEADER
            
            # رسم بيانات الأساتذة
            pdf.set_detail_font(13)  # QFont("Calibri", 13, QFont.Bold) - خط صفوف الجدول
            
            for row_idx, teacher in enumerate(teachers_data):
                # التحقق من الحاجة لصفحة جديدة
                if y > pdf.h - 30:
                    pdf.add_page()
                    y = pdf.get_y()
                    
                    # إعادة رسم رأس الجدول في الصفحة الجديدة
                    pdf.set_detail_font(13)
                    pdf.set_fill_color(200, 220, 255)
                    x = margin
                    for i, header in enumerate(TABLE_HEADERS):
                        pdf.set_xy(x, y)
                        pdf.cell(COL_WIDTHS_TEACHERS[i], ROW_HEIGHT_HEADER, pdf.ar_text(header), border=1, align='C', fill=True)
                        x += COL_WIDTHS_TEACHERS[i]
                    y += ROW_HEIGHT_HEADER
                
                # تلوين متناوب للصفوف
                if row_idx % 2 == 0:
                    pdf.set_fill_color(245, 250, 255)  # أزرق فاتح جداً
                else:
                    pdf.set_fill_color(255, 255, 255)  # أبيض
                
                # بيانات الصف - ترتيب معكوس
                teacher_id, duties_percent, group, section, subject, name = teacher
                row_data = [
                    f"{duties_percent or 100}%",
                    group or "غير محدد",
                    section or "غير محدد",
                    subject or "غير محدد",
                    name or "غير محدد",
                    str(teacher_id)
                ]
                
                x = margin
                for i, cell_data in enumerate(row_data):
                    pdf.set_xy(x, y)
                    pdf.cell(COL_WIDTHS_TEACHERS[i], ROW_HEIGHT_DATA, pdf.ar_text(cell_data), border=1, align='C', fill=True)
                    x += COL_WIDTHS_TEACHERS[i]
                
                y += ROW_HEIGHT_DATA
            
            # إضافة التوقيع والتاريخ
            y += 10
            current_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            pdf.set_normal_font(10)
            pdf.set_xy(margin, y)
            pdf.cell(usable_w/2, 8, pdf.ar_text(f'تاريخ الطباعة: {current_date}'), border=0, align='R')
            
            # مساحة للتوقيع
            signature_width = usable_w / 3
            pdf.set_xy(margin + usable_w - signature_width, y + 10)
            pdf.cell(signature_width, 15, '', border=1, align='C')
            
            pdf.set_xy(margin + usable_w - signature_width, y + 27)
            pdf.cell(signature_width, 8, pdf.ar_text('توقيع المسؤول'), border=0, align='C')
            
            # حفظ الملف
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            pdf.output(output_path)
            print(f"تم إنشاء تقرير سجلات الأساتذة: {output_path}")
            return True
            
        except Exception as e:
            print(f"خطأ في إنشاء التقرير: {str(e)}")
            traceback.print_exc()
            return False
    
    def print_report(self):
        """طباعة تقرير سجلات الأساتذة"""
        try:
            # إنشاء مجلد التقارير إذا لم يكن موجوداً
            reports_dir = os.path.join(os.path.expanduser('~'), 'Desktop', 'تقارير الأساتذة')
            if not os.path.exists(reports_dir):
                os.makedirs(reports_dir)
            
            # تحديد اسم الملف
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_path = os.path.join(reports_dir, f"سجلات_الأساتذة_{timestamp}.pdf")
            
            # إنشاء التقرير
            if self.generate_report(output_path):
                # فتح الملف بعد إنشائه
                try:
                    if sys.platform == 'win32':
                        os.startfile(output_path)
                    elif sys.platform == 'darwin':  # macOS
                        subprocess.call(['open', output_path])
                    else:  # Linux
                        subprocess.call(['xdg-open', output_path])
                    return True
                except Exception as e:
                    print(f"تم إنشاء الملف ولكن فشل في فتحه: {str(e)}")
                    return True
            else:
                return False
                
        except Exception as e:
            print(f"خطأ في طباعة التقرير: {str(e)}")
            traceback.print_exc()
            return False

def main():
    """دالة الاختبار"""
    printer = TeachersRegistryPrinter()
    printer.print_report()

if __name__ == "__main__":
    main()
