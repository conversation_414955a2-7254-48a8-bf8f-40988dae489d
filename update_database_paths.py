#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
سكريبت لتحديث جميع ملفات المشروع لتستخدم دالة مسار قاعدة البيانات المركزية
"""

import os
import re
import glob
from pathlib import Path
from db_path import get_db_path

def find_python_files():
    """العثور على جميع ملفات Python في المشروع"""
    python_files = []
    
    # البحث في المجلد الحالي
    for file in glob.glob("*.py"):
        if file not in ["update_database_paths.py", "database_utils.py"]:
            python_files.append(file)
    
    return python_files

def update_file_imports(file_path):
    """تحديث استيرادات الملف لتشمل database_utils"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # التحقق من وجود استيراد database_utils
        if "from database_utils import" not in content and "import database_utils" not in content:
            # البحث عن مكان مناسب لإضافة الاستيراد
            lines = content.split('\n')
            import_line_added = False
            
            for i, line in enumerate(lines):
                # إضافة الاستيراد بعد آخر استيراد موجود
                if line.startswith('import ') or line.startswith('from '):
                    continue
                else:
                    # إضافة الاستيراد هنا
                    lines.insert(i, "from database_utils import get_database_path, get_connection")
                    import_line_added = True
                    break
            
            if not import_line_added:
                # إضافة الاستيراد في بداية الملف
                lines.insert(0, "from database_utils import get_database_path, get_connection")
            
            content = '\n'.join(lines)
        
        return content
        
    except Exception as e:
        print(f"❌ خطأ في تحديث استيرادات {file_path}: {str(e)}")
        return None

def update_database_connections(content):
    """تحديث اتصالات قاعدة البيانات في المحتوى"""
    
    # أنماط مختلفة لاتصالات قاعدة البيانات
    patterns = [
        # sqlite3.connect(get_db_path())
        (r'sqlite3\.connect\s*\(\s*["\']school_database\.db["\']\s*\)', 'get_connection()'),
        
        # sqlite3.connect(get_db_path())
        (r'sqlite3\.connect\s*\(\s*r["\']school_database\.db["\']\s*\)', 'get_connection()'),
        
        # sqlite3.connect(os.path.join(..., get_db_path()))
        (r'sqlite3\.connect\s*\(\s*os\.path\.join\s*\([^)]*["\']school_database\.db["\']\s*\)\s*\)', 'get_connection()'),
        
        # self.db_path = get_db_path()
        (r'self\.db_path\s*=\s*["\']school_database\.db["\']', 'self.db_path = get_database_path()'),
        
        # db_path = get_db_path()
        (r'db_path\s*=\s*["\']school_database\.db["\']', 'db_path = get_database_path()'),
        
        # أنماط أخرى محتملة
        (r'["\']school_database\.db["\']', 'get_database_path()'),
    ]
    
    updated_content = content
    changes_made = 0
    
    for pattern, replacement in patterns:
        matches = re.findall(pattern, updated_content)
        if matches:
            updated_content = re.sub(pattern, replacement, updated_content)
            changes_made += len(matches)
            print(f"  ✅ تم تحديث {len(matches)} مطابقة للنمط: {pattern[:50]}...")
    
    return updated_content, changes_made

def update_file(file_path):
    """تحديث ملف واحد"""
    try:
        print(f"\n🔧 تحديث الملف: {file_path}")
        
        # قراءة المحتوى الأصلي
        with open(file_path, 'r', encoding='utf-8') as f:
            original_content = f.read()
        
        # تحديث الاستيرادات
        updated_content = update_file_imports(file_path)
        if updated_content is None:
            return False
        
        # تحديث اتصالات قاعدة البيانات
        updated_content, changes_made = update_database_connections(updated_content)
        
        # حفظ الملف إذا تم إجراء تغييرات
        if updated_content != original_content:
            # إنشاء نسخة احتياطية
            backup_path = f"{file_path}.backup"
            with open(backup_path, 'w', encoding='utf-8') as f:
                f.write(original_content)
            
            # حفظ الملف المحدث
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(updated_content)
            
            print(f"  ✅ تم تحديث الملف بنجاح. عدد التغييرات: {changes_made}")
            print(f"  💾 تم حفظ نسخة احتياطية في: {backup_path}")
            return True
        else:
            print(f"  ℹ️ لا توجد تغييرات مطلوبة في الملف")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في تحديث الملف {file_path}: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء تحديث ملفات المشروع لاستخدام دالة مسار قاعدة البيانات المركزية")
    print("=" * 80)
    
    # العثور على ملفات Python
    python_files = find_python_files()
    
    if not python_files:
        print("❌ لم يتم العثور على ملفات Python للتحديث")
        return
    
    print(f"📁 تم العثور على {len(python_files)} ملف Python:")
    for file in python_files:
        print(f"  - {file}")
    
    print("\n" + "=" * 80)
    
    # تحديث كل ملف
    updated_files = 0
    total_files = len(python_files)
    
    for file_path in python_files:
        if update_file(file_path):
            updated_files += 1
    
    print("\n" + "=" * 80)
    print(f"🎉 انتهى التحديث!")
    print(f"📊 الإحصائيات:")
    print(f"  - إجمالي الملفات: {total_files}")
    print(f"  - الملفات المحدثة: {updated_files}")
    print(f"  - الملفات بدون تغيير: {total_files - updated_files}")
    
    if updated_files > 0:
        print(f"\n💡 ملاحظات مهمة:")
        print(f"  - تم إنشاء نسخ احتياطية للملفات المحدثة")
        print(f"  - تأكد من اختبار البرنامج بعد التحديث")
        print(f"  - يمكنك استعادة النسخ الاحتياطية إذا حدثت مشاكل")
    
    print(f"\n🔧 الخطوات التالية:")
    print(f"  1. اختبر البرنامج للتأكد من عمله بشكل صحيح")
    print(f"  2. احذف النسخ الاحتياطية (.backup) إذا كان كل شيء يعمل")
    print(f"  3. أعد تحزيم البرنامج باستخدام PyInstaller")

if __name__ == "__main__":
    main()
