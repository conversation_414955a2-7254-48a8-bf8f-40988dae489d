#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار استيراد الملفات
"""

import sys

def test_import(module_name):
    """اختبار استيراد ملف واحد"""
    try:
        __import__(module_name)
        print(f"✅ {module_name}.py - يعمل بشكل صحيح")
        return True
    except Exception as e:
        print(f"❌ {module_name}.py - خطأ: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار استيراد الملفات...")
    print("=" * 50)
    
    modules = [
        'sub252_window',
        'sub262_window', 
        'sub232_window',
        'attendance_processing_window',
        'multi_section_duties_window',
        'main_window'
    ]
    
    success_count = 0
    for module in modules:
        if test_import(module):
            success_count += 1
        print()
    
    print("=" * 50)
    print(f"📊 النتيجة: {success_count}/{len(modules)} ملف يعمل بشكل صحيح")
    
    if success_count == len(modules):
        print("🎉 جميع الملفات تعمل بشكل صحيح!")
    else:
        print("⚠️ بعض الملفات تحتاج إصلاح")

if __name__ == "__main__":
    main()
