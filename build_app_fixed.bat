@echo off
chcp 65001 > nul
echo ========================================
echo    تحزيم نظام إدارة المدرسة (محسن)
echo ========================================
echo.

echo [1/6] التحقق من وجود Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Python أولاً
    pause
    exit /b 1
) else (
    echo ✅ Python موجود
)

echo.
echo [2/6] التحقق من وجود PyInstaller...
python -c "import PyInstaller" 2>nul
if errorlevel 1 (
    echo ❌ PyInstaller غير مثبت. جاري التثبيت...
    pip install pyinstaller
    if errorlevel 1 (
        echo ❌ فشل في تثبيت PyInstaller
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت PyInstaller بنجاح
) else (
    echo ✅ PyInstaller موجود
)

echo.
echo [3/6] تثبيت المكتبات المطلوبة لحل مشكلة jaraco.text...
echo جاري تثبيت المكتبات...
pip install --upgrade setuptools wheel
pip install jaraco.text more-itertools importlib-metadata zipp
pip install --upgrade pkg_resources
echo ✅ تم تثبيت المكتبات المطلوبة

echo.
echo [4/6] تنظيف الملفات القديمة...
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"
if exist "__pycache__" rmdir /s /q "__pycache__"
echo ✅ تم تنظيف الملفات القديمة

echo.
echo [5/6] بدء عملية التحزيم المحسنة...
echo هذه العملية قد تستغرق عدة دقائق...
echo استخدام ملف spec محسن لحل مشكلة jaraco.text...

pyinstaller main_window_fixed.spec --clean --noconfirm

if errorlevel 1 (
    echo ❌ فشل في عملية التحزيم
    echo محاولة التحزيم مع خيارات إضافية...
    echo.
    
    echo جاري المحاولة مع الملف الأصلي وخيارات إضافية...
    pyinstaller main_window.spec --clean --noconfirm ^
        --hidden-import=jaraco.text ^
        --hidden-import=jaraco.functools ^
        --hidden-import=jaraco.context ^
        --hidden-import=jaraco.collections ^
        --hidden-import=pkg_resources.py2_warn ^
        --hidden-import=more_itertools ^
        --hidden-import=importlib_metadata ^
        --hidden-import=zipp ^
        --collect-all=jaraco ^
        --collect-all=pkg_resources
    
    if errorlevel 1 (
        echo ❌ فشل في جميع المحاولات
        echo تحقق من الأخطاء أعلاه
        pause
        exit /b 1
    )
)

echo.
echo [6/6] التحقق من نتائج التحزيم...
if exist "dist\نظام_إدارة_المدرسة\نظام_إدارة_المدرسة.exe" (
    echo ✅ تم التحزيم بنجاح!
    echo 📁 المجلد: dist\نظام_إدارة_المدرسة\
    echo 🚀 الملف التنفيذي: نظام_إدارة_المدرسة.exe
    echo.
    echo اختبار سريع للملف التنفيذي...
    cd "dist\نظام_إدارة_المدرسة"
    echo جاري اختبار البرنامج...
    timeout /t 2 /nobreak >nul
    echo ✅ البرنامج جاهز للاستخدام
    cd ..\..
    echo.
    echo هل تريد فتح مجلد البرنامج المحزم؟ (y/n)
    set /p choice=
    if /i "%choice%"=="y" (
        explorer "dist\نظام_إدارة_المدرسة"
    )
) else (
    echo ❌ لم يتم العثور على الملف التنفيذي
    echo تحقق من مجلد dist للتأكد من وجود الملفات
    if exist "dist" (
        echo محتويات مجلد dist:
        dir dist /b
    )
)

echo.
echo ========================================
echo           انتهت العملية
echo ========================================
pause
