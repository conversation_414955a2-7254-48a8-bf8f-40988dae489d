# -*- mode: python ; coding: utf-8 -*-

"""
ملف تكوين PyInstaller لتحزيم تطبيق إدارة المدرسة
يضمن وضع قاعدة البيانات في مجلد التطبيق وليس في _internal
"""

import os
import sys
from pathlib import Path

# إعدادات التطبيق
APP_NAME = "SchoolManagement"
MAIN_SCRIPT = "main_window.py"

# المسارات
current_dir = os.path.dirname(os.path.abspath(SPEC))
icon_path = os.path.join(current_dir, "icon.ico") if os.path.exists(os.path.join(current_dir, "icon.ico")) else None

# الملفات المطلوبة
required_files = [
    "database_utils.py",
    "main_window.py",
    "sub232_window.py", 
    "sub252_window.py",
    "sub262_window.py",
    "monthly_duties_window.py",
    "multi_section_duties_window.py",
    "attendance_processing_window.py",
    "reports_window.py",
    "financial_reports_window.py"
]

# التحقق من وجود الملفات المطلوبة
missing_files = []
for file in required_files:
    if not os.path.exists(os.path.join(current_dir, file)):
        missing_files.append(file)

if missing_files:
    print(f"❌ الملفات التالية مفقودة: {missing_files}")
    sys.exit(1)

# إعداد التحليل
a = Analysis(
    [MAIN_SCRIPT],
    pathex=[current_dir],
    binaries=[],
    datas=[
        # إضافة ملف database_utils كبيانات
        ('database_utils.py', '.'),
    ],
    hiddenimports=[
        # إضافة الوحدات المخفية المطلوبة
        'database_utils',
        'sqlite3',
        'PyQt5.QtCore',
        'PyQt5.QtGui', 
        'PyQt5.QtWidgets',
        'PyQt5.QtPrintSupport',
        'jaraco.text',  # مطلوب لحل مشكلة PyInstaller
        'pkg_resources.py2_warn',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # استبعاد الوحدات غير المطلوبة لتقليل حجم التطبيق
        'tkinter',
        'matplotlib',
        'numpy',
        'pandas',
        'scipy',
        'PIL',
        'cv2',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# إعداد PYZ
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# إعداد EXE
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name=APP_NAME,
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # إخفاء نافذة وحدة التحكم
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=icon_path,
    # إعدادات إضافية لضمان عمل التطبيق بشكل صحيح
    version_file=None,
    uac_admin=False,
    uac_uiaccess=False,
)

# إعداد COLLECT (للتوزيع كمجلد)
# يمكن استخدام هذا بدلاً من onefile إذا كنت تريد توزيع التطبيق كمجلد
"""
coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name=APP_NAME,
)
"""

print("✅ تم إعداد ملف التكوين بنجاح!")
print(f"📱 اسم التطبيق: {APP_NAME}")
print(f"📄 الملف الرئيسي: {MAIN_SCRIPT}")
print(f"🎯 وضع التحزيم: ملف واحد (onefile)")
print(f"🖼️ الأيقونة: {'موجودة' if icon_path else 'غير موجودة'}")
print(f"💻 وحدة التحكم: مخفية")

# تعليمات الاستخدام
print("\n" + "="*60)
print("📋 تعليمات التحزيم:")
print("1. تأكد من تشغيل update_database_paths.py أولاً")
print("2. استخدم الأمر التالي للتحزيم:")
print(f"   pyinstaller {os.path.basename(__file__)}")
print("3. ستجد التطبيق المحزم في مجلد dist/")
print("4. قاعدة البيانات ستكون في نفس مجلد التطبيق")
print("="*60)
