#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""فحص قاعدة البيانات"""

import sqlite3

def check_database():
    """فحص قاعدة البيانات"""
    try:
        print("🔍 فحص قاعدة البيانات...")
        
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # فحص الجداول الموجودة
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print(f"📋 الجداول الموجودة ({len(tables)}):")
        for table in tables:
            print(f"   - {table[0]}")
            
        # فحص جدول monthly_duties
        if ('monthly_duties',) in tables:
            print("\n🔍 فحص جدول monthly_duties:")
            cursor.execute("SELECT COUNT(*) FROM monthly_duties")
            count = cursor.fetchone()[0]
            print(f"   📊 عدد السجلات: {count}")
            
            if count > 0:
                cursor.execute("""
                    SELECT month, year, COUNT(*) as count
                    FROM monthly_duties 
                    GROUP BY month, year
                    ORDER BY year DESC, month
                    LIMIT 5
                """)
                
                results = cursor.fetchall()
                print("   📋 عينة من البيانات:")
                for row in results:
                    print(f"      - {row[0]}/{row[1]}: {row[2]} سجل")
        else:
            print("❌ جدول monthly_duties غير موجود")
            
        # فحص جدول الحسابات المرحلة
        if ('الحسابات_المرحلة',) in tables:
            print("\n🔍 فحص جدول الحسابات_المرحلة:")
            cursor.execute("SELECT COUNT(*) FROM الحسابات_المرحلة")
            count = cursor.fetchone()[0]
            print(f"   📊 عدد السجلات: {count}")
        else:
            print("❌ جدول الحسابات_المرحلة غير موجود")
            
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_database()
