2025-06-10 00:19:16 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-10 00:19:16 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-10 00:19:16 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-10 00:19:16 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-10 00:19:16 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المجموعات
2025-06-10 00:19:16 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-10 00:19:16 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-10 00:19:16 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-10 00:19:16 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد والأقسام
2025-06-10 00:19:16 - SubjectsTeachersWindow - DEBUG - إضافة 4 مجموعة افتراضية
2025-06-10 00:19:16 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-10 00:19:16 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-10 00:19:16 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المجموعات: 4, المواد: 19, الأقسام: 50, الأساتذة: 0
2025-06-10 00:19:16 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-10 00:19:16 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المجموعات
2025-06-10 00:19:16 - SubjectsTeachersWindow - DEBUG - تم العثور على 4 مجموعة في قاعدة البيانات
2025-06-10 00:19:16 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المجموعات بنجاح - التفاصيل: تم تحميل 4 مجموعة
2025-06-10 00:19:16 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-10 00:19:16 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-10 00:19:16 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-10 00:19:16 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-10 00:19:16 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-10 00:19:16 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-10 00:19:16 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-10 00:19:16 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'قسم_id', 'نسبة_الواجبات', 'تاريخ_الاضافة', 'مجموعة_id']
2025-06-10 00:19:16 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-10 00:19:16 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-10 00:19:16 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-10 00:19:18 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-10 00:33:08 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-10 00:33:10 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-10 00:33:10 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-10 00:33:10 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-10 00:33:10 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المجموعات
2025-06-10 00:33:10 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-10 00:33:10 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-10 00:33:10 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-10 00:33:10 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد والأقسام
2025-06-10 00:33:10 - SubjectsTeachersWindow - DEBUG - إضافة 4 مجموعة افتراضية
2025-06-10 00:33:10 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-10 00:33:10 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-10 00:33:10 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المجموعات: 4, المواد: 19, الأقسام: 50, الأساتذة: 0
2025-06-10 00:33:10 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-10 00:33:10 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المجموعات
2025-06-10 00:33:10 - SubjectsTeachersWindow - DEBUG - تم العثور على 4 مجموعة في قاعدة البيانات
2025-06-10 00:33:10 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المجموعات بنجاح - التفاصيل: تم تحميل 4 مجموعة
2025-06-10 00:33:10 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-10 00:33:10 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-10 00:33:10 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-10 00:33:10 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-10 00:33:10 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-10 00:33:10 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-10 00:33:10 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-10 00:33:10 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'قسم_id', 'نسبة_الواجبات', 'تاريخ_الاضافة', 'مجموعة_id']
2025-06-10 00:33:10 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-10 00:33:10 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-10 00:33:10 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-10 00:33:14 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-10 00:33:45 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-10 00:33:46 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-10 00:33:47 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-10 00:33:47 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-10 00:33:47 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المجموعات
2025-06-10 00:33:47 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-10 00:33:47 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-10 00:33:47 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-10 00:33:47 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد والأقسام
2025-06-10 00:33:47 - SubjectsTeachersWindow - DEBUG - إضافة 4 مجموعة افتراضية
2025-06-10 00:33:47 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-10 00:33:47 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-10 00:33:47 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المجموعات: 4, المواد: 19, الأقسام: 50, الأساتذة: 0
2025-06-10 00:33:47 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-10 00:33:47 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المجموعات
2025-06-10 00:33:47 - SubjectsTeachersWindow - DEBUG - تم العثور على 4 مجموعة في قاعدة البيانات
2025-06-10 00:33:47 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المجموعات بنجاح - التفاصيل: تم تحميل 4 مجموعة
2025-06-10 00:33:47 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-10 00:33:47 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-10 00:33:47 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-10 00:33:47 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-10 00:33:47 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-10 00:33:47 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-10 00:33:47 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-10 00:33:47 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'قسم_id', 'نسبة_الواجبات', 'تاريخ_الاضافة', 'مجموعة_id']
2025-06-10 00:33:47 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-10 00:33:47 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-10 00:33:47 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-10 00:33:49 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-10 08:21:49 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-10 08:21:52 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-10 08:21:54 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-10 08:21:54 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-10 08:21:54 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-10 08:21:54 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المجموعات
2025-06-10 08:21:54 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-10 08:21:54 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-10 08:21:54 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-10 08:21:54 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد والأقسام
2025-06-10 08:21:54 - SubjectsTeachersWindow - DEBUG - إضافة 4 مجموعة افتراضية
2025-06-10 08:21:54 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-10 08:21:54 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-10 08:21:54 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المجموعات: 4, المواد: 19, الأقسام: 50, الأساتذة: 0
2025-06-10 08:21:54 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-10 08:21:54 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المجموعات
2025-06-10 08:21:54 - SubjectsTeachersWindow - DEBUG - تم العثور على 4 مجموعة في قاعدة البيانات
2025-06-10 08:21:54 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المجموعات بنجاح - التفاصيل: تم تحميل 4 مجموعة
2025-06-10 08:21:54 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-10 08:21:54 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-10 08:21:54 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-10 08:21:54 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-10 08:21:54 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-10 08:21:54 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-10 08:21:54 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-10 08:21:54 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'قسم_id', 'نسبة_الواجبات', 'تاريخ_الاضافة', 'مجموعة_id']
2025-06-10 08:21:54 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-10 08:21:54 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-10 08:21:54 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-10 08:21:56 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-10 09:13:39 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-10 09:13:39 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-10 09:13:39 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: c:\Users\<USER>\Desktop\taheri10\data.db
2025-06-10 09:13:39 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-10 09:13:39 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المجموعات
2025-06-10 09:13:39 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-10 09:13:39 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-10 09:13:39 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-10 09:13:39 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد والأقسام
2025-06-10 09:13:39 - SubjectsTeachersWindow - DEBUG - إضافة 4 مجموعة افتراضية
2025-06-10 09:13:39 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-10 09:13:39 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-10 09:13:39 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المجموعات: 4, المواد: 19, الأقسام: 50, الأساتذة: 0
2025-06-10 09:13:39 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-10 09:13:39 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المجموعات
2025-06-10 09:13:39 - SubjectsTeachersWindow - DEBUG - تم العثور على 4 مجموعة في قاعدة البيانات
2025-06-10 09:13:39 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المجموعات بنجاح - التفاصيل: تم تحميل 4 مجموعة
2025-06-10 09:13:39 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-10 09:13:39 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-10 09:13:39 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-10 09:13:39 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-10 09:13:39 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-10 09:13:39 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-10 09:13:39 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-10 09:13:39 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'قسم_id', 'نسبة_الواجبات', 'تاريخ_الاضافة', 'مجموعة_id']
2025-06-10 09:13:39 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-10 09:13:39 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-10 09:13:39 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-10 09:16:04 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-10 09:16:04 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-10 09:16:04 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: c:\Users\<USER>\Desktop\taheri10\data.db
2025-06-10 09:16:04 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-10 09:16:04 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المجموعات
2025-06-10 09:16:04 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-10 09:16:04 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-10 09:16:04 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-10 09:16:04 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد والأقسام
2025-06-10 09:16:04 - SubjectsTeachersWindow - DEBUG - إضافة 4 مجموعة افتراضية
2025-06-10 09:16:04 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-10 09:16:04 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-10 09:16:04 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المجموعات: 4, المواد: 19, الأقسام: 50, الأساتذة: 0
2025-06-10 09:16:04 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-10 09:16:04 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المجموعات
2025-06-10 09:16:04 - SubjectsTeachersWindow - DEBUG - تم العثور على 4 مجموعة في قاعدة البيانات
2025-06-10 09:16:04 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المجموعات بنجاح - التفاصيل: تم تحميل 4 مجموعة
2025-06-10 09:16:04 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-10 09:16:04 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-10 09:16:04 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-10 09:16:04 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-10 09:16:04 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-10 09:16:04 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-10 09:16:04 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-10 09:16:04 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'قسم_id', 'نسبة_الواجبات', 'تاريخ_الاضافة', 'مجموعة_id']
2025-06-10 09:16:04 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-10 09:16:04 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-10 09:16:04 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-10 09:48:20 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-10 09:48:20 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-10 09:48:20 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: c:\Users\<USER>\Desktop\taheri10\data.db
2025-06-10 09:48:20 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-10 09:48:20 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المجموعات
2025-06-10 09:48:20 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-10 09:48:20 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-10 09:48:20 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-10 09:48:20 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد والأقسام
2025-06-10 09:48:20 - SubjectsTeachersWindow - DEBUG - إضافة 4 مجموعة افتراضية
2025-06-10 09:48:20 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-10 09:48:20 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-10 09:48:20 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المجموعات: 4, المواد: 19, الأقسام: 50, الأساتذة: 0
2025-06-10 09:48:20 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-10 09:48:20 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المجموعات
2025-06-10 09:48:20 - SubjectsTeachersWindow - DEBUG - تم العثور على 4 مجموعة في قاعدة البيانات
2025-06-10 09:48:20 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المجموعات بنجاح - التفاصيل: تم تحميل 4 مجموعة
2025-06-10 09:48:20 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-10 09:48:20 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-10 09:48:20 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-10 09:48:20 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-10 09:48:20 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-10 09:48:20 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-10 09:48:20 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-10 09:48:20 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'قسم_id', 'نسبة_الواجبات', 'تاريخ_الاضافة', 'مجموعة_id']
2025-06-10 09:48:20 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-10 09:48:20 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-10 09:48:20 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-10 09:51:10 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-10 09:51:10 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-10 09:51:10 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: c:\Users\<USER>\Desktop\taheri10\data.db
2025-06-10 09:51:10 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-10 09:51:10 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المجموعات
2025-06-10 09:51:10 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-10 09:51:10 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-10 09:51:10 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-10 09:51:10 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد والأقسام
2025-06-10 09:51:10 - SubjectsTeachersWindow - DEBUG - إضافة 4 مجموعة افتراضية
2025-06-10 09:51:10 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-10 09:51:10 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-10 09:51:10 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المجموعات: 4, المواد: 19, الأقسام: 50, الأساتذة: 0
2025-06-10 09:51:10 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-10 09:51:10 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المجموعات
2025-06-10 09:51:10 - SubjectsTeachersWindow - DEBUG - تم العثور على 4 مجموعة في قاعدة البيانات
2025-06-10 09:51:10 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المجموعات بنجاح - التفاصيل: تم تحميل 4 مجموعة
2025-06-10 09:51:10 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-10 09:51:10 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-10 09:51:10 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-10 09:51:10 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-10 09:51:10 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-10 09:51:10 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-10 09:51:10 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-10 09:51:10 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'قسم_id', 'نسبة_الواجبات', 'تاريخ_الاضافة', 'مجموعة_id']
2025-06-10 09:51:10 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-10 09:51:10 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-10 09:51:10 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-10 09:51:29 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-10 09:51:30 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-10 09:51:30 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: c:\Users\<USER>\Desktop\taheri10\data.db
2025-06-10 09:51:30 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-10 09:51:30 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المجموعات
2025-06-10 09:51:30 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-10 09:51:30 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-10 09:51:30 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-10 09:51:30 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد والأقسام
2025-06-10 09:51:30 - SubjectsTeachersWindow - DEBUG - إضافة 4 مجموعة افتراضية
2025-06-10 09:51:30 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-10 09:51:30 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-10 09:51:30 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المجموعات: 4, المواد: 19, الأقسام: 50, الأساتذة: 0
2025-06-10 09:51:30 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-10 09:51:30 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المجموعات
2025-06-10 09:51:30 - SubjectsTeachersWindow - DEBUG - تم العثور على 4 مجموعة في قاعدة البيانات
2025-06-10 09:51:30 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المجموعات بنجاح - التفاصيل: تم تحميل 4 مجموعة
2025-06-10 09:51:30 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-10 09:51:30 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-10 09:51:30 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-10 09:51:30 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-10 09:51:30 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-10 09:51:30 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-10 09:51:30 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-10 09:51:30 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'قسم_id', 'نسبة_الواجبات', 'تاريخ_الاضافة', 'مجموعة_id']
2025-06-10 09:51:30 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-10 09:51:30 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-10 09:51:30 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-10 09:57:40 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-10 09:57:40 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-10 09:57:40 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-10 09:57:40 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-10 09:57:40 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المجموعات
2025-06-10 09:57:40 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-10 09:57:40 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-10 09:57:40 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-10 09:57:40 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد والأقسام
2025-06-10 09:57:40 - SubjectsTeachersWindow - DEBUG - إضافة 4 مجموعة افتراضية
2025-06-10 09:57:40 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-10 09:57:40 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-10 09:57:40 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المجموعات: 4, المواد: 19, الأقسام: 50, الأساتذة: 0
2025-06-10 09:57:40 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-10 09:57:40 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المجموعات
2025-06-10 09:57:40 - SubjectsTeachersWindow - DEBUG - تم العثور على 4 مجموعة في قاعدة البيانات
2025-06-10 09:57:40 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المجموعات بنجاح - التفاصيل: تم تحميل 4 مجموعة
2025-06-10 09:57:40 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-10 09:57:40 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-10 09:57:40 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-10 09:57:40 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-10 09:57:40 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-10 09:57:40 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-10 09:57:40 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-10 09:57:40 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'قسم_id', 'نسبة_الواجبات', 'تاريخ_الاضافة', 'مجموعة_id']
2025-06-10 09:57:40 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-10 09:57:40 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-10 09:57:40 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-10 09:57:43 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-10 09:58:13 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-10 09:58:13 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-10 09:58:13 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-10 09:58:13 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-10 09:58:13 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المجموعات
2025-06-10 09:58:13 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-10 09:58:13 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-10 09:58:13 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-10 09:58:13 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد والأقسام
2025-06-10 09:58:13 - SubjectsTeachersWindow - DEBUG - إضافة 4 مجموعة افتراضية
2025-06-10 09:58:13 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-10 09:58:13 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-10 09:58:13 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المجموعات: 4, المواد: 19, الأقسام: 50, الأساتذة: 0
2025-06-10 09:58:13 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-10 09:58:13 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المجموعات
2025-06-10 09:58:13 - SubjectsTeachersWindow - DEBUG - تم العثور على 4 مجموعة في قاعدة البيانات
2025-06-10 09:58:13 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المجموعات بنجاح - التفاصيل: تم تحميل 4 مجموعة
2025-06-10 09:58:13 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-10 09:58:13 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-10 09:58:13 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-10 09:58:13 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-10 09:58:13 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-10 09:58:13 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-10 09:58:13 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-10 09:58:13 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'قسم_id', 'نسبة_الواجبات', 'تاريخ_الاضافة', 'مجموعة_id']
2025-06-10 09:58:13 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-10 09:58:13 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-10 09:58:13 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-10 10:20:36 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-10 10:20:36 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-10 10:20:36 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: c:\Users\<USER>\Desktop\taheri10\data.db
2025-06-10 10:20:36 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-10 10:20:36 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المجموعات
2025-06-10 10:20:36 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-10 10:20:36 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-10 10:20:36 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-10 10:20:36 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد والأقسام
2025-06-10 10:20:36 - SubjectsTeachersWindow - DEBUG - إضافة 4 مجموعة افتراضية
2025-06-10 10:20:36 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-10 10:20:36 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-10 10:20:36 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المجموعات: 4, المواد: 19, الأقسام: 50, الأساتذة: 0
2025-06-10 10:20:36 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-10 10:20:36 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المجموعات
2025-06-10 10:20:36 - SubjectsTeachersWindow - DEBUG - تم العثور على 4 مجموعة في قاعدة البيانات
2025-06-10 10:20:36 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المجموعات بنجاح - التفاصيل: تم تحميل 4 مجموعة
2025-06-10 10:20:36 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-10 10:20:36 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-10 10:20:36 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-10 10:20:36 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-10 10:20:36 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-10 10:20:36 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-10 10:20:36 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-10 10:20:36 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'قسم_id', 'نسبة_الواجبات', 'تاريخ_الاضافة', 'مجموعة_id']
2025-06-10 10:20:36 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-10 10:20:36 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-10 10:20:36 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-10 10:22:10 - SubjectsTeachersWindow - ERROR - خطأ في إضافة القسم آليا: no such table: الأقسام
2025-06-10 10:22:10 - SubjectsTeachersWindow - DEBUG - تفاصيل الخطأ:
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\taheri10\sub262_window.py", line 2597, in auto_add_section
    cursor.execute("""
    ~~~~~~~~~~~~~~^^^^
        SELECT اسم_القسم FROM الأقسام
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        WHERE اسم_القسم LIKE ?
        ^^^^^^^^^^^^^^^^^^^^^^
        ORDER BY اسم_القسم DESC
        ^^^^^^^^^^^^^^^^^^^^^^^
    """, (f"{section_name}%",))
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such table: الأقسام

2025-06-10 10:22:52 - SubjectsTeachersWindow - INFO - العملية: محاولة إضافة أستاذ جديد - التفاصيل: الأستاذ: محمد الطاهري
2025-06-10 10:22:52 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-10 10:22:52 - SubjectsTeachersWindow - INFO - العملية: تم إضافة الأستاذ بنجاح - التفاصيل: الأستاذ: محمد الطاهري - القسم: قسم / 01
2025-06-10 10:55:44 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-10 10:55:44 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-10 10:55:44 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-10 10:55:44 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-10 10:55:44 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المجموعات
2025-06-10 10:55:44 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-10 10:55:44 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-10 10:55:44 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-10 10:55:44 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد والأقسام
2025-06-10 10:55:44 - SubjectsTeachersWindow - DEBUG - إضافة 4 مجموعة افتراضية
2025-06-10 10:55:44 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-10 10:55:44 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-10 10:55:44 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المجموعات: 4, المواد: 19, الأقسام: 50, الأساتذة: 0
2025-06-10 10:55:44 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-10 10:55:44 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المجموعات
2025-06-10 10:55:44 - SubjectsTeachersWindow - DEBUG - تم العثور على 4 مجموعة في قاعدة البيانات
2025-06-10 10:55:44 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المجموعات بنجاح - التفاصيل: تم تحميل 4 مجموعة
2025-06-10 10:55:44 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-10 10:55:44 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-10 10:55:44 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-10 10:55:44 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-10 10:55:44 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-10 10:55:44 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-10 10:55:44 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-10 10:55:44 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'قسم_id', 'نسبة_الواجبات', 'تاريخ_الاضافة', 'مجموعة_id']
2025-06-10 10:55:44 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-10 10:55:44 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-10 10:55:44 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-10 10:55:50 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-10 10:56:31 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-10 10:56:31 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-10 10:56:31 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-10 10:56:31 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-10 10:56:31 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المجموعات
2025-06-10 10:56:31 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-10 10:56:31 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-10 10:56:31 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-10 10:56:31 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد والأقسام
2025-06-10 10:56:31 - SubjectsTeachersWindow - DEBUG - إضافة 4 مجموعة افتراضية
2025-06-10 10:56:31 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-10 10:56:31 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-10 10:56:31 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المجموعات: 4, المواد: 19, الأقسام: 50, الأساتذة: 0
2025-06-10 10:56:31 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-10 10:56:31 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المجموعات
2025-06-10 10:56:31 - SubjectsTeachersWindow - DEBUG - تم العثور على 4 مجموعة في قاعدة البيانات
2025-06-10 10:56:31 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المجموعات بنجاح - التفاصيل: تم تحميل 4 مجموعة
2025-06-10 10:56:31 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-10 10:56:31 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-10 10:56:31 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-10 10:56:31 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-10 10:56:31 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-10 10:56:31 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-10 10:56:31 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-10 10:56:31 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'قسم_id', 'نسبة_الواجبات', 'تاريخ_الاضافة', 'مجموعة_id']
2025-06-10 10:56:31 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-10 10:56:31 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-10 10:56:31 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-10 10:56:41 - SubjectsTeachersWindow - INFO - العملية: محاولة إضافة قسم جديد - التفاصيل: اسم القسم: 88888
2025-06-10 10:56:41 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-10 10:56:41 - SubjectsTeachersWindow - DEBUG - تم العثور على 51 قسم في قاعدة البيانات
2025-06-10 10:56:41 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 51 قسم
2025-06-10 10:56:41 - SubjectsTeachersWindow - INFO - العملية: تم إضافة القسم بنجاح - التفاصيل: القسم: 88888
2025-06-10 10:56:47 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-10 10:56:48 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-10 10:56:49 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-10 10:56:49 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-10 10:56:49 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-10 10:56:49 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المجموعات
2025-06-10 10:56:49 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-10 10:56:49 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-10 10:56:49 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-10 10:56:49 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد والأقسام
2025-06-10 10:56:49 - SubjectsTeachersWindow - DEBUG - إضافة 4 مجموعة افتراضية
2025-06-10 10:56:49 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-10 10:56:49 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-10 10:56:49 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المجموعات: 4, المواد: 19, الأقسام: 51, الأساتذة: 0
2025-06-10 10:56:49 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-10 10:56:49 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المجموعات
2025-06-10 10:56:49 - SubjectsTeachersWindow - DEBUG - تم العثور على 4 مجموعة في قاعدة البيانات
2025-06-10 10:56:49 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المجموعات بنجاح - التفاصيل: تم تحميل 4 مجموعة
2025-06-10 10:56:49 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-10 10:56:49 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-10 10:56:49 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-10 10:56:49 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-10 10:56:49 - SubjectsTeachersWindow - DEBUG - تم العثور على 51 قسم في قاعدة البيانات
2025-06-10 10:56:49 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 51 قسم
2025-06-10 10:56:49 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-10 10:56:49 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'قسم_id', 'نسبة_الواجبات', 'تاريخ_الاضافة', 'مجموعة_id']
2025-06-10 10:56:49 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-10 10:56:49 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-10 10:56:49 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-10 10:57:00 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-10 11:03:10 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-10 11:03:11 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-10 11:03:11 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-10 11:03:11 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-10 11:03:11 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المجموعات
2025-06-10 11:03:11 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-10 11:03:11 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-10 11:03:11 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-10 11:03:11 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد والأقسام
2025-06-10 11:03:11 - SubjectsTeachersWindow - DEBUG - إضافة 4 مجموعة افتراضية
2025-06-10 11:03:11 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-10 11:03:11 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-10 11:03:11 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المجموعات: 4, المواد: 19, الأقسام: 50, الأساتذة: 0
2025-06-10 11:03:11 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-10 11:03:11 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المجموعات
2025-06-10 11:03:11 - SubjectsTeachersWindow - DEBUG - تم العثور على 4 مجموعة في قاعدة البيانات
2025-06-10 11:03:11 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المجموعات بنجاح - التفاصيل: تم تحميل 4 مجموعة
2025-06-10 11:03:11 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-10 11:03:11 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-10 11:03:11 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-10 11:03:11 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-10 11:03:11 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-10 11:03:11 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-10 11:03:11 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-10 11:03:11 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'قسم_id', 'نسبة_الواجبات', 'تاريخ_الاضافة', 'مجموعة_id']
2025-06-10 11:03:11 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-10 11:03:11 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-10 11:03:11 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-10 11:03:18 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-10 11:03:18 - SubjectsTeachersWindow - DEBUG - تم العثور على 51 قسم في قاعدة البيانات
2025-06-10 11:03:18 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 51 قسم
2025-06-10 11:03:20 - SubjectsTeachersWindow - INFO - العملية: إضافة قسم آليا - التفاصيل: تم إضافة: قسم / 51
2025-06-10 11:24:12 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-10 11:24:12 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-10 11:24:12 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: c:\Users\<USER>\Desktop\taheri10\data.db
2025-06-10 11:24:12 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-10 11:24:12 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المجموعات
2025-06-10 11:24:12 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-10 11:24:12 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-10 11:24:12 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-10 11:24:12 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد والأقسام
2025-06-10 11:24:12 - SubjectsTeachersWindow - DEBUG - إضافة 4 مجموعة افتراضية
2025-06-10 11:24:12 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-10 11:24:12 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-10 11:24:12 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المجموعات: 4, المواد: 19, الأقسام: 51, الأساتذة: 0
2025-06-10 11:24:12 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-10 11:24:12 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المجموعات
2025-06-10 11:24:12 - SubjectsTeachersWindow - DEBUG - تم العثور على 4 مجموعة في قاعدة البيانات
2025-06-10 11:24:12 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المجموعات بنجاح - التفاصيل: تم تحميل 4 مجموعة
2025-06-10 11:24:12 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-10 11:24:12 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-10 11:24:12 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-10 11:24:12 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-10 11:24:12 - SubjectsTeachersWindow - DEBUG - تم العثور على 51 قسم في قاعدة البيانات
2025-06-10 11:24:12 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 51 قسم
2025-06-10 11:24:12 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-10 11:24:12 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'قسم_id', 'نسبة_الواجبات', 'تاريخ_الاضافة', 'مجموعة_id']
2025-06-10 11:24:12 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-10 11:24:12 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-10 11:24:12 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-10 11:24:36 - SubjectsTeachersWindow - ERROR - خطأ في إنشاء التقرير: arguments did not match any overloaded call:
  drawPixmap(self, targetRect: QRectF, pixmap: QPixmap, sourceRect: QRectF): argument 1 has unexpected type 'QRect'
  drawPixmap(self, targetRect: QRect, pixmap: QPixmap, sourceRect: QRect): argument 2 has unexpected type 'str'
  drawPixmap(self, p: Union[QPointF, QPoint], pm: QPixmap): argument 1 has unexpected type 'QRect'
  drawPixmap(self, p: QPoint, pm: QPixmap): argument 1 has unexpected type 'QRect'
  drawPixmap(self, r: QRect, pm: QPixmap): argument 2 has unexpected type 'str'
  drawPixmap(self, x: int, y: int, pm: QPixmap): argument 1 has unexpected type 'QRect'
  drawPixmap(self, x: int, y: int, w: int, h: int, pm: QPixmap): argument 1 has unexpected type 'QRect'
  drawPixmap(self, x: int, y: int, w: int, h: int, pm: QPixmap, sx: int, sy: int, sw: int, sh: int): argument 1 has unexpected type 'QRect'
  drawPixmap(self, x: int, y: int, pm: QPixmap, sx: int, sy: int, sw: int, sh: int): argument 1 has unexpected type 'QRect'
  drawPixmap(self, p: Union[QPointF, QPoint], pm: QPixmap, sr: QRectF): argument 1 has unexpected type 'QRect'
  drawPixmap(self, p: QPoint, pm: QPixmap, sr: QRect): argument 1 has unexpected type 'QRect'
2025-06-10 11:24:36 - SubjectsTeachersWindow - DEBUG - تفاصيل الخطأ:
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\taheri10\sub262_window.py", line 2704, in generate_teachers_report
    painter.drawPixmap(logo_rect, logo_path)
    ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
TypeError: arguments did not match any overloaded call:
  drawPixmap(self, targetRect: QRectF, pixmap: QPixmap, sourceRect: QRectF): argument 1 has unexpected type 'QRect'
  drawPixmap(self, targetRect: QRect, pixmap: QPixmap, sourceRect: QRect): argument 2 has unexpected type 'str'
  drawPixmap(self, p: Union[QPointF, QPoint], pm: QPixmap): argument 1 has unexpected type 'QRect'
  drawPixmap(self, p: QPoint, pm: QPixmap): argument 1 has unexpected type 'QRect'
  drawPixmap(self, r: QRect, pm: QPixmap): argument 2 has unexpected type 'str'
  drawPixmap(self, x: int, y: int, pm: QPixmap): argument 1 has unexpected type 'QRect'
  drawPixmap(self, x: int, y: int, w: int, h: int, pm: QPixmap): argument 1 has unexpected type 'QRect'
  drawPixmap(self, x: int, y: int, w: int, h: int, pm: QPixmap, sx: int, sy: int, sw: int, sh: int): argument 1 has unexpected type 'QRect'
  drawPixmap(self, x: int, y: int, pm: QPixmap, sx: int, sy: int, sw: int, sh: int): argument 1 has unexpected type 'QRect'
  drawPixmap(self, p: Union[QPointF, QPoint], pm: QPixmap, sr: QRectF): argument 1 has unexpected type 'QRect'
  drawPixmap(self, p: QPoint, pm: QPixmap, sr: QRect): argument 1 has unexpected type 'QRect'

2025-06-10 11:25:21 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-10 11:25:22 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-10 11:25:22 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-10 11:25:22 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-10 11:25:22 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المجموعات
2025-06-10 11:25:22 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-10 11:25:22 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-10 11:25:22 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-10 11:25:22 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد والأقسام
2025-06-10 11:25:22 - SubjectsTeachersWindow - DEBUG - إضافة 4 مجموعة افتراضية
2025-06-10 11:25:22 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-10 11:25:22 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-10 11:25:22 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المجموعات: 4, المواد: 19, الأقسام: 51, الأساتذة: 0
2025-06-10 11:25:22 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-10 11:25:22 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المجموعات
2025-06-10 11:25:22 - SubjectsTeachersWindow - DEBUG - تم العثور على 4 مجموعة في قاعدة البيانات
2025-06-10 11:25:22 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المجموعات بنجاح - التفاصيل: تم تحميل 4 مجموعة
2025-06-10 11:25:22 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-10 11:25:22 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-10 11:25:22 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-10 11:25:22 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-10 11:25:22 - SubjectsTeachersWindow - DEBUG - تم العثور على 51 قسم في قاعدة البيانات
2025-06-10 11:25:22 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 51 قسم
2025-06-10 11:25:22 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-10 11:25:22 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'قسم_id', 'نسبة_الواجبات', 'تاريخ_الاضافة', 'مجموعة_id']
2025-06-10 11:25:22 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-10 11:25:22 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-10 11:25:22 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-10 11:25:31 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-10 11:25:35 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-10 11:25:36 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-10 11:25:36 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-10 11:25:36 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-10 11:25:36 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المجموعات
2025-06-10 11:25:36 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-10 11:25:36 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-10 11:25:36 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-10 11:25:36 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد والأقسام
2025-06-10 11:25:36 - SubjectsTeachersWindow - DEBUG - إضافة 4 مجموعة افتراضية
2025-06-10 11:25:36 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-10 11:25:36 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-10 11:25:36 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المجموعات: 4, المواد: 19, الأقسام: 51, الأساتذة: 0
2025-06-10 11:25:36 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-10 11:25:36 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المجموعات
2025-06-10 11:25:36 - SubjectsTeachersWindow - DEBUG - تم العثور على 4 مجموعة في قاعدة البيانات
2025-06-10 11:25:36 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المجموعات بنجاح - التفاصيل: تم تحميل 4 مجموعة
2025-06-10 11:25:36 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-10 11:25:36 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-10 11:25:36 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-10 11:25:36 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-10 11:25:36 - SubjectsTeachersWindow - DEBUG - تم العثور على 51 قسم في قاعدة البيانات
2025-06-10 11:25:36 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 51 قسم
2025-06-10 11:25:36 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-10 11:25:36 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'قسم_id', 'نسبة_الواجبات', 'تاريخ_الاضافة', 'مجموعة_id']
2025-06-10 11:25:36 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-10 11:25:36 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-10 11:25:36 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-10 11:25:44 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-10 11:25:48 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-10 11:25:49 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-10 11:25:49 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-10 11:25:49 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-10 11:25:49 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المجموعات
2025-06-10 11:25:49 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-10 11:25:49 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-10 11:25:49 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-10 11:25:49 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد والأقسام
2025-06-10 11:25:49 - SubjectsTeachersWindow - DEBUG - إضافة 4 مجموعة افتراضية
2025-06-10 11:25:49 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-10 11:25:49 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-10 11:25:49 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المجموعات: 4, المواد: 19, الأقسام: 51, الأساتذة: 0
2025-06-10 11:25:49 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-10 11:25:49 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المجموعات
2025-06-10 11:25:49 - SubjectsTeachersWindow - DEBUG - تم العثور على 4 مجموعة في قاعدة البيانات
2025-06-10 11:25:49 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المجموعات بنجاح - التفاصيل: تم تحميل 4 مجموعة
2025-06-10 11:25:49 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-10 11:25:49 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-10 11:25:49 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-10 11:25:49 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-10 11:25:49 - SubjectsTeachersWindow - DEBUG - تم العثور على 51 قسم في قاعدة البيانات
2025-06-10 11:25:49 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 51 قسم
2025-06-10 11:25:49 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-10 11:25:49 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'قسم_id', 'نسبة_الواجبات', 'تاريخ_الاضافة', 'مجموعة_id']
2025-06-10 11:25:49 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-10 11:25:49 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-10 11:25:49 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-10 11:26:05 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-10 11:26:10 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-10 11:26:11 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-10 11:26:11 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-10 11:26:11 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-10 11:26:11 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المجموعات
2025-06-10 11:26:11 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-10 11:26:11 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-10 11:26:11 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-10 11:26:11 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد والأقسام
2025-06-10 11:26:11 - SubjectsTeachersWindow - DEBUG - إضافة 4 مجموعة افتراضية
2025-06-10 11:26:11 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-10 11:26:11 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-10 11:26:11 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المجموعات: 4, المواد: 19, الأقسام: 51, الأساتذة: 0
2025-06-10 11:26:11 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-10 11:26:11 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المجموعات
2025-06-10 11:26:11 - SubjectsTeachersWindow - DEBUG - تم العثور على 4 مجموعة في قاعدة البيانات
2025-06-10 11:26:11 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المجموعات بنجاح - التفاصيل: تم تحميل 4 مجموعة
2025-06-10 11:26:11 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-10 11:26:11 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-10 11:26:11 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-10 11:26:11 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-10 11:26:11 - SubjectsTeachersWindow - DEBUG - تم العثور على 51 قسم في قاعدة البيانات
2025-06-10 11:26:11 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 51 قسم
2025-06-10 11:26:11 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-10 11:26:11 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'قسم_id', 'نسبة_الواجبات', 'تاريخ_الاضافة', 'مجموعة_id']
2025-06-10 11:26:11 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-10 11:26:11 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-10 11:26:11 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-10 11:26:20 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-10 11:26:24 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-10 11:26:25 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-10 11:26:25 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-10 11:26:25 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-10 11:26:25 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المجموعات
2025-06-10 11:26:25 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-10 11:26:25 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-10 11:26:25 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-10 11:26:25 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد والأقسام
2025-06-10 11:26:25 - SubjectsTeachersWindow - DEBUG - إضافة 4 مجموعة افتراضية
2025-06-10 11:26:25 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-10 11:26:25 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-10 11:26:25 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المجموعات: 4, المواد: 19, الأقسام: 51, الأساتذة: 0
2025-06-10 11:26:25 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-10 11:26:25 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المجموعات
2025-06-10 11:26:25 - SubjectsTeachersWindow - DEBUG - تم العثور على 4 مجموعة في قاعدة البيانات
2025-06-10 11:26:25 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المجموعات بنجاح - التفاصيل: تم تحميل 4 مجموعة
2025-06-10 11:26:25 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-10 11:26:25 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-10 11:26:25 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-10 11:26:25 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-10 11:26:25 - SubjectsTeachersWindow - DEBUG - تم العثور على 51 قسم في قاعدة البيانات
2025-06-10 11:26:25 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 51 قسم
2025-06-10 11:26:25 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-10 11:26:25 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'قسم_id', 'نسبة_الواجبات', 'تاريخ_الاضافة', 'مجموعة_id']
2025-06-10 11:26:25 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-10 11:26:25 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-10 11:26:25 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-10 11:26:32 - SubjectsTeachersWindow - ERROR - خطأ في إنشاء التقرير: arguments did not match any overloaded call:
  drawPixmap(self, targetRect: QRectF, pixmap: QPixmap, sourceRect: QRectF): argument 1 has unexpected type 'QRect'
  drawPixmap(self, targetRect: QRect, pixmap: QPixmap, sourceRect: QRect): argument 2 has unexpected type 'str'
  drawPixmap(self, p: Union[QPointF, QPoint], pm: QPixmap): argument 1 has unexpected type 'QRect'
  drawPixmap(self, p: QPoint, pm: QPixmap): argument 1 has unexpected type 'QRect'
  drawPixmap(self, r: QRect, pm: QPixmap): argument 2 has unexpected type 'str'
  drawPixmap(self, x: int, y: int, pm: QPixmap): argument 1 has unexpected type 'QRect'
  drawPixmap(self, x: int, y: int, w: int, h: int, pm: QPixmap): argument 1 has unexpected type 'QRect'
  drawPixmap(self, x: int, y: int, w: int, h: int, pm: QPixmap, sx: int, sy: int, sw: int, sh: int): argument 1 has unexpected type 'QRect'
  drawPixmap(self, x: int, y: int, pm: QPixmap, sx: int, sy: int, sw: int, sh: int): argument 1 has unexpected type 'QRect'
  drawPixmap(self, p: Union[QPointF, QPoint], pm: QPixmap, sr: QRectF): argument 1 has unexpected type 'QRect'
  drawPixmap(self, p: QPoint, pm: QPixmap, sr: QRect): argument 1 has unexpected type 'QRect'
2025-06-10 11:26:32 - SubjectsTeachersWindow - DEBUG - تفاصيل الخطأ:
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\taheri10\sub262_window.py", line 2706, in generate_teachers_report
    painter.drawPixmap(logo_rect, logo_path)
    ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
TypeError: arguments did not match any overloaded call:
  drawPixmap(self, targetRect: QRectF, pixmap: QPixmap, sourceRect: QRectF): argument 1 has unexpected type 'QRect'
  drawPixmap(self, targetRect: QRect, pixmap: QPixmap, sourceRect: QRect): argument 2 has unexpected type 'str'
  drawPixmap(self, p: Union[QPointF, QPoint], pm: QPixmap): argument 1 has unexpected type 'QRect'
  drawPixmap(self, p: QPoint, pm: QPixmap): argument 1 has unexpected type 'QRect'
  drawPixmap(self, r: QRect, pm: QPixmap): argument 2 has unexpected type 'str'
  drawPixmap(self, x: int, y: int, pm: QPixmap): argument 1 has unexpected type 'QRect'
  drawPixmap(self, x: int, y: int, w: int, h: int, pm: QPixmap): argument 1 has unexpected type 'QRect'
  drawPixmap(self, x: int, y: int, w: int, h: int, pm: QPixmap, sx: int, sy: int, sw: int, sh: int): argument 1 has unexpected type 'QRect'
  drawPixmap(self, x: int, y: int, pm: QPixmap, sx: int, sy: int, sw: int, sh: int): argument 1 has unexpected type 'QRect'
  drawPixmap(self, p: Union[QPointF, QPoint], pm: QPixmap, sr: QRectF): argument 1 has unexpected type 'QRect'
  drawPixmap(self, p: QPoint, pm: QPixmap, sr: QRect): argument 1 has unexpected type 'QRect'

2025-06-10 11:26:44 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-10 11:26:45 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-10 11:26:45 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-10 11:26:45 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-10 11:26:45 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المجموعات
2025-06-10 11:26:45 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-10 11:26:45 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-10 11:26:45 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-10 11:26:45 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد والأقسام
2025-06-10 11:26:45 - SubjectsTeachersWindow - DEBUG - إضافة 4 مجموعة افتراضية
2025-06-10 11:26:45 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-10 11:26:45 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-10 11:26:45 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المجموعات: 4, المواد: 19, الأقسام: 51, الأساتذة: 0
2025-06-10 11:26:45 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-10 11:26:45 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المجموعات
2025-06-10 11:26:45 - SubjectsTeachersWindow - DEBUG - تم العثور على 4 مجموعة في قاعدة البيانات
2025-06-10 11:26:45 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المجموعات بنجاح - التفاصيل: تم تحميل 4 مجموعة
2025-06-10 11:26:45 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-10 11:26:45 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-10 11:26:45 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-10 11:26:45 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-10 11:26:45 - SubjectsTeachersWindow - DEBUG - تم العثور على 51 قسم في قاعدة البيانات
2025-06-10 11:26:45 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 51 قسم
2025-06-10 11:26:45 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-10 11:26:45 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'قسم_id', 'نسبة_الواجبات', 'تاريخ_الاضافة', 'مجموعة_id']
2025-06-10 11:26:45 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-10 11:26:45 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-10 11:26:45 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-10 11:26:52 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-10 11:27:00 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-10 11:27:00 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-10 11:27:00 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-10 11:27:00 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-10 11:27:00 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المجموعات
2025-06-10 11:27:00 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-10 11:27:00 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-10 11:27:00 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-10 11:27:00 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد والأقسام
2025-06-10 11:27:00 - SubjectsTeachersWindow - DEBUG - إضافة 4 مجموعة افتراضية
2025-06-10 11:27:00 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-10 11:27:00 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-10 11:27:00 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المجموعات: 4, المواد: 19, الأقسام: 51, الأساتذة: 0
2025-06-10 11:27:00 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-10 11:27:00 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المجموعات
2025-06-10 11:27:00 - SubjectsTeachersWindow - DEBUG - تم العثور على 4 مجموعة في قاعدة البيانات
2025-06-10 11:27:00 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المجموعات بنجاح - التفاصيل: تم تحميل 4 مجموعة
2025-06-10 11:27:00 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-10 11:27:00 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-10 11:27:00 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-10 11:27:00 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-10 11:27:00 - SubjectsTeachersWindow - DEBUG - تم العثور على 51 قسم في قاعدة البيانات
2025-06-10 11:27:00 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 51 قسم
2025-06-10 11:27:00 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-10 11:27:00 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'قسم_id', 'نسبة_الواجبات', 'تاريخ_الاضافة', 'مجموعة_id']
2025-06-10 11:27:00 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-10 11:27:00 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-10 11:27:00 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-10 11:27:07 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-10 11:27:12 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-10 11:27:13 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-10 11:27:13 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-10 11:27:13 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-10 11:27:13 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المجموعات
2025-06-10 11:27:13 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-10 11:27:13 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-10 11:27:13 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-10 11:27:13 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد والأقسام
2025-06-10 11:27:13 - SubjectsTeachersWindow - DEBUG - إضافة 4 مجموعة افتراضية
2025-06-10 11:27:13 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-10 11:27:13 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-10 11:27:13 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المجموعات: 4, المواد: 19, الأقسام: 51, الأساتذة: 0
2025-06-10 11:27:13 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-10 11:27:13 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المجموعات
2025-06-10 11:27:13 - SubjectsTeachersWindow - DEBUG - تم العثور على 4 مجموعة في قاعدة البيانات
2025-06-10 11:27:13 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المجموعات بنجاح - التفاصيل: تم تحميل 4 مجموعة
2025-06-10 11:27:13 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-10 11:27:13 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-10 11:27:13 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-10 11:27:13 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-10 11:27:13 - SubjectsTeachersWindow - DEBUG - تم العثور على 51 قسم في قاعدة البيانات
2025-06-10 11:27:13 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 51 قسم
2025-06-10 11:27:13 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-10 11:27:13 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'قسم_id', 'نسبة_الواجبات', 'تاريخ_الاضافة', 'مجموعة_id']
2025-06-10 11:27:13 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-10 11:27:13 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-10 11:27:13 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-10 11:27:24 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-10 11:27:42 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-10 11:27:42 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-10 11:27:42 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-10 11:27:42 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-10 11:27:42 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المجموعات
2025-06-10 11:27:42 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-10 11:27:42 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-10 11:27:42 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-10 11:27:42 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد والأقسام
2025-06-10 11:27:42 - SubjectsTeachersWindow - DEBUG - إضافة 4 مجموعة افتراضية
2025-06-10 11:27:42 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-10 11:27:42 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-10 11:27:42 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المجموعات: 4, المواد: 19, الأقسام: 51, الأساتذة: 0
2025-06-10 11:27:42 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-10 11:27:42 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المجموعات
2025-06-10 11:27:42 - SubjectsTeachersWindow - DEBUG - تم العثور على 4 مجموعة في قاعدة البيانات
2025-06-10 11:27:42 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المجموعات بنجاح - التفاصيل: تم تحميل 4 مجموعة
2025-06-10 11:27:42 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-10 11:27:42 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-10 11:27:42 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-10 11:27:42 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-10 11:27:42 - SubjectsTeachersWindow - DEBUG - تم العثور على 51 قسم في قاعدة البيانات
2025-06-10 11:27:42 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 51 قسم
2025-06-10 11:27:42 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-10 11:27:42 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'قسم_id', 'نسبة_الواجبات', 'تاريخ_الاضافة', 'مجموعة_id']
2025-06-10 11:27:42 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-10 11:27:42 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-10 11:27:42 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-10 11:27:56 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-10 11:28:09 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-10 11:28:09 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-10 11:28:09 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-10 11:28:09 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-10 11:28:09 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المجموعات
2025-06-10 11:28:09 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-10 11:28:09 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-10 11:28:09 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-10 11:28:09 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد والأقسام
2025-06-10 11:28:09 - SubjectsTeachersWindow - DEBUG - إضافة 4 مجموعة افتراضية
2025-06-10 11:28:09 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-10 11:28:09 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-10 11:28:09 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المجموعات: 4, المواد: 19, الأقسام: 51, الأساتذة: 0
2025-06-10 11:28:09 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-10 11:28:09 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المجموعات
2025-06-10 11:28:09 - SubjectsTeachersWindow - DEBUG - تم العثور على 4 مجموعة في قاعدة البيانات
2025-06-10 11:28:09 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المجموعات بنجاح - التفاصيل: تم تحميل 4 مجموعة
2025-06-10 11:28:09 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-10 11:28:09 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-10 11:28:09 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-10 11:28:09 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-10 11:28:09 - SubjectsTeachersWindow - DEBUG - تم العثور على 51 قسم في قاعدة البيانات
2025-06-10 11:28:09 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 51 قسم
2025-06-10 11:28:09 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-10 11:28:09 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'قسم_id', 'نسبة_الواجبات', 'تاريخ_الاضافة', 'مجموعة_id']
2025-06-10 11:28:09 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-10 11:28:09 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-10 11:28:09 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-10 11:28:16 - SubjectsTeachersWindow - ERROR - خطأ في إنشاء التقرير: arguments did not match any overloaded call:
  drawPixmap(self, targetRect: QRectF, pixmap: QPixmap, sourceRect: QRectF): argument 1 has unexpected type 'QRect'
  drawPixmap(self, targetRect: QRect, pixmap: QPixmap, sourceRect: QRect): argument 2 has unexpected type 'str'
  drawPixmap(self, p: Union[QPointF, QPoint], pm: QPixmap): argument 1 has unexpected type 'QRect'
  drawPixmap(self, p: QPoint, pm: QPixmap): argument 1 has unexpected type 'QRect'
  drawPixmap(self, r: QRect, pm: QPixmap): argument 2 has unexpected type 'str'
  drawPixmap(self, x: int, y: int, pm: QPixmap): argument 1 has unexpected type 'QRect'
  drawPixmap(self, x: int, y: int, w: int, h: int, pm: QPixmap): argument 1 has unexpected type 'QRect'
  drawPixmap(self, x: int, y: int, w: int, h: int, pm: QPixmap, sx: int, sy: int, sw: int, sh: int): argument 1 has unexpected type 'QRect'
  drawPixmap(self, x: int, y: int, pm: QPixmap, sx: int, sy: int, sw: int, sh: int): argument 1 has unexpected type 'QRect'
  drawPixmap(self, p: Union[QPointF, QPoint], pm: QPixmap, sr: QRectF): argument 1 has unexpected type 'QRect'
  drawPixmap(self, p: QPoint, pm: QPixmap, sr: QRect): argument 1 has unexpected type 'QRect'
2025-06-10 11:28:16 - SubjectsTeachersWindow - DEBUG - تفاصيل الخطأ:
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\taheri10\sub262_window.py", line 2706, in generate_teachers_report
    painter.drawPixmap(logo_rect, logo_path)
    ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
TypeError: arguments did not match any overloaded call:
  drawPixmap(self, targetRect: QRectF, pixmap: QPixmap, sourceRect: QRectF): argument 1 has unexpected type 'QRect'
  drawPixmap(self, targetRect: QRect, pixmap: QPixmap, sourceRect: QRect): argument 2 has unexpected type 'str'
  drawPixmap(self, p: Union[QPointF, QPoint], pm: QPixmap): argument 1 has unexpected type 'QRect'
  drawPixmap(self, p: QPoint, pm: QPixmap): argument 1 has unexpected type 'QRect'
  drawPixmap(self, r: QRect, pm: QPixmap): argument 2 has unexpected type 'str'
  drawPixmap(self, x: int, y: int, pm: QPixmap): argument 1 has unexpected type 'QRect'
  drawPixmap(self, x: int, y: int, w: int, h: int, pm: QPixmap): argument 1 has unexpected type 'QRect'
  drawPixmap(self, x: int, y: int, w: int, h: int, pm: QPixmap, sx: int, sy: int, sw: int, sh: int): argument 1 has unexpected type 'QRect'
  drawPixmap(self, x: int, y: int, pm: QPixmap, sx: int, sy: int, sw: int, sh: int): argument 1 has unexpected type 'QRect'
  drawPixmap(self, p: Union[QPointF, QPoint], pm: QPixmap, sr: QRectF): argument 1 has unexpected type 'QRect'
  drawPixmap(self, p: QPoint, pm: QPixmap, sr: QRect): argument 1 has unexpected type 'QRect'

2025-06-10 11:28:33 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-10 11:28:33 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-10 11:28:33 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-10 11:28:33 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-10 11:28:33 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المجموعات
2025-06-10 11:28:33 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-10 11:28:33 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-10 11:28:33 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-10 11:28:33 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد والأقسام
2025-06-10 11:28:33 - SubjectsTeachersWindow - DEBUG - إضافة 4 مجموعة افتراضية
2025-06-10 11:28:33 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-10 11:28:33 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-10 11:28:33 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المجموعات: 4, المواد: 19, الأقسام: 51, الأساتذة: 0
2025-06-10 11:28:33 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-10 11:28:33 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المجموعات
2025-06-10 11:28:33 - SubjectsTeachersWindow - DEBUG - تم العثور على 4 مجموعة في قاعدة البيانات
2025-06-10 11:28:33 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المجموعات بنجاح - التفاصيل: تم تحميل 4 مجموعة
2025-06-10 11:28:33 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-10 11:28:33 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-10 11:28:33 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-10 11:28:33 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-10 11:28:33 - SubjectsTeachersWindow - DEBUG - تم العثور على 51 قسم في قاعدة البيانات
2025-06-10 11:28:33 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 51 قسم
2025-06-10 11:28:33 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-10 11:28:33 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'قسم_id', 'نسبة_الواجبات', 'تاريخ_الاضافة', 'مجموعة_id']
2025-06-10 11:28:33 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-10 11:28:33 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-10 11:28:33 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-10 11:28:38 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-10 11:28:38 - SubjectsTeachersWindow - DEBUG - تم العثور على 52 قسم في قاعدة البيانات
2025-06-10 11:28:38 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 52 قسم
2025-06-10 11:28:40 - SubjectsTeachersWindow - INFO - العملية: إضافة قسم آليا - التفاصيل: تم إضافة: قسم / 52
2025-06-10 11:28:45 - SubjectsTeachersWindow - ERROR - خطأ في إنشاء التقرير: arguments did not match any overloaded call:
  drawPixmap(self, targetRect: QRectF, pixmap: QPixmap, sourceRect: QRectF): argument 1 has unexpected type 'QRect'
  drawPixmap(self, targetRect: QRect, pixmap: QPixmap, sourceRect: QRect): argument 2 has unexpected type 'str'
  drawPixmap(self, p: Union[QPointF, QPoint], pm: QPixmap): argument 1 has unexpected type 'QRect'
  drawPixmap(self, p: QPoint, pm: QPixmap): argument 1 has unexpected type 'QRect'
  drawPixmap(self, r: QRect, pm: QPixmap): argument 2 has unexpected type 'str'
  drawPixmap(self, x: int, y: int, pm: QPixmap): argument 1 has unexpected type 'QRect'
  drawPixmap(self, x: int, y: int, w: int, h: int, pm: QPixmap): argument 1 has unexpected type 'QRect'
  drawPixmap(self, x: int, y: int, w: int, h: int, pm: QPixmap, sx: int, sy: int, sw: int, sh: int): argument 1 has unexpected type 'QRect'
  drawPixmap(self, x: int, y: int, pm: QPixmap, sx: int, sy: int, sw: int, sh: int): argument 1 has unexpected type 'QRect'
  drawPixmap(self, p: Union[QPointF, QPoint], pm: QPixmap, sr: QRectF): argument 1 has unexpected type 'QRect'
  drawPixmap(self, p: QPoint, pm: QPixmap, sr: QRect): argument 1 has unexpected type 'QRect'
2025-06-10 11:28:45 - SubjectsTeachersWindow - DEBUG - تفاصيل الخطأ:
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\taheri10\sub262_window.py", line 2706, in generate_teachers_report
    painter.drawPixmap(logo_rect, logo_path)
    ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
TypeError: arguments did not match any overloaded call:
  drawPixmap(self, targetRect: QRectF, pixmap: QPixmap, sourceRect: QRectF): argument 1 has unexpected type 'QRect'
  drawPixmap(self, targetRect: QRect, pixmap: QPixmap, sourceRect: QRect): argument 2 has unexpected type 'str'
  drawPixmap(self, p: Union[QPointF, QPoint], pm: QPixmap): argument 1 has unexpected type 'QRect'
  drawPixmap(self, p: QPoint, pm: QPixmap): argument 1 has unexpected type 'QRect'
  drawPixmap(self, r: QRect, pm: QPixmap): argument 2 has unexpected type 'str'
  drawPixmap(self, x: int, y: int, pm: QPixmap): argument 1 has unexpected type 'QRect'
  drawPixmap(self, x: int, y: int, w: int, h: int, pm: QPixmap): argument 1 has unexpected type 'QRect'
  drawPixmap(self, x: int, y: int, w: int, h: int, pm: QPixmap, sx: int, sy: int, sw: int, sh: int): argument 1 has unexpected type 'QRect'
  drawPixmap(self, x: int, y: int, pm: QPixmap, sx: int, sy: int, sw: int, sh: int): argument 1 has unexpected type 'QRect'
  drawPixmap(self, p: Union[QPointF, QPoint], pm: QPixmap, sr: QRectF): argument 1 has unexpected type 'QRect'
  drawPixmap(self, p: QPoint, pm: QPixmap, sr: QRect): argument 1 has unexpected type 'QRect'

2025-06-10 11:29:04 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-10 11:29:05 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-10 11:29:05 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-10 11:29:05 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-10 11:29:05 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المجموعات
2025-06-10 11:29:05 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-10 11:29:05 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-10 11:29:05 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-10 11:29:05 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد والأقسام
2025-06-10 11:29:05 - SubjectsTeachersWindow - DEBUG - إضافة 4 مجموعة افتراضية
2025-06-10 11:29:05 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-10 11:29:05 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-10 11:29:05 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المجموعات: 4, المواد: 19, الأقسام: 52, الأساتذة: 0
2025-06-10 11:29:05 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-10 11:29:05 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المجموعات
2025-06-10 11:29:05 - SubjectsTeachersWindow - DEBUG - تم العثور على 4 مجموعة في قاعدة البيانات
2025-06-10 11:29:05 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المجموعات بنجاح - التفاصيل: تم تحميل 4 مجموعة
2025-06-10 11:29:05 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-10 11:29:05 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-10 11:29:05 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-10 11:29:05 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-10 11:29:05 - SubjectsTeachersWindow - DEBUG - تم العثور على 52 قسم في قاعدة البيانات
2025-06-10 11:29:05 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 52 قسم
2025-06-10 11:29:05 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-10 11:29:05 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'قسم_id', 'نسبة_الواجبات', 'تاريخ_الاضافة', 'مجموعة_id']
2025-06-10 11:29:05 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-10 11:29:05 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-10 11:29:05 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-10 11:29:21 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-10 11:29:21 - SubjectsTeachersWindow - DEBUG - تم العثور على 53 قسم في قاعدة البيانات
2025-06-10 11:29:21 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 53 قسم
2025-06-10 11:29:23 - SubjectsTeachersWindow - INFO - العملية: إضافة قسم آليا - التفاصيل: تم إضافة: قسم / 53
2025-06-10 11:29:58 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-10 11:41:10 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-10 11:41:11 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-10 11:41:11 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: c:\Users\<USER>\Desktop\taheri10\data.db
2025-06-10 11:41:11 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-10 11:41:11 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المجموعات
2025-06-10 11:41:11 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-10 11:41:11 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-10 11:41:11 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-10 11:41:11 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد والأقسام
2025-06-10 11:41:11 - SubjectsTeachersWindow - DEBUG - إضافة 4 مجموعة افتراضية
2025-06-10 11:41:11 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-10 11:41:11 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-10 11:41:11 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المجموعات: 4, المواد: 19, الأقسام: 53, الأساتذة: 0
2025-06-10 11:41:11 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-10 11:41:11 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المجموعات
2025-06-10 11:41:11 - SubjectsTeachersWindow - DEBUG - تم العثور على 4 مجموعة في قاعدة البيانات
2025-06-10 11:41:11 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المجموعات بنجاح - التفاصيل: تم تحميل 4 مجموعة
2025-06-10 11:41:11 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-10 11:41:11 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-10 11:41:11 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-10 11:41:11 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-10 11:41:11 - SubjectsTeachersWindow - DEBUG - تم العثور على 53 قسم في قاعدة البيانات
2025-06-10 11:41:11 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 53 قسم
2025-06-10 11:41:11 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-10 11:41:11 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'قسم_id', 'نسبة_الواجبات', 'تاريخ_الاضافة', 'مجموعة_id']
2025-06-10 11:41:11 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-10 11:41:11 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-10 11:41:11 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-10 11:44:13 - SubjectsTeachersWindow - INFO - العملية: محاولة إضافة أستاذ جديد - التفاصيل: الأستاذ: حسن الطاهري
2025-06-10 11:44:18 - SubjectsTeachersWindow - INFO - العملية: محاولة إضافة أستاذ جديد - التفاصيل: الأستاذ: حسن الطاهري
2025-06-10 11:44:18 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-10 11:44:18 - SubjectsTeachersWindow - INFO - العملية: تم إضافة الأستاذ بنجاح - التفاصيل: الأستاذ: حسن الطاهري - القسم: قسم / 02
2025-06-10 12:03:50 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-10 12:03:51 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-10 12:03:51 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: c:\Users\<USER>\Desktop\taheri10\data.db
2025-06-10 12:03:51 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-10 12:03:51 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المجموعات
2025-06-10 12:03:51 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-10 12:03:51 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-10 12:03:51 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-10 12:03:51 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد والأقسام
2025-06-10 12:03:51 - SubjectsTeachersWindow - DEBUG - إضافة 4 مجموعة افتراضية
2025-06-10 12:03:51 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-10 12:03:51 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-10 12:03:51 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المجموعات: 4, المواد: 19, الأقسام: 50, الأساتذة: 0
2025-06-10 12:03:51 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-10 12:03:51 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المجموعات
2025-06-10 12:03:51 - SubjectsTeachersWindow - DEBUG - تم العثور على 4 مجموعة في قاعدة البيانات
2025-06-10 12:03:51 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المجموعات بنجاح - التفاصيل: تم تحميل 4 مجموعة
2025-06-10 12:03:51 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-10 12:03:51 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-10 12:03:51 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-10 12:03:51 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-10 12:03:51 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-10 12:03:51 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-10 12:03:51 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-10 12:03:51 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'قسم_id', 'نسبة_الواجبات', 'تاريخ_الاضافة', 'مجموعة_id']
2025-06-10 12:03:51 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-10 12:03:51 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-10 12:03:51 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-10 12:04:21 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-10 12:04:21 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-10 12:04:21 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: c:\Users\<USER>\Desktop\taheri10\data.db
2025-06-10 12:04:21 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-10 12:04:21 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المجموعات
2025-06-10 12:04:21 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-10 12:04:21 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-10 12:04:21 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-10 12:04:21 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد والأقسام
2025-06-10 12:04:21 - SubjectsTeachersWindow - DEBUG - إضافة 4 مجموعة افتراضية
2025-06-10 12:04:21 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-10 12:04:21 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-10 12:04:21 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المجموعات: 4, المواد: 19, الأقسام: 50, الأساتذة: 0
2025-06-10 12:04:21 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-10 12:04:21 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المجموعات
2025-06-10 12:04:21 - SubjectsTeachersWindow - DEBUG - تم العثور على 4 مجموعة في قاعدة البيانات
2025-06-10 12:04:21 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المجموعات بنجاح - التفاصيل: تم تحميل 4 مجموعة
2025-06-10 12:04:21 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-10 12:04:21 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-10 12:04:21 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-10 12:04:21 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-10 12:04:21 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-10 12:04:21 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-10 12:04:21 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-10 12:04:21 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'قسم_id', 'نسبة_الواجبات', 'تاريخ_الاضافة', 'مجموعة_id']
2025-06-10 12:04:21 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-10 12:04:21 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-10 12:04:21 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-10 12:10:01 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-10 12:10:01 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-10 12:10:01 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: c:\Users\<USER>\Desktop\taheri10\data.db
2025-06-10 12:10:01 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-10 12:10:01 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المجموعات
2025-06-10 12:10:01 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-10 12:10:01 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-10 12:10:01 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-10 12:10:01 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد والأقسام
2025-06-10 12:10:01 - SubjectsTeachersWindow - DEBUG - إضافة 4 مجموعة افتراضية
2025-06-10 12:10:01 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-10 12:10:01 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-10 12:10:01 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المجموعات: 4, المواد: 19, الأقسام: 50, الأساتذة: 0
2025-06-10 12:10:01 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-10 12:10:01 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المجموعات
2025-06-10 12:10:01 - SubjectsTeachersWindow - DEBUG - تم العثور على 4 مجموعة في قاعدة البيانات
2025-06-10 12:10:01 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المجموعات بنجاح - التفاصيل: تم تحميل 4 مجموعة
2025-06-10 12:10:01 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-10 12:10:01 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-10 12:10:01 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-10 12:10:01 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-10 12:10:01 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-10 12:10:01 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-10 12:10:01 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-10 12:10:01 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'قسم_id', 'نسبة_الواجبات', 'تاريخ_الاضافة', 'مجموعة_id']
2025-06-10 12:10:01 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-10 12:10:01 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-10 12:10:01 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-10 12:11:56 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-10 12:11:56 - SubjectsTeachersWindow - DEBUG - تم العثور على 51 قسم في قاعدة البيانات
2025-06-10 12:11:56 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 51 قسم
2025-06-10 12:11:57 - SubjectsTeachersWindow - INFO - العملية: إضافة قسم آليا - التفاصيل: تم إضافة: قسم / 51
2025-06-10 12:12:29 - SubjectsTeachersWindow - INFO - العملية: محاولة إضافة أستاذ جديد - التفاصيل: الأستاذ: محمد الطاهري
2025-06-10 12:12:29 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-10 12:12:29 - SubjectsTeachersWindow - INFO - العملية: تم إضافة الأستاذ بنجاح - التفاصيل: الأستاذ: محمد الطاهري - القسم: قسم / 01
2025-06-10 12:12:50 - SubjectsTeachersWindow - INFO - العملية: محاولة إضافة أستاذ جديد - التفاصيل: الأستاذ: حسن الطاهري
2025-06-10 12:12:55 - SubjectsTeachersWindow - INFO - العملية: محاولة إضافة أستاذ جديد - التفاصيل: الأستاذ: حسن الطاهري
2025-06-10 12:12:55 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-10 12:12:55 - SubjectsTeachersWindow - INFO - العملية: تم إضافة الأستاذ بنجاح - التفاصيل: الأستاذ: حسن الطاهري - القسم: قسم / 02
2025-06-10 12:13:15 - SubjectsTeachersWindow - INFO - العملية: محاولة إضافة أستاذ جديد - التفاصيل: الأستاذ: اسامة
2025-06-10 12:13:19 - SubjectsTeachersWindow - INFO - العملية: محاولة إضافة أستاذ جديد - التفاصيل: الأستاذ: اسامة
2025-06-10 12:13:19 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-10 12:13:19 - SubjectsTeachersWindow - INFO - العملية: تم إضافة الأستاذ بنجاح - التفاصيل: الأستاذ: اسامة - القسم: قسم / 03
2025-06-10 12:13:52 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-10 12:47:45 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-10 12:47:46 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-10 12:47:46 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-10 12:47:46 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-10 12:47:46 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المجموعات
2025-06-10 12:47:46 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-10 12:47:46 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-10 12:47:46 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-10 12:47:46 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد والأقسام
2025-06-10 12:47:46 - SubjectsTeachersWindow - DEBUG - إضافة 4 مجموعة افتراضية
2025-06-10 12:47:46 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-10 12:47:46 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-10 12:47:46 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المجموعات: 4, المواد: 19, الأقسام: 51, الأساتذة: 0
2025-06-10 12:47:46 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-10 12:47:46 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المجموعات
2025-06-10 12:47:46 - SubjectsTeachersWindow - DEBUG - تم العثور على 4 مجموعة في قاعدة البيانات
2025-06-10 12:47:46 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المجموعات بنجاح - التفاصيل: تم تحميل 4 مجموعة
2025-06-10 12:47:46 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-10 12:47:46 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-10 12:47:46 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-10 12:47:46 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-10 12:47:46 - SubjectsTeachersWindow - DEBUG - تم العثور على 51 قسم في قاعدة البيانات
2025-06-10 12:47:46 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 51 قسم
2025-06-10 12:47:46 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-10 12:47:46 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'قسم_id', 'نسبة_الواجبات', 'تاريخ_الاضافة', 'مجموعة_id']
2025-06-10 12:47:46 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-10 12:47:46 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-10 12:47:46 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-10 12:47:49 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-10 13:32:06 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-10 13:32:06 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-10 13:32:06 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: c:\Users\<USER>\Desktop\taheri10\data.db
2025-06-10 13:32:06 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-10 13:32:06 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المجموعات
2025-06-10 13:32:06 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-10 13:32:06 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-10 13:32:06 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-10 13:32:06 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد والأقسام
2025-06-10 13:32:06 - SubjectsTeachersWindow - DEBUG - إضافة 4 مجموعة افتراضية
2025-06-10 13:32:06 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-10 13:32:06 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-10 13:32:06 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المجموعات: 4, المواد: 19, الأقسام: 51, الأساتذة: 0
2025-06-10 13:32:06 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-10 13:32:06 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المجموعات
2025-06-10 13:32:06 - SubjectsTeachersWindow - DEBUG - تم العثور على 4 مجموعة في قاعدة البيانات
2025-06-10 13:32:06 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المجموعات بنجاح - التفاصيل: تم تحميل 4 مجموعة
2025-06-10 13:32:06 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-10 13:32:06 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-10 13:32:06 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-10 13:32:06 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-10 13:32:06 - SubjectsTeachersWindow - DEBUG - تم العثور على 51 قسم في قاعدة البيانات
2025-06-10 13:32:06 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 51 قسم
2025-06-10 13:32:06 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-10 13:32:06 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'قسم_id', 'نسبة_الواجبات', 'تاريخ_الاضافة', 'مجموعة_id']
2025-06-10 13:32:06 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-10 13:32:06 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-10 13:32:06 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-10 13:36:00 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-10 13:36:00 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-10 13:36:00 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: c:\Users\<USER>\Desktop\taheri10\data.db
2025-06-10 13:36:00 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-10 13:36:00 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المجموعات
2025-06-10 13:36:00 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-10 13:36:00 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-10 13:36:00 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-10 13:36:00 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد والأقسام
2025-06-10 13:36:00 - SubjectsTeachersWindow - DEBUG - إضافة 4 مجموعة افتراضية
2025-06-10 13:36:00 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-10 13:36:00 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-10 13:36:00 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المجموعات: 4, المواد: 19, الأقسام: 51, الأساتذة: 0
2025-06-10 13:36:00 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-10 13:36:00 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المجموعات
2025-06-10 13:36:00 - SubjectsTeachersWindow - DEBUG - تم العثور على 4 مجموعة في قاعدة البيانات
2025-06-10 13:36:00 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المجموعات بنجاح - التفاصيل: تم تحميل 4 مجموعة
2025-06-10 13:36:00 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-10 13:36:00 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-10 13:36:00 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-10 13:36:00 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-10 13:36:00 - SubjectsTeachersWindow - DEBUG - تم العثور على 51 قسم في قاعدة البيانات
2025-06-10 13:36:00 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 51 قسم
2025-06-10 13:36:00 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-10 13:36:00 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'قسم_id', 'نسبة_الواجبات', 'تاريخ_الاضافة', 'مجموعة_id']
2025-06-10 13:36:00 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-10 13:36:00 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-10 13:36:00 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-10 13:50:43 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-10 13:50:44 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-10 13:50:44 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: c:\Users\<USER>\Desktop\taheri10\data.db
2025-06-10 13:50:44 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-10 13:50:44 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المجموعات
2025-06-10 13:50:44 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-10 13:50:44 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-10 13:50:44 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-10 13:50:44 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد والأقسام
2025-06-10 13:50:44 - SubjectsTeachersWindow - DEBUG - إضافة 4 مجموعة افتراضية
2025-06-10 13:50:44 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-10 13:50:44 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-10 13:50:44 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المجموعات: 4, المواد: 19, الأقسام: 51, الأساتذة: 0
2025-06-10 13:50:44 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-10 13:50:44 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المجموعات
2025-06-10 13:50:44 - SubjectsTeachersWindow - DEBUG - تم العثور على 4 مجموعة في قاعدة البيانات
2025-06-10 13:50:44 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المجموعات بنجاح - التفاصيل: تم تحميل 4 مجموعة
2025-06-10 13:50:44 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-10 13:50:44 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-10 13:50:44 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-10 13:50:44 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-10 13:50:44 - SubjectsTeachersWindow - DEBUG - تم العثور على 51 قسم في قاعدة البيانات
2025-06-10 13:50:44 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 51 قسم
2025-06-10 13:50:44 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-10 13:50:44 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'قسم_id', 'نسبة_الواجبات', 'تاريخ_الاضافة', 'مجموعة_id']
2025-06-10 13:50:44 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-10 13:50:44 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-10 13:50:44 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-10 13:52:36 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-10 13:52:36 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-10 13:52:36 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: c:\Users\<USER>\Desktop\taheri10\data.db
2025-06-10 13:52:36 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-10 13:52:36 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المجموعات
2025-06-10 13:52:36 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-10 13:52:36 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-10 13:52:36 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-10 13:52:36 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد والأقسام
2025-06-10 13:52:36 - SubjectsTeachersWindow - DEBUG - إضافة 4 مجموعة افتراضية
2025-06-10 13:52:36 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-10 13:52:36 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-10 13:52:36 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المجموعات: 4, المواد: 19, الأقسام: 51, الأساتذة: 0
2025-06-10 13:52:36 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-10 13:52:36 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المجموعات
2025-06-10 13:52:36 - SubjectsTeachersWindow - DEBUG - تم العثور على 4 مجموعة في قاعدة البيانات
2025-06-10 13:52:36 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المجموعات بنجاح - التفاصيل: تم تحميل 4 مجموعة
2025-06-10 13:52:36 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-10 13:52:36 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-10 13:52:36 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-10 13:52:36 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-10 13:52:36 - SubjectsTeachersWindow - DEBUG - تم العثور على 51 قسم في قاعدة البيانات
2025-06-10 13:52:36 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 51 قسم
2025-06-10 13:52:36 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-10 13:52:36 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'قسم_id', 'نسبة_الواجبات', 'تاريخ_الاضافة', 'مجموعة_id']
2025-06-10 13:52:36 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-10 13:52:36 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-10 13:52:36 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-10 14:35:26 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-10 14:35:26 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-10 14:35:27 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: c:\Users\<USER>\Desktop\taheri10\data.db
2025-06-10 14:35:27 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-10 14:35:27 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المجموعات
2025-06-10 14:35:27 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-10 14:35:27 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-10 14:35:27 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-10 14:35:27 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد والأقسام
2025-06-10 14:35:27 - SubjectsTeachersWindow - DEBUG - إضافة 4 مجموعة افتراضية
2025-06-10 14:35:27 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-10 14:35:27 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-10 14:35:27 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المجموعات: 4, المواد: 19, الأقسام: 51, الأساتذة: 0
2025-06-10 14:35:27 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-10 14:35:27 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المجموعات
2025-06-10 14:35:27 - SubjectsTeachersWindow - DEBUG - تم العثور على 4 مجموعة في قاعدة البيانات
2025-06-10 14:35:27 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المجموعات بنجاح - التفاصيل: تم تحميل 4 مجموعة
2025-06-10 14:35:27 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-10 14:35:27 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-10 14:35:27 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-10 14:35:27 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-10 14:35:27 - SubjectsTeachersWindow - DEBUG - تم العثور على 51 قسم في قاعدة البيانات
2025-06-10 14:35:27 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 51 قسم
2025-06-10 14:35:27 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-10 14:35:27 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'قسم_id', 'نسبة_الواجبات', 'تاريخ_الاضافة', 'مجموعة_id']
2025-06-10 14:35:27 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-10 14:35:27 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-10 14:35:27 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-10 15:02:05 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-10 15:02:05 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-10 15:02:05 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: c:\Users\<USER>\Desktop\taheri10\data.db
2025-06-10 15:02:05 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-10 15:02:05 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المجموعات
2025-06-10 15:02:05 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-10 15:02:05 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-10 15:02:05 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-10 15:02:05 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد والأقسام
2025-06-10 15:02:05 - SubjectsTeachersWindow - DEBUG - إضافة 4 مجموعة افتراضية
2025-06-10 15:02:05 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-10 15:02:05 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-10 15:02:05 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المجموعات: 4, المواد: 19, الأقسام: 51, الأساتذة: 0
2025-06-10 15:02:05 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-10 15:02:05 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المجموعات
2025-06-10 15:02:05 - SubjectsTeachersWindow - DEBUG - تم العثور على 4 مجموعة في قاعدة البيانات
2025-06-10 15:02:05 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المجموعات بنجاح - التفاصيل: تم تحميل 4 مجموعة
2025-06-10 15:02:05 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-10 15:02:05 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-10 15:02:05 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-10 15:02:05 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-10 15:02:05 - SubjectsTeachersWindow - DEBUG - تم العثور على 51 قسم في قاعدة البيانات
2025-06-10 15:02:05 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 51 قسم
2025-06-10 15:02:05 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-10 15:02:05 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'قسم_id', 'نسبة_الواجبات', 'تاريخ_الاضافة', 'مجموعة_id']
2025-06-10 15:02:05 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-10 15:02:05 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-10 15:02:05 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-10 15:03:02 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-10 15:03:05 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-10 15:03:05 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-10 15:03:05 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-10 15:03:05 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المجموعات
2025-06-10 15:03:05 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-10 15:03:05 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-10 15:03:05 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-10 15:03:05 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد والأقسام
2025-06-10 15:03:05 - SubjectsTeachersWindow - DEBUG - إضافة 4 مجموعة افتراضية
2025-06-10 15:03:05 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-10 15:03:05 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-10 15:03:05 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المجموعات: 4, المواد: 19, الأقسام: 51, الأساتذة: 0
2025-06-10 15:03:05 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-10 15:03:05 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المجموعات
2025-06-10 15:03:05 - SubjectsTeachersWindow - DEBUG - تم العثور على 4 مجموعة في قاعدة البيانات
2025-06-10 15:03:05 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المجموعات بنجاح - التفاصيل: تم تحميل 4 مجموعة
2025-06-10 15:03:05 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-10 15:03:05 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-10 15:03:05 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-10 15:03:05 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-10 15:03:05 - SubjectsTeachersWindow - DEBUG - تم العثور على 51 قسم في قاعدة البيانات
2025-06-10 15:03:05 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 51 قسم
2025-06-10 15:03:05 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-10 15:03:05 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'قسم_id', 'نسبة_الواجبات', 'تاريخ_الاضافة', 'مجموعة_id']
2025-06-10 15:03:05 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-10 15:03:05 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-10 15:03:05 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-10 15:03:07 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-10 21:42:05 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-10 21:42:06 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-10 21:42:06 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: c:\Users\<USER>\Desktop\taheri10\data.db
2025-06-10 21:42:06 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-10 21:42:06 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المجموعات
2025-06-10 21:42:06 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-10 21:42:06 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-10 21:42:06 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-10 21:42:06 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد والأقسام
2025-06-10 21:42:06 - SubjectsTeachersWindow - DEBUG - إضافة 4 مجموعة افتراضية
2025-06-10 21:42:06 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-10 21:42:06 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-10 21:42:06 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المجموعات: 4, المواد: 19, الأقسام: 51, الأساتذة: 0
2025-06-10 21:42:06 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-10 21:42:06 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المجموعات
2025-06-10 21:42:06 - SubjectsTeachersWindow - DEBUG - تم العثور على 4 مجموعة في قاعدة البيانات
2025-06-10 21:42:06 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المجموعات بنجاح - التفاصيل: تم تحميل 4 مجموعة
2025-06-10 21:42:06 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-10 21:42:06 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-10 21:42:06 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-10 21:42:06 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-10 21:42:06 - SubjectsTeachersWindow - DEBUG - تم العثور على 51 قسم في قاعدة البيانات
2025-06-10 21:42:06 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 51 قسم
2025-06-10 21:42:06 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-10 21:42:06 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'قسم_id', 'نسبة_الواجبات', 'تاريخ_الاضافة', 'مجموعة_id']
2025-06-10 21:42:06 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-10 21:42:06 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-10 21:42:06 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-10 21:42:20 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-10 21:42:20 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-10 21:42:20 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: c:\Users\<USER>\Desktop\taheri10\data.db
2025-06-10 21:42:20 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-10 21:42:20 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المجموعات
2025-06-10 21:42:20 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-10 21:42:20 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-10 21:42:20 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-10 21:42:20 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد والأقسام
2025-06-10 21:42:20 - SubjectsTeachersWindow - DEBUG - إضافة 4 مجموعة افتراضية
2025-06-10 21:42:20 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-10 21:42:20 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-10 21:42:20 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المجموعات: 4, المواد: 19, الأقسام: 51, الأساتذة: 0
2025-06-10 21:42:20 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-10 21:42:20 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المجموعات
2025-06-10 21:42:20 - SubjectsTeachersWindow - DEBUG - تم العثور على 4 مجموعة في قاعدة البيانات
2025-06-10 21:42:20 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المجموعات بنجاح - التفاصيل: تم تحميل 4 مجموعة
2025-06-10 21:42:20 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-10 21:42:20 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-10 21:42:20 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-10 21:42:20 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-10 21:42:20 - SubjectsTeachersWindow - DEBUG - تم العثور على 51 قسم في قاعدة البيانات
2025-06-10 21:42:20 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 51 قسم
2025-06-10 21:42:20 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-10 21:42:20 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'قسم_id', 'نسبة_الواجبات', 'تاريخ_الاضافة', 'مجموعة_id']
2025-06-10 21:42:20 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-10 21:42:20 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-10 21:42:20 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-10 21:42:53 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-10 21:42:53 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-10 21:42:53 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: c:\Users\<USER>\Desktop\taheri10\data.db
2025-06-10 21:42:53 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-10 21:42:53 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المجموعات
2025-06-10 21:42:53 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-10 21:42:53 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-10 21:42:53 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-10 21:42:53 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد والأقسام
2025-06-10 21:42:53 - SubjectsTeachersWindow - DEBUG - إضافة 4 مجموعة افتراضية
2025-06-10 21:42:53 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-10 21:42:53 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-10 21:42:53 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المجموعات: 4, المواد: 19, الأقسام: 51, الأساتذة: 0
2025-06-10 21:42:53 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-10 21:42:53 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المجموعات
2025-06-10 21:42:53 - SubjectsTeachersWindow - DEBUG - تم العثور على 4 مجموعة في قاعدة البيانات
2025-06-10 21:42:53 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المجموعات بنجاح - التفاصيل: تم تحميل 4 مجموعة
2025-06-10 21:42:53 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-10 21:42:53 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-10 21:42:53 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-10 21:42:53 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-10 21:42:53 - SubjectsTeachersWindow - DEBUG - تم العثور على 51 قسم في قاعدة البيانات
2025-06-10 21:42:53 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 51 قسم
2025-06-10 21:42:53 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-10 21:42:53 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'قسم_id', 'نسبة_الواجبات', 'تاريخ_الاضافة', 'مجموعة_id']
2025-06-10 21:42:53 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-10 21:42:53 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-10 21:42:53 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-10 21:43:35 - SubjectsTeachersWindow - INFO - العملية: محاولة إضافة أستاذ جديد - التفاصيل: الأستاذ: حسن الطاهري
2025-06-10 21:43:35 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-10 21:43:35 - SubjectsTeachersWindow - INFO - العملية: تم إضافة الأستاذ بنجاح - التفاصيل: الأستاذ: حسن الطاهري - القسم: قسم / 01
2025-06-10 21:43:59 - SubjectsTeachersWindow - INFO - العملية: محاولة إضافة أستاذ جديد - التفاصيل: الأستاذ: محمد الطاهري
2025-06-10 21:43:59 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-10 21:43:59 - SubjectsTeachersWindow - INFO - العملية: تم إضافة الأستاذ بنجاح - التفاصيل: الأستاذ: محمد الطاهري - القسم: قسم / 02
2025-06-10 21:44:52 - SubjectsTeachersWindow - INFO - العملية: محاولة إضافة أستاذ جديد - التفاصيل: الأستاذ: اسامة
2025-06-10 21:44:56 - SubjectsTeachersWindow - INFO - العملية: محاولة إضافة أستاذ جديد - التفاصيل: الأستاذ: اسامة
2025-06-10 21:44:56 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-10 21:44:56 - SubjectsTeachersWindow - INFO - العملية: تم إضافة الأستاذ بنجاح - التفاصيل: الأستاذ: اسامة - القسم: قسم / 03
2025-06-10 21:45:12 - SubjectsTeachersWindow - INFO - العملية: بدء حفظ بيانات الأساتذة في جدول منفصل
2025-06-10 21:53:34 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-10 21:53:34 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-10 21:53:35 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: c:\Users\<USER>\Desktop\taheri10\data.db
2025-06-10 21:53:35 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-10 21:53:35 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المجموعات
2025-06-10 21:53:35 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-10 21:53:35 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-10 21:53:35 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-10 21:53:35 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد والأقسام
2025-06-10 21:53:35 - SubjectsTeachersWindow - DEBUG - إضافة 4 مجموعة افتراضية
2025-06-10 21:53:35 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-10 21:53:35 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-10 21:53:35 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المجموعات: 4, المواد: 19, الأقسام: 51, الأساتذة: 0
2025-06-10 21:53:35 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-10 21:53:35 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المجموعات
2025-06-10 21:53:35 - SubjectsTeachersWindow - DEBUG - تم العثور على 4 مجموعة في قاعدة البيانات
2025-06-10 21:53:35 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المجموعات بنجاح - التفاصيل: تم تحميل 4 مجموعة
2025-06-10 21:53:35 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-10 21:53:35 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-10 21:53:35 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-10 21:53:35 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-10 21:53:35 - SubjectsTeachersWindow - DEBUG - تم العثور على 51 قسم في قاعدة البيانات
2025-06-10 21:53:35 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 51 قسم
2025-06-10 21:53:35 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-10 21:53:35 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'قسم_id', 'نسبة_الواجبات', 'تاريخ_الاضافة', 'مجموعة_id']
2025-06-10 21:53:35 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-10 21:53:35 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-10 21:53:35 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
