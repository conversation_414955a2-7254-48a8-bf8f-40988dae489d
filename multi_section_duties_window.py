#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
import sqlite3
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QTableWidget, QTableWidgetItem, QFrame, 
    QMessageBox, QHeaderView, QAbstractItemView, QComboBox,
    QLineEdit, QTextEdit, QDateEdit, QSpinBox, QGroupBox, QGridLayout,
    QTabWidget, QCheckBox, QDialog, QProgressDialog
)
from PyQt5.QtGui import QFont, QIcon, QColor, QPixmap
from PyQt5.QtCore import Qt, QDate, QTimer
from datetime import datetime, date
import json
import traceback

class MultiSectionDutiesWindow(QMainWindow):
    """نافذة إدارة الواجبات للتلاميذ متعددي الأقسام"""
    
    def __init__(self, parent=None, db_path="data.db", student_code=None):
        super().__init__(parent)
        print(f"🔍 [DEBUG] تهيئة نافذة الواجبات متعددة الأقسام...")
        print(f"🔍 [DEBUG] رمز التلميذ: {student_code}")
        print(f"🔍 [DEBUG] مسار قاعدة البيانات: {db_path}")

        self.db_path = db_path
        self.student_code = student_code
        self.student_sections = []  # قائمة بجميع أقسام التلميذ
        self.student_basic_info = None
        self.editing_duty_id = None
        self.current_section_id = None  # القسم المحدد حالياً

        # إعداد النافذة - فتح في كامل الشاشة
        self.setWindowTitle(f"إدارة التلاميذ متعددي الأقسام - رمز التلميذ: {student_code}")
        self.setLayoutDirection(Qt.RightToLeft)

        # تعيين الخط الافتراضي للنافذة - Calibri 14 أسود غامق
        default_font = QFont("Calibri", 14, QFont.Bold)
        self.setFont(default_font)

        # إعداد حجم النافذة أولاً ثم تكبيرها
        self.setGeometry(100, 100, 1200, 800)

        # فتح النافذة في كامل الشاشة - تطبيق مرة واحدة فقط
        QTimer.singleShot(100, self.showMaximized)

        try:
            print(f"🔍 [DEBUG] بدء إعداد واجهة المستخدم...")
            self.setupUI()
            print(f"✅ [SUCCESS] تم إعداد واجهة المستخدم بنجاح")

            print(f"🔍 [DEBUG] تحميل بيانات التلميذ...")
            self.load_student_sections()
            print(f"✅ [SUCCESS] تم تحميل بيانات التلميذ بنجاح")

            print(f"🔍 [DEBUG] تحميل بيانات الواجبات...")
            self.load_all_duties_data()
            print(f"✅ [SUCCESS] تم تحميل بيانات الواجبات بنجاح")
            
        except Exception as e:
            print(f"❌ [ERROR] خطأ في تهيئة نافذة الواجبات متعددة الأقسام: {str(e)}")
            traceback.print_exc()
            raise

    def setupUI(self):
        """إعداد واجهة المستخدم"""
        try:
            print(f"🔍 [DEBUG] بدء إعداد واجهة المستخدم...")
            
            # إعداد النافذة الرئيسية
            self.setWindowTitle("💰 إدارة الواجبات - التلاميذ متعددي الأقسام")
            self.setGeometry(100, 100, 1400, 900)
            self.setLayoutDirection(Qt.RightToLeft)
            
            # تطبيق نمط النافذة
            self.setStyleSheet("""
                QMainWindow {
                    background-color: #f8f9fa;
                }
                QTabWidget::pane {
                    border: 2px solid #dee2e6;
                    border-radius: 8px;
                    background-color: white;
                }
                QTabWidget::tab-bar {
                    alignment: center;
                }
                QTabBar::tab {
                    background-color: #e9ecef;
                    color: #495057;
                    padding: 12px 20px;
                    margin: 2px;
                    border-radius: 8px 8px 0px 0px;
                    font-weight: bold;
                    min-width: 150px;
                }
                QTabBar::tab:selected {
                    background-color: #007bff;
                    color: white;
                }
                QTabBar::tab:hover {
                    background-color: #6c757d;
                    color: white;
                }
            """)
            
            # إنشاء العنصر المركزي
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            
            # التخطيط الرئيسي
            main_layout = QVBoxLayout(central_widget)
            main_layout.setContentsMargins(15, 15, 15, 15)
            main_layout.setSpacing(10)
            
            # العنوان الرئيسي
            print(f"🔍 [DEBUG] إضافة العنوان الرئيسي...")
            title_label = QLabel("💰 إدارة الواجبات - التلاميذ متعددي الأقسام")
            title_label.setFont(QFont("Calibri", 16, QFont.Bold))
            title_label.setAlignment(Qt.AlignCenter)
            title_label.setMaximumHeight(60)
            title_label.setStyleSheet("""
                QLabel {
                    background: qlineargradient(
                        x1: 0, y1: 0, x2: 1, y2: 0,
                        stop: 0 #007bff,
                        stop: 0.5 #0056b3,
                        stop: 1 #007bff
                    );
                    color: white;
                    padding: 15px;
                    border-radius: 12px;
                    font-weight: bold;
                    margin-bottom: 10px;
                }
            """)
            main_layout.addWidget(title_label)
            
            # إنشاء التبويبات
            print(f"🔍 [DEBUG] إنشاء التبويبات...")
            self.create_tabs(main_layout)
            
            print(f"✅ [SUCCESS] تم إعداد واجهة المستخدم بنجاح")
            
        except Exception as e:
            print(f"❌ [ERROR] خطأ في إعداد واجهة المستخدم: {str(e)}")
            traceback.print_exc()
            raise

    def create_tabs(self, main_layout):
        """إنشاء التبويبات الرئيسية"""
        try:
            print(f"🔍 [DEBUG] إنشاء التبويبات الرئيسية...")
            
            # إنشاء عنصر التبويبات
            self.tab_widget = QTabWidget()
            
            # تطبيق خط التبويبات
            tab_font = QFont("Calibri", 14, QFont.Bold)
            self.tab_widget.setFont(tab_font)
            
            # التبويب الأول: معلومات التلميذ
            print(f"🔍 [DEBUG] إنشاء تبويب معلومات التلميذ...")
            self.student_info_tab = QWidget()
            self.create_student_info_tab()
            self.tab_widget.addTab(self.student_info_tab, "📋 معلومات التلميذ")
            
            # التبويب الثاني: إدارة الواجبات الشهرية
            print(f"🔍 [DEBUG] إنشاء تبويب الواجبات الشهرية...")
            self.monthly_duties_tab = QWidget()
            self.create_monthly_duties_tab()
            self.tab_widget.addTab(self.monthly_duties_tab, "💰 الواجبات الشهرية")
            
            # التبويب الثالث: إدارة واجبات التسجيل
            print(f"🔍 [DEBUG] إنشاء تبويب واجبات التسجيل...")
            self.registration_duties_tab = QWidget()
            self.create_registration_duties_tab()
            self.tab_widget.addTab(self.registration_duties_tab, "📝 واجبات التسجيل")
            
            # التبويب الرابع: التوصيلات الموحدة
            print(f"🔍 [DEBUG] إنشاء تبويب التوصيلات...")
            self.receipts_tab = QWidget()
            self.create_receipts_tab()
            self.tab_widget.addTab(self.receipts_tab, "🖨️ التوصيلات الموحدة")
            
            main_layout.addWidget(self.tab_widget)
            
            print(f"✅ [SUCCESS] تم إنشاء التبويبات بنجاح")
            
        except Exception as e:
            print(f"❌ [ERROR] خطأ في إنشاء التبويبات: {str(e)}")
            traceback.print_exc()
            raise

    def create_student_info_tab(self):
        """إنشاء تبويب معلومات التلميذ"""
        try:
            layout = QVBoxLayout(self.student_info_tab)
            
            # مجموعة المعلومات الأساسية
            basic_info_group = QGroupBox("📋 المعلومات الأساسية")
            basic_info_layout = QGridLayout(basic_info_group)
            
            # حقول المعلومات الأساسية
            self.student_name_label = QLabel("اسم التلميذ: ")
            self.student_code_label = QLabel(f"رمز التلميذ: {self.student_code}")
            self.student_phone_label = QLabel("رقم الهاتف: ")
            self.student_institution_label = QLabel("المؤسسة الأصلية: ")
            
            # تطبيق تنسيق للتسميات
            for label in [self.student_name_label, self.student_code_label, 
                         self.student_phone_label, self.student_institution_label]:
                label.setFont(QFont("Calibri", 12, QFont.Bold))
                label.setStyleSheet("color: #2c3e50; padding: 5px;")
            
            basic_info_layout.addWidget(self.student_name_label, 0, 0)
            basic_info_layout.addWidget(self.student_code_label, 0, 1)
            basic_info_layout.addWidget(self.student_phone_label, 1, 0)
            basic_info_layout.addWidget(self.student_institution_label, 1, 1)
            
            layout.addWidget(basic_info_group)
            
            # مجموعة الأقسام المسجل بها
            sections_group = QGroupBox("📚 الأقسام المسجل بها")
            sections_layout = QVBoxLayout(sections_group)
            
            # جدول الأقسام
            self.sections_table = QTableWidget()
            self.setup_sections_table()
            sections_layout.addWidget(self.sections_table)
            
            layout.addWidget(sections_group)
            
            print(f"✅ [SUCCESS] تم إنشاء تبويب معلومات التلميذ بنجاح")
            
        except Exception as e:
            print(f"❌ [ERROR] خطأ في إنشاء تبويب معلومات التلميذ: {str(e)}")
            traceback.print_exc()
            raise

    def setup_sections_table(self):
        """إعداد جدول الأقسام"""
        try:
            columns = ["ID", "القسم", "الأستاذ", "المادة", "النوع", "الواجب الشهري", "واجبات التسجيل"]
            self.sections_table.setColumnCount(len(columns))
            self.sections_table.setHorizontalHeaderLabels(columns)
            
            # تطبيق تنسيق الجدول - Calibri 13 أسود غامق
            self.sections_table.setFont(QFont("Calibri", 13, QFont.Bold))
            self.sections_table.setAlternatingRowColors(True)
            self.sections_table.setSelectionBehavior(QAbstractItemView.SelectRows)
            self.sections_table.setSelectionMode(QAbstractItemView.SingleSelection)
            
            # تعيين عرض الأعمدة
            header = self.sections_table.horizontalHeader()
            header.setStretchLastSection(True)
            
            # إخفاء عمود ID
            self.sections_table.setColumnHidden(0, True)
            
            print(f"✅ [SUCCESS] تم إعداد جدول الأقسام بنجاح")
            
        except Exception as e:
            print(f"❌ [ERROR] خطأ في إعداد جدول الأقسام: {str(e)}")
            traceback.print_exc()
            raise

    def load_student_sections(self):
        """تحميل جميع أقسام التلميذ"""
        try:
            print(f"🔍 [DEBUG] تحميل أقسام التلميذ برمز: {self.student_code}")
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # جلب جميع سجلات التلميذ من جميع الأقسام
            cursor.execute("""
                SELECT 
                    jb.id, jb.اسم_التلميذ, jb.رمز_التلميذ, jb.القسم, 
                    jb.رقم_الهاتف_الأول, jb.المؤسسة_الاصلية, jb.النوع,
                    jb.الواجب_الشهري, jb.اجمالي_مبلغ_التسجيل
                FROM جدول_البيانات jb
                WHERE jb.رمز_التلميذ = ?
                ORDER BY jb.القسم
            """, (self.student_code,))
            
            sections_data = cursor.fetchall()
            
            if not sections_data:
                QMessageBox.warning(self, "تحذير", f"لم يتم العثور على تلميذ برمز: {self.student_code}")
                conn.close()
                return
            
            # حفظ المعلومات الأساسية من أول سجل
            first_record = sections_data[0]
            self.student_basic_info = {
                'name': first_record[1],
                'code': first_record[2],
                'phone': first_record[4] or "",
                'institution': first_record[5] or "",
                'type': first_record[6] or ""
            }
            
            # تحديث التسميات
            self.student_name_label.setText(f"اسم التلميذ: {self.student_basic_info['name']}")
            self.student_phone_label.setText(f"رقم الهاتف: {self.student_basic_info['phone']}")
            self.student_institution_label.setText(f"المؤسسة الأصلية: {self.student_basic_info['institution']}")
            
            # حفظ بيانات الأقسام
            self.student_sections = []
            for record in sections_data:
                # جلب معلومات الأستاذ والمادة للقسم
                cursor.execute("""
                    SELECT اسم_الاستاذ, المادة
                    FROM جدول_المواد_والاقسام
                    WHERE القسم = ?
                    LIMIT 1
                """, (record[3],))
                
                teacher_subject = cursor.fetchone()
                teacher_name = teacher_subject[0] if teacher_subject else "غير محدد"
                subject_name = teacher_subject[1] if teacher_subject else "غير محدد"
                
                section_info = {
                    'id': record[0],
                    'section': record[3] or "غير محدد",
                    'teacher': teacher_name,
                    'subject': subject_name,
                    'type': record[6] or "غير محدد",
                    'monthly_duty': record[7] or 0,
                    'registration_fee': record[8] or 0
                }
                self.student_sections.append(section_info)
            
            conn.close()
            
            # تحديث جدول الأقسام
            self.update_sections_table()
            
            print(f"✅ [SUCCESS] تم تحميل {len(self.student_sections)} قسم للتلميذ")
            
        except Exception as e:
            print(f"❌ [ERROR] خطأ في تحميل أقسام التلميذ: {str(e)}")
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بيانات التلميذ: {str(e)}")

    def update_sections_table(self):
        """تحديث جدول الأقسام"""
        try:
            self.sections_table.setRowCount(len(self.student_sections))
            
            for row, section in enumerate(self.student_sections):
                self.sections_table.setItem(row, 0, QTableWidgetItem(str(section['id'])))
                self.sections_table.setItem(row, 1, QTableWidgetItem(section['section']))
                self.sections_table.setItem(row, 2, QTableWidgetItem(section['teacher']))
                self.sections_table.setItem(row, 3, QTableWidgetItem(section['subject']))
                self.sections_table.setItem(row, 4, QTableWidgetItem(section['type']))
                self.sections_table.setItem(row, 5, QTableWidgetItem(f"{section['monthly_duty']} درهم"))
                self.sections_table.setItem(row, 6, QTableWidgetItem(f"{section['registration_fee']} درهم"))
            
            print(f"✅ [SUCCESS] تم تحديث جدول الأقسام بنجاح")
            
        except Exception as e:
            print(f"❌ [ERROR] خطأ في تحديث جدول الأقسام: {str(e)}")
            traceback.print_exc()

    def create_monthly_duties_tab(self):
        """إنشاء تبويب الواجبات الشهرية"""
        try:
            layout = QVBoxLayout(self.monthly_duties_tab)

            # مجموعة اختيار القسم
            section_selection_group = QGroupBox("📚 اختيار القسم")
            section_selection_layout = QHBoxLayout(section_selection_group)

            section_label = QLabel("القسم:")
            section_label.setFont(QFont("Calibri", 12, QFont.Bold))

            self.section_combo = QComboBox()
            self.section_combo.setFont(QFont("Calibri", 12))
            self.section_combo.currentTextChanged.connect(self.on_section_changed)

            section_selection_layout.addWidget(section_label)
            section_selection_layout.addWidget(self.section_combo)
            section_selection_layout.addStretch()

            layout.addWidget(section_selection_group)

            # مجموعة إضافة واجب جديد
            add_duty_group = QGroupBox("➕ إضافة واجب شهري جديد")
            add_duty_layout = QGridLayout(add_duty_group)

            # حقول إدخال الواجب
            month_label = QLabel("الشهر:")
            month_label.setFont(QFont("Calibri", 12, QFont.Bold))
            self.month_combo = QComboBox()
            self.month_combo.setFont(QFont("Calibri", 12))
            months = ["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                     "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"]
            self.month_combo.addItems(months)

            year_label = QLabel("السنة:")
            year_label.setFont(QFont("Calibri", 12, QFont.Bold))
            self.year_spin = QSpinBox()
            self.year_spin.setFont(QFont("Calibri", 12))
            self.year_spin.setRange(2020, 2030)
            self.year_spin.setValue(datetime.now().year)

            amount_label = QLabel("المبلغ المطلوب:")
            amount_label.setFont(QFont("Calibri", 12, QFont.Bold))
            self.amount_input = QLineEdit()
            self.amount_input.setFont(QFont("Calibri", 12))
            self.amount_input.setPlaceholderText("مثال: 200")

            paid_label = QLabel("المبلغ المدفوع:")
            paid_label.setFont(QFont("Calibri", 12, QFont.Bold))
            self.paid_input = QLineEdit()
            self.paid_input.setFont(QFont("Calibri", 12))
            self.paid_input.setPlaceholderText("مثال: 200")

            payment_date_label = QLabel("تاريخ الدفع:")
            payment_date_label.setFont(QFont("Calibri", 12, QFont.Bold))
            self.payment_date = QDateEdit()
            self.payment_date.setFont(QFont("Calibri", 12))
            self.payment_date.setDate(QDate.currentDate())
            self.payment_date.setCalendarPopup(True)

            notes_label = QLabel("ملاحظات:")
            notes_label.setFont(QFont("Calibri", 12, QFont.Bold))
            self.notes_input = QLineEdit()
            self.notes_input.setFont(QFont("Calibri", 12))
            self.notes_input.setPlaceholderText("ملاحظات اختيارية...")

            # ترتيب الحقول
            add_duty_layout.addWidget(month_label, 0, 0)
            add_duty_layout.addWidget(self.month_combo, 0, 1)
            add_duty_layout.addWidget(year_label, 0, 2)
            add_duty_layout.addWidget(self.year_spin, 0, 3)

            add_duty_layout.addWidget(amount_label, 1, 0)
            add_duty_layout.addWidget(self.amount_input, 1, 1)
            add_duty_layout.addWidget(paid_label, 1, 2)
            add_duty_layout.addWidget(self.paid_input, 1, 3)

            add_duty_layout.addWidget(payment_date_label, 2, 0)
            add_duty_layout.addWidget(self.payment_date, 2, 1)
            add_duty_layout.addWidget(notes_label, 2, 2)
            add_duty_layout.addWidget(self.notes_input, 2, 3)

            # أزرار التحكم
            buttons_layout = QHBoxLayout()

            self.add_duty_btn = QPushButton("➕ إضافة الواجب")
            self.add_duty_btn.setFont(QFont("Calibri", 12, QFont.Bold))
            self.add_duty_btn.setStyleSheet("""
                QPushButton {
                    background-color: #28a745;
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 10px 20px;
                    margin: 5px;
                }
                QPushButton:hover {
                    background-color: #218838;
                }
            """)
            self.add_duty_btn.clicked.connect(self.add_monthly_duty)

            self.update_duty_btn = QPushButton("✏️ تحديث الواجب")
            self.update_duty_btn.setFont(QFont("Calibri", 12, QFont.Bold))
            self.update_duty_btn.setStyleSheet("""
                QPushButton {
                    background-color: #ffc107;
                    color: #212529;
                    border: none;
                    border-radius: 8px;
                    padding: 10px 20px;
                    margin: 5px;
                }
                QPushButton:hover {
                    background-color: #e0a800;
                }
            """)
            self.update_duty_btn.clicked.connect(self.update_monthly_duty)
            self.update_duty_btn.setVisible(False)

            self.cancel_edit_btn = QPushButton("❌ إلغاء التعديل")
            self.cancel_edit_btn.setFont(QFont("Calibri", 12, QFont.Bold))
            self.cancel_edit_btn.setStyleSheet("""
                QPushButton {
                    background-color: #dc3545;
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 10px 20px;
                    margin: 5px;
                }
                QPushButton:hover {
                    background-color: #c82333;
                }
            """)
            self.cancel_edit_btn.clicked.connect(self.cancel_edit)
            self.cancel_edit_btn.setVisible(False)

            buttons_layout.addWidget(self.add_duty_btn)
            buttons_layout.addWidget(self.update_duty_btn)
            buttons_layout.addWidget(self.cancel_edit_btn)
            buttons_layout.addStretch()

            add_duty_layout.addLayout(buttons_layout, 3, 0, 1, 4)

            layout.addWidget(add_duty_group)

            # مجموعة عرض الواجبات
            duties_display_group = QGroupBox("📊 الواجبات الشهرية")
            duties_display_layout = QVBoxLayout(duties_display_group)

            # أزرار التحكم في الجدول
            table_controls_layout = QHBoxLayout()

            edit_duty_btn = QPushButton("✏️ تعديل الواجب المحدد")
            edit_duty_btn.setFont(QFont("Calibri", 11, QFont.Bold))
            edit_duty_btn.setStyleSheet("""
                QPushButton {
                    background-color: #17a2b8;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 8px 15px;
                    margin: 3px;
                }
                QPushButton:hover {
                    background-color: #138496;
                }
            """)
            edit_duty_btn.clicked.connect(self.edit_selected_duty)

            delete_duty_btn = QPushButton("🗑️ حذف الواجب المحدد")
            delete_duty_btn.setFont(QFont("Calibri", 11, QFont.Bold))
            delete_duty_btn.setStyleSheet("""
                QPushButton {
                    background-color: #dc3545;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 8px 15px;
                    margin: 3px;
                }
                QPushButton:hover {
                    background-color: #c82333;
                }
            """)
            delete_duty_btn.clicked.connect(self.delete_selected_duty)

            print_duty_btn = QPushButton("🖨️ طباعة توصيل")
            print_duty_btn.setFont(QFont("Calibri", 11, QFont.Bold))
            print_duty_btn.setStyleSheet("""
                QPushButton {
                    background-color: #6f42c1;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 8px 15px;
                    margin: 3px;
                }
                QPushButton:hover {
                    background-color: #5a32a3;
                }
            """)
            print_duty_btn.clicked.connect(self.print_duty_receipt)

            # زر طباعة التوصيل الموحد للمحدد
            print_unified_selected_btn = QPushButton("🖨️ طباعة التوصيل الموحد للمحدد")
            print_unified_selected_btn.setFont(QFont("Calibri", 11, QFont.Bold))
            print_unified_selected_btn.setStyleSheet("""
                QPushButton {
                    background-color: #17a2b8;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 8px 15px;
                    margin: 3px;
                }
                QPushButton:hover {
                    background-color: #138496;
                }
            """)
            print_unified_selected_btn.clicked.connect(self.print_unified_selected_duties)

            table_controls_layout.addWidget(edit_duty_btn)
            table_controls_layout.addWidget(delete_duty_btn)
            table_controls_layout.addWidget(print_duty_btn)
            table_controls_layout.addWidget(print_unified_selected_btn)
            table_controls_layout.addStretch()

            duties_display_layout.addLayout(table_controls_layout)

            # جدول الواجبات
            self.duties_table = QTableWidget()
            self.setup_duties_table()
            duties_display_layout.addWidget(self.duties_table)

            layout.addWidget(duties_display_group)

            print(f"✅ [SUCCESS] تم إنشاء تبويب الواجبات الشهرية بنجاح")

        except Exception as e:
            print(f"❌ [ERROR] خطأ في إنشاء تبويب الواجبات الشهرية: {str(e)}")
            traceback.print_exc()
            raise

    def setup_duties_table(self):
        """إعداد جدول الواجبات الشهرية"""
        try:
            columns = ["ID", "اختيار", "القسم", "الشهر", "السنة", "المطلوب", "المدفوع", "المتبقي", "تاريخ الدفع", "الحالة", "ملاحظات"]
            self.duties_table.setColumnCount(len(columns))
            self.duties_table.setHorizontalHeaderLabels(columns)

            # تطبيق تنسيق الجدول - Calibri 13 أسود غامق
            self.duties_table.setFont(QFont("Calibri", 13, QFont.Bold))
            self.duties_table.setAlternatingRowColors(True)
            self.duties_table.setSelectionBehavior(QAbstractItemView.SelectRows)
            self.duties_table.setSelectionMode(QAbstractItemView.SingleSelection)

            # تعيين عرض الأعمدة
            header = self.duties_table.horizontalHeader()
            header.setStretchLastSection(True)

            # إخفاء عمود ID
            self.duties_table.setColumnHidden(0, True)

            print(f"✅ [SUCCESS] تم إعداد جدول الواجبات بنجاح")

        except Exception as e:
            print(f"❌ [ERROR] خطأ في إعداد جدول الواجبات: {str(e)}")
            traceback.print_exc()

    def on_section_changed(self):
        """عند تغيير القسم المحدد"""
        try:
            selected_section = self.section_combo.currentText()
            if selected_section and selected_section != "اختر القسم":
                # العثور على معرف القسم
                for section in self.student_sections:
                    if section['section'] == selected_section:
                        self.current_section_id = section['id']
                        # تحديث المبلغ المطلوب تلقائياً
                        self.amount_input.setText(str(section['monthly_duty']))
                        break

                # تحديث جدول الواجبات للقسم المحدد
                self.load_section_duties()

        except Exception as e:
            print(f"❌ [ERROR] خطأ في تغيير القسم: {str(e)}")
            traceback.print_exc()

    def load_section_duties(self):
        """تحميل واجبات القسم المحدد"""
        try:
            if not self.current_section_id:
                return

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # جلب واجبات القسم المحدد
            cursor.execute("""
                SELECT
                    md.id, md.month, md.year, md.amount_required, md.amount_paid,
                    md.amount_remaining, md.payment_date, md.payment_status, md.notes,
                    jb.القسم
                FROM monthly_duties md
                JOIN جدول_البيانات jb ON md.student_id = jb.id
                WHERE md.student_id = ?
                ORDER BY md.year DESC,
                CASE md.month
                    WHEN 'يناير' THEN 1 WHEN 'فبراير' THEN 2 WHEN 'مارس' THEN 3
                    WHEN 'أبريل' THEN 4 WHEN 'مايو' THEN 5 WHEN 'يونيو' THEN 6
                    WHEN 'يوليو' THEN 7 WHEN 'أغسطس' THEN 8 WHEN 'سبتمبر' THEN 9
                    WHEN 'أكتوبر' THEN 10 WHEN 'نوفمبر' THEN 11 WHEN 'ديسمبر' THEN 12
                END DESC
            """, (self.current_section_id,))

            duties = cursor.fetchall()
            conn.close()

            # تحديث الجدول
            self.duties_table.setRowCount(len(duties))

            for row, duty in enumerate(duties):
                self.duties_table.setItem(row, 0, QTableWidgetItem(str(duty[0])))  # ID

                # إضافة صندوق اختيار
                checkbox = QCheckBox()
                checkbox.setStyleSheet("QCheckBox { margin: 5px; }")
                self.duties_table.setCellWidget(row, 1, checkbox)

                self.duties_table.setItem(row, 2, QTableWidgetItem(duty[9]))       # القسم
                self.duties_table.setItem(row, 3, QTableWidgetItem(duty[1]))       # الشهر
                self.duties_table.setItem(row, 4, QTableWidgetItem(str(duty[2])))  # السنة
                self.duties_table.setItem(row, 5, QTableWidgetItem(f"{duty[3]:.2f}"))  # المطلوب
                self.duties_table.setItem(row, 6, QTableWidgetItem(f"{duty[4]:.2f}"))  # المدفوع
                self.duties_table.setItem(row, 7, QTableWidgetItem(f"{duty[5]:.2f}"))  # المتبقي
                self.duties_table.setItem(row, 8, QTableWidgetItem(duty[6] or ""))     # تاريخ الدفع
                self.duties_table.setItem(row, 9, QTableWidgetItem(duty[7]))           # الحالة
                self.duties_table.setItem(row, 10, QTableWidgetItem(duty[8] or ""))     # ملاحظات

                # تلوين الصفوف حسب حالة الدفع
                if duty[7] == "مدفوع":
                    for col in range(self.duties_table.columnCount()):
                        if col == 1:  # تجاهل عمود الاختيار
                            continue
                        item = self.duties_table.item(row, col)
                        if item:
                            item.setBackground(QColor("#d4edda"))
                elif duty[7] == "مدفوع جزئياً":
                    for col in range(self.duties_table.columnCount()):
                        if col == 1:  # تجاهل عمود الاختيار
                            continue
                        item = self.duties_table.item(row, col)
                        if item:
                            item.setBackground(QColor("#fff3cd"))
                else:  # غير مدفوع
                    for col in range(self.duties_table.columnCount()):
                        if col == 1:  # تجاهل عمود الاختيار
                            continue
                        item = self.duties_table.item(row, col)
                        if item:
                            item.setBackground(QColor("#f8d7da"))

            print(f"✅ [SUCCESS] تم تحميل {len(duties)} واجب للقسم")

        except Exception as e:
            print(f"❌ [ERROR] خطأ في تحميل واجبات القسم: {str(e)}")
            traceback.print_exc()

    def load_all_duties_data(self):
        """تحميل جميع بيانات الواجبات وتحديث قائمة الأقسام"""
        try:
            # تحديث قائمة الأقسام
            self.section_combo.clear()
            self.section_combo.addItem("اختر القسم")

            for section in self.student_sections:
                self.section_combo.addItem(section['section'])

            # تحديث قائمة أقسام التسجيل أيضاً
            if hasattr(self, 'reg_section_combo'):
                self.reg_section_combo.clear()
                self.reg_section_combo.addItem("اختر القسم")

                for section in self.student_sections:
                    self.reg_section_combo.addItem(section['section'])

            print(f"✅ [SUCCESS] تم تحديث قائمة الأقسام")

        except Exception as e:
            print(f"❌ [ERROR] خطأ في تحميل بيانات الواجبات: {str(e)}")
            traceback.print_exc()

    def add_monthly_duty(self):
        """إضافة واجب شهري جديد"""
        try:
            if not self.current_section_id:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار قسم أولاً.")
                return

            # التحقق من صحة البيانات
            try:
                amount_required = float(self.amount_input.text() or 0)
                amount_paid = float(self.paid_input.text() or 0)
            except ValueError:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال قيم صحيحة للمبالغ.")
                return

            if amount_required < 0 or amount_paid < 0:
                QMessageBox.warning(self, "تحذير", "لا يمكن أن تكون المبالغ سالبة.")
                return

            if amount_paid > amount_required:
                QMessageBox.warning(self, "تحذير", "المبلغ المدفوع لا يمكن أن يكون أكبر من المبلغ المطلوب.")
                return

            # حساب المبلغ المتبقي والحالة
            amount_remaining = amount_required - amount_paid

            if amount_paid == 0:
                status = "غير مدفوع"
            elif amount_remaining == 0:
                status = "مدفوع"
            else:
                status = "مدفوع جزئياً"

            # إضافة الواجب لقاعدة البيانات
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # جلب معلومات القسم
            cursor.execute("""
                SELECT jb.القسم, jb.النوع
                FROM جدول_البيانات jb
                WHERE jb.id = ?
            """, (self.current_section_id,))

            section_info = cursor.fetchone()
            section_name = section_info[0] if section_info else ""
            student_type = section_info[1] if section_info else ""

            # جلب معلومات الأستاذ والمادة
            cursor.execute("""
                SELECT اسم_الاستاذ, المادة
                FROM جدول_المواد_والاقسام
                WHERE القسم = ?
                LIMIT 1
            """, (section_name,))

            teacher_subject = cursor.fetchone()
            teacher_name = teacher_subject[0] if teacher_subject else ""
            subject_name = teacher_subject[1] if teacher_subject else ""

            # إدراج الواجب
            cursor.execute("""
                INSERT INTO monthly_duties
                (student_id, month, year, amount_required, amount_paid, amount_remaining,
                 payment_date, payment_status, notes, اسم_الاستاذ, القسم, المادة, النوع)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                self.current_section_id,
                self.month_combo.currentText(),
                self.year_spin.value(),
                amount_required,
                amount_paid,
                amount_remaining,
                self.payment_date.date().toString("yyyy-MM-dd") if amount_paid > 0 else None,
                status,
                self.notes_input.text().strip(),
                teacher_name,
                section_name,
                subject_name,
                student_type
            ))

            conn.commit()
            conn.close()

            QMessageBox.information(self, "نجح", "تم إضافة الواجب الشهري بنجاح.")

            # مسح الحقول وتحديث الجدول
            self.clear_duty_form()
            self.load_section_duties()

        except Exception as e:
            print(f"❌ [ERROR] خطأ في إضافة الواجب: {str(e)}")
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"فشل في إضافة الواجب: {str(e)}")

    def clear_duty_form(self):
        """مسح حقول نموذج الواجب"""
        try:
            self.paid_input.clear()
            self.notes_input.clear()
            self.payment_date.setDate(QDate.currentDate())

        except Exception as e:
            print(f"❌ [ERROR] خطأ في مسح النموذج: {str(e)}")
            traceback.print_exc()

    def edit_selected_duty(self):
        """تعديل الواجب المحدد"""
        try:
            current_row = self.duties_table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "تحذير", "يرجى تحديد واجب للتعديل.")
                return

            # جلب معرف الواجب
            duty_id = int(self.duties_table.item(current_row, 0).text())
            self.editing_duty_id = duty_id

            # ملء الحقول ببيانات الواجب
            self.month_combo.setCurrentText(self.duties_table.item(current_row, 3).text())
            self.year_spin.setValue(int(self.duties_table.item(current_row, 4).text()))
            self.amount_input.setText(self.duties_table.item(current_row, 5).text())
            self.paid_input.setText(self.duties_table.item(current_row, 6).text())

            payment_date_text = self.duties_table.item(current_row, 8).text()
            if payment_date_text:
                self.payment_date.setDate(QDate.fromString(payment_date_text, "yyyy-MM-dd"))

            notes_text = self.duties_table.item(current_row, 10).text()
            self.notes_input.setText(notes_text)

            # تبديل الأزرار
            self.add_duty_btn.setVisible(False)
            self.update_duty_btn.setVisible(True)
            self.cancel_edit_btn.setVisible(True)

        except Exception as e:
            print(f"❌ [ERROR] خطأ في تعديل الواجب: {str(e)}")
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"فشل في تعديل الواجب: {str(e)}")

    def update_monthly_duty(self):
        """تحديث الواجب الشهري"""
        try:
            if not self.editing_duty_id:
                return

            # التحقق من صحة البيانات
            try:
                amount_required = float(self.amount_input.text() or 0)
                amount_paid = float(self.paid_input.text() or 0)
            except ValueError:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال قيم صحيحة للمبالغ.")
                return

            if amount_required < 0 or amount_paid < 0:
                QMessageBox.warning(self, "تحذير", "لا يمكن أن تكون المبالغ سالبة.")
                return

            if amount_paid > amount_required:
                QMessageBox.warning(self, "تحذير", "المبلغ المدفوع لا يمكن أن يكون أكبر من المبلغ المطلوب.")
                return

            # حساب المبلغ المتبقي والحالة
            amount_remaining = amount_required - amount_paid

            if amount_paid == 0:
                status = "غير مدفوع"
            elif amount_remaining == 0:
                status = "مدفوع"
            else:
                status = "مدفوع جزئياً"

            # تحديث الواجب في قاعدة البيانات
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                UPDATE monthly_duties
                SET month = ?, year = ?, amount_required = ?, amount_paid = ?,
                    amount_remaining = ?, payment_date = ?, payment_status = ?,
                    notes = ?, updated_date = CURRENT_TIMESTAMP
                WHERE id = ?
            """, (
                self.month_combo.currentText(),
                self.year_spin.value(),
                amount_required,
                amount_paid,
                amount_remaining,
                self.payment_date.date().toString("yyyy-MM-dd") if amount_paid > 0 else None,
                status,
                self.notes_input.text().strip(),
                self.editing_duty_id
            ))

            conn.commit()
            conn.close()

            QMessageBox.information(self, "نجح", "تم تحديث الواجب الشهري بنجاح.")

            # إنهاء وضع التعديل وتحديث الجدول
            self.cancel_edit()
            self.load_section_duties()

        except Exception as e:
            print(f"❌ [ERROR] خطأ في تحديث الواجب: {str(e)}")
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"فشل في تحديث الواجب: {str(e)}")

    def cancel_edit(self):
        """إلغاء وضع التعديل"""
        try:
            self.editing_duty_id = None
            self.clear_duty_form()

            # تبديل الأزرار
            self.add_duty_btn.setVisible(True)
            self.update_duty_btn.setVisible(False)
            self.cancel_edit_btn.setVisible(False)

        except Exception as e:
            print(f"❌ [ERROR] خطأ في إلغاء التعديل: {str(e)}")
            traceback.print_exc()

    def delete_selected_duty(self):
        """حذف الواجب المحدد"""
        try:
            current_row = self.duties_table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "تحذير", "يرجى تحديد واجب للحذف.")
                return

            # تأكيد الحذف
            reply = QMessageBox.question(
                self, "تأكيد الحذف",
                "هل أنت متأكد من حذف هذا الواجب؟\nهذا الإجراء لا يمكن التراجع عنه.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                duty_id = int(self.duties_table.item(current_row, 0).text())

                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()

                cursor.execute("DELETE FROM monthly_duties WHERE id = ?", (duty_id,))

                conn.commit()
                conn.close()

                QMessageBox.information(self, "نجح", "تم حذف الواجب بنجاح.")
                self.load_section_duties()

        except Exception as e:
            print(f"❌ [ERROR] خطأ في حذف الواجب: {str(e)}")
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"فشل في حذف الواجب: {str(e)}")

    def print_duty_receipt(self):
        """طباعة توصيل الواجب المحدد"""
        try:
            current_row = self.duties_table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "تحذير", "يرجى تحديد واجب لطباعة التوصيل.")
                return

            # جلب بيانات الواجب
            duty_data = {
                'section': self.duties_table.item(current_row, 2).text(),
                'month': self.duties_table.item(current_row, 3).text(),
                'year': self.duties_table.item(current_row, 4).text(),
                'required': self.duties_table.item(current_row, 5).text(),
                'paid': self.duties_table.item(current_row, 6).text(),
                'remaining': self.duties_table.item(current_row, 7).text(),
                'payment_date': self.duties_table.item(current_row, 8).text(),
                'status': self.duties_table.item(current_row, 9).text(),
                'notes': self.duties_table.item(current_row, 10).text()
            }

            # إنشاء محتوى التوصيل
            receipt_content = self.create_duty_receipt_content(duty_data)

            # طباعة التوصيل
            self.send_to_thermal_printer(receipt_content)

            QMessageBox.information(self, "نجح", "تم إرسال التوصيل للطباعة بنجاح.")

        except Exception as e:
            print(f"❌ [ERROR] خطأ في طباعة التوصيل: {str(e)}")
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"فشل في طباعة التوصيل: {str(e)}")

    def print_unified_selected_duties(self):
        """طباعة التوصيل الموحد للواجبات المحددة"""
        try:
            # جمع الواجبات المحددة
            selected_duties = []

            for row in range(self.duties_table.rowCount()):
                checkbox = self.duties_table.cellWidget(row, 1)
                if checkbox and checkbox.isChecked():
                    duty_data = {
                        'id': int(self.duties_table.item(row, 0).text()),
                        'section': self.duties_table.item(row, 2).text(),
                        'month': self.duties_table.item(row, 3).text(),
                        'year': int(self.duties_table.item(row, 4).text()),
                        'required': float(self.duties_table.item(row, 5).text()),
                        'paid': float(self.duties_table.item(row, 6).text()),
                        'remaining': float(self.duties_table.item(row, 7).text()),
                        'payment_date': self.duties_table.item(row, 8).text(),
                        'status': self.duties_table.item(row, 9).text(),
                        'notes': self.duties_table.item(row, 10).text()
                    }
                    selected_duties.append(duty_data)

            if not selected_duties:
                QMessageBox.warning(self, "تحذير", "يرجى تحديد واجبات لطباعة التوصيل الموحد.")
                return

            # إنشاء محتوى التوصيل الموحد
            receipt_content = self.create_unified_selected_receipt_content(selected_duties)

            # طباعة التوصيل
            self.send_to_thermal_printer(receipt_content)

            # إزالة علامات الاختيار بعد الطباعة
            for row in range(self.duties_table.rowCount()):
                checkbox = self.duties_table.cellWidget(row, 1)
                if checkbox and checkbox.isChecked():
                    checkbox.setChecked(False)

            QMessageBox.information(self, "نجح", f"تم إرسال التوصيل الموحد للطباعة بنجاح.\nعدد الواجبات: {len(selected_duties)}")

        except Exception as e:
            print(f"❌ [ERROR] خطأ في طباعة التوصيل الموحد: {str(e)}")
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"فشل في طباعة التوصيل الموحد: {str(e)}")

    def create_unified_selected_receipt_content(self, selected_duties):
        """إنشاء محتوى التوصيل الموحد للواجبات المحددة"""
        try:
            # الحصول على بيانات المؤسسة
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT المؤسسة, رقم_الهاتف, المدينة FROM بيانات_المؤسسة LIMIT 1")
            institution_data = cursor.fetchone()
            conn.close()

            institution_name = institution_data[0] if institution_data and institution_data[0] else "المؤسسة التعليمية"
            institution_phone = institution_data[1] if institution_data and institution_data[1] else ""
            institution_city = institution_data[2] if institution_data and institution_data[2] else ""

            # بناء محتوى الوصل - تصميم مخصص مطابق للأصل
            receipt_lines = []

            # إطار حول اسم المؤسسة والمدينة
            receipt_lines.append("=" * 40)
            receipt_lines.append(f"{institution_name}".center(40))
            if institution_city:
                receipt_lines.append(f"{institution_city}".center(40))
            receipt_lines.append("=" * 40)

            # الهاتف خارج الإطار
            if institution_phone:
                receipt_lines.append(f"هاتف: {institution_phone}".center(40))

            # وصل الأداء في الوسط
            receipt_lines.append("وصل الأداء".center(40))

            # بيانات التلميذ - محاذاة إلى اليمين
            receipt_lines.append(f"اسم التلميذ: {self.student_basic_info['name'] or 'غير محدد'}".rjust(40))
            receipt_lines.append(f"رمز التلميذ: {self.student_basic_info['code'] or 'غير محدد'}".rjust(40))
            if self.student_basic_info['phone']:
                receipt_lines.append(f"رقم الهاتف: {self.student_basic_info['phone']}".rjust(40))

            # عرض الأقسام المسجل بها
            all_sections = set(duty['section'] for duty in selected_duties)
            receipt_lines.append(f"الأقسام: {', '.join(sorted(all_sections))}".rjust(40))
            receipt_lines.append("-" * 40)

            # تجميع البيانات حسب الشهر والسنة وتاريخ الدفع (اليوم فقط، ليس التوقيت)
            monthly_summary = {}
            total_required = 0
            total_paid = 0
            total_remaining = 0

            for duty in selected_duties:
                month, year, amount_required, amount_paid, amount_remaining = duty['month'], duty['year'], duty['required'], duty['paid'], duty['remaining']
                payment_date, payment_status, section = duty['payment_date'], duty['status'], duty['section']

                # استخراج التاريخ فقط (بدون التوقيت) للتجميع
                payment_date_only = None
                if payment_date:
                    try:
                        # إذا كان التاريخ يحتوي على توقيت، استخرج التاريخ فقط
                        if ' ' in str(payment_date):
                            payment_date_only = str(payment_date).split(' ')[0]
                        else:
                            payment_date_only = str(payment_date)
                    except:
                        payment_date_only = str(payment_date) if payment_date else None

                # مفتاح التجميع: الشهر + السنة + تاريخ الدفع (بدون توقيت)
                key = f"{month} {year} - {payment_date_only or 'غير محدد'}"

                if key not in monthly_summary:
                    monthly_summary[key] = {
                        'month': month, 'year': year,
                        'required': 0, 'paid': 0, 'remaining': 0,
                        'sections': [], 'payment_date': payment_date, 'status': payment_status
                    }

                monthly_summary[key]['required'] += amount_required or 0
                monthly_summary[key]['paid'] += amount_paid or 0
                monthly_summary[key]['remaining'] += amount_remaining or 0

                if section and section not in monthly_summary[key]['sections']:
                    monthly_summary[key]['sections'].append(section)

                total_required += amount_required or 0
                total_paid += amount_paid or 0
                total_remaining += amount_remaining or 0

            # عرض الواجبات الشهرية المجمعة
            for key, summary in monthly_summary.items():
                receipt_lines.append(f"الشهر: {summary['month']} {summary['year']}".rjust(40))
                receipt_lines.append(f"المطلوب: {summary['required']:.2f} درهم".rjust(40))
                receipt_lines.append(f"المدفوع: {summary['paid']:.2f} درهم".rjust(40))
                receipt_lines.append(f"المتبقي: {summary['remaining']:.2f} درهم".rjust(40))
                receipt_lines.append(f"تاريخ الدفع: {summary['payment_date'] or 'غير محدد'}".rjust(40))
                receipt_lines.append(f"الحالة: {summary['status']}".rjust(40))
                receipt_lines.append("-" * 30)

            # تذييل التوصيل
            from datetime import datetime
            receipt_lines.append(f"تاريخ الطباعة {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}".rjust(40))
            receipt_lines.append("")  # سطر فارغ
            receipt_lines.append("نتمنى لكم التوفيق والنجاح".center(40))

            return "\n".join(receipt_lines)

        except Exception as e:
            print(f"❌ [ERROR] خطأ في إنشاء التوصيل الموحد للواجبات المحددة: {str(e)}")
            return f"خطأ في إنشاء التوصيل: {str(e)}"

    def create_duty_receipt_content(self, duty_data):
        """إنشاء محتوى توصيل الواجب"""
        try:
            # الحصول على بيانات المؤسسة
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT المؤسسة, رقم_الهاتف, المدينة FROM بيانات_المؤسسة LIMIT 1")
            institution_data = cursor.fetchone()
            conn.close()

            institution_name = institution_data[0] if institution_data and institution_data[0] else "المؤسسة التعليمية"
            institution_phone = institution_data[1] if institution_data and institution_data[1] else ""
            institution_city = institution_data[2] if institution_data and institution_data[2] else ""

            # بناء محتوى التوصيل
            receipt_lines = []

            # رأس التوصيل
            receipt_lines.append("=" * 50)
            receipt_lines.append(f"{institution_name}".center(50))
            if institution_city:
                receipt_lines.append(f"{institution_city}".center(50))
            receipt_lines.append("=" * 50)
            if institution_phone:
                receipt_lines.append(f"هاتف: {institution_phone}".center(50))

            receipt_lines.append("توصيل واجب شهري".center(50))
            receipt_lines.append("-" * 50)

            # معلومات التلميذ
            receipt_lines.append(f"اسم التلميذ: {self.student_basic_info['name']}".rjust(50))
            receipt_lines.append(f"رمز التلميذ: {self.student_basic_info['code']}".rjust(50))
            if self.student_basic_info['phone']:
                receipt_lines.append(f"الهاتف: {self.student_basic_info['phone']}".rjust(50))

            receipt_lines.append("-" * 50)

            # تفاصيل الواجب
            receipt_lines.append(f"القسم: {duty_data['section']}".rjust(50))
            receipt_lines.append(f"الشهر: {duty_data['month']} {duty_data['year']}".rjust(50))
            receipt_lines.append(f"المبلغ المطلوب: {duty_data['required']} درهم".rjust(50))
            receipt_lines.append(f"المبلغ المدفوع: {duty_data['paid']} درهم".rjust(50))
            receipt_lines.append(f"المبلغ المتبقي: {duty_data['remaining']} درهم".rjust(50))
            receipt_lines.append(f"تاريخ الدفع: {duty_data['payment_date'] or 'غير محدد'}".rjust(50))
            receipt_lines.append(f"حالة الدفع: {duty_data['status']}".rjust(50))

            if duty_data['notes']:
                receipt_lines.append(f"ملاحظات: {duty_data['notes']}".rjust(50))

            receipt_lines.append("=" * 50)

            # تذييل التوصيل
            receipt_lines.append(f"تاريخ الطباعة: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}".rjust(50))
            receipt_lines.append("شكراً لكم".center(50))

            return "\n".join(receipt_lines)

        except Exception as e:
            print(f"❌ [ERROR] خطأ في إنشاء محتوى التوصيل: {str(e)}")
            return f"خطأ في إنشاء التوصيل: {str(e)}"

    def send_to_thermal_printer(self, content):
        """إرسال المحتوى للطابعة الحرارية المحفوظة في قاعدة البيانات"""
        try:
            print(f"🔍 [DEBUG] إرسال المحتوى للطابعة الحرارية...")

            # الحصول على اسم الطابعة الحرارية من قاعدة البيانات
            thermal_printer = self.get_thermal_printer_name()

            if not thermal_printer:
                QMessageBox.warning(self, "تحذير", "لم يتم تعيين طابعة حرارية. يرجى الذهاب إلى إعدادات الطابعة أولاً.")
                return

            # طباعة مباشرة على الطابعة الحرارية بدون حفظ ملف
            self.print_directly_to_thermal_printer(thermal_printer, content)

            print(f"✅ [SUCCESS] تم إرسال التوصيل للطابعة الحرارية: {thermal_printer}")

        except Exception as e:
            print(f"❌ [ERROR] خطأ في إرسال المحتوى للطابعة: {str(e)}")
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ في الطباعة", f"فشل في الطباعة على الطابعة الحرارية:\n{str(e)}")

    def get_thermal_printer_name(self):
        """الحصول على اسم الطابعة الحرارية من قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("SELECT الطابعة_الحرارية FROM إعدادات_الطابعة LIMIT 1")
            result = cursor.fetchone()

            conn.close()

            if result and result[0]:
                return result[0]
            else:
                return None

        except Exception as e:
            print(f"❌ [ERROR] خطأ في الحصول على اسم الطابعة الحرارية: {str(e)}")
            return None

    def print_directly_to_thermal_printer(self, printer_name, content):
        """طباعة مباشرة على الطابعة الحرارية بدون حفظ ملف - باستخدام جدول"""
        try:
            from PyQt5.QtPrintSupport import QPrinter
            from PyQt5.QtGui import QPainter, QFont, QFontMetrics, QPen
            from PyQt5.QtCore import QSizeF, QRect, Qt

            # إعداد الطابعة
            printer = QPrinter()
            printer.setPrinterName(printer_name)

            # إعداد حجم الورقة للطابعة الحرارية (80mm)
            printer.setPageSize(QPrinter.Custom)
            printer.setPageSizeMM(QSizeF(75, 170))  # عرض 75 ارتفاع 170

            # إعداد الهوامش - 0.4 من كل الجهات
            printer.setPageMargins(0.4, 0.4, 0.4, 0.4, QPrinter.Millimeter)

            # إعداد الرسام
            painter = QPainter()
            if painter.begin(printer):
                # إعداد الخط Calibri 12 أسود غامق
                font = QFont("Calibri", 12, QFont.Bold)
                painter.setFont(font)
                painter.setPen(Qt.black)

                # حساب مقاييس الخط
                font_metrics = QFontMetrics(font)
                line_height = font_metrics.height() + 4  # إضافة مسافة بين الأسطر

                # تقسيم المحتوى إلى أسطر
                lines = content.split('\n')

                # رسم الوصل باستخدام جدول منظم
                y_position = 20  # البدء من الأعلى
                page_width = printer.pageRect().width() - 20  # عرض الصفحة مع الهوامش

                # حساب عرض الجدول (نفس العرض المستخدم في draw_table)
                table_width = int(page_width)  # عرض الجدول
                table_x = 10  # موضع بداية الجدول

                # متغيرات لتجميع بيانات الجدول
                table_data = []
                table_start_y = None
                in_table_section = False

                # رسم العناوين (في الوسط) - رسم الخطوط المزخرفة حول المؤسسة فقط
                for i, line in enumerate(lines):
                    # رسم الخطوط المزخرفة حول المؤسسة والمدينة، تجاهل باقي الخطوط
                    if line.strip().startswith('='):
                        # رسم الخطوط المزخرفة بعرض الجدول
                        # حساب عدد الرموز المطلوبة لملء عرض الجدول
                        char_width = font_metrics.width('=')
                        num_chars = int(table_width / char_width)
                        decorative_line = '=' * num_chars

                        # رسم الخط المزخرف بنفس موضع الجدول
                        painter.drawText(table_x, int(y_position), decorative_line)
                        y_position += line_height
                        continue
                    elif line.strip().startswith('-'):
                        # تجاهل الخطوط المتقطعة
                        continue

                    if ('وصل الأداء' in line or 'وصل التسجيل' in line or
                        'شكراً لكم' in line or
                        (i < 5 and not ':' in line and not 'تاريخ الطباعة' in line)):

                        # إذا كنا في قسم الجدول، ارسم الجدول أولاً
                        if table_data and table_start_y is not None:
                            y_position = self.draw_table(painter, table_data, table_start_y, page_width, line_height, font_metrics)
                            table_data = []
                            table_start_y = None
                            in_table_section = False

                        # رسم العناوين في الوسط
                        text_width = font_metrics.width(line)
                        x_position = int((page_width - text_width) / 2 + 10)
                        x_position = max(10, x_position)

                        painter.drawText(x_position, int(y_position), line)
                        y_position += line_height

                    elif ':' in line and not line.strip().startswith('تاريخ الطباعة'):
                        # تجميع البيانات لرسم جدول واحد
                        if not in_table_section:
                            table_start_y = y_position
                            in_table_section = True

                        parts = line.split(':', 1)
                        if len(parts) == 2:
                            label = parts[0].strip()
                            value = parts[1].strip()
                            table_data.append((label, value))

                    elif line.strip().startswith('تاريخ الطباعة'):
                        # إذا كنا في قسم الجدول، ارسم الجدول أولاً
                        if table_data and table_start_y is not None:
                            y_position = self.draw_table(painter, table_data, table_start_y, page_width, line_height, font_metrics)
                            table_data = []
                            table_start_y = None
                            in_table_section = False

                        # رسم تاريخ الطباعة في الوسط
                        text_width = font_metrics.width(line)
                        x_position = int((page_width - text_width) / 2 + 10)
                        x_position = max(10, x_position)

                        painter.drawText(x_position, int(y_position), line)
                        y_position += line_height

                # رسم الجدول الأخير إذا كان متبقي
                if table_data and table_start_y is not None:
                    y_position = self.draw_table(painter, table_data, table_start_y, page_width, line_height, font_metrics)

                painter.end()
                print(f"✅ [SUCCESS] تم إرسال الوصل للطباعة على: {printer_name}")
            else:
                raise Exception(f"فشل في بدء الطباعة على الطابعة: {printer_name}")

        except Exception as e:
            print(f"❌ [ERROR] خطأ في الطباعة المباشرة: {str(e)}")
            raise

    def draw_table(self, painter, table_data, start_y, page_width, line_height, font_metrics):
        """رسم جدول حقيقي بخطوط وحدود"""
        try:
            from PyQt5.QtGui import QPen
            from PyQt5.QtCore import QRect, Qt

            if not table_data:
                return start_y

            # إعدادات الجدول - عكس الأعمدة
            col1_width = int(page_width * 0.6)  # 60% للقيمة (العمود الأول الآن)
            col2_width = int(page_width * 0.4)  # 40% للتسمية (العمود الثاني الآن)
            table_x = 10
            table_width = col1_width + col2_width
            row_height = line_height + 4  # ارتفاع أكبر للصفوف

            # حساب ارتفاع الجدول الكامل
            table_height = len(table_data) * row_height

            # رسم الإطار الخارجي للجدول
            painter.setPen(QPen(Qt.black, 2))  # خط أسود سميك
            table_rect = QRect(table_x, int(start_y), table_width, table_height)
            painter.drawRect(table_rect)

            # رسم الخط الفاصل بين العمودين
            painter.setPen(QPen(Qt.black, 1))  # خط أسود عادي
            separator_x = table_x + col1_width
            painter.drawLine(separator_x, int(start_y), separator_x, int(start_y + table_height))

            # رسم الخطوط الأفقية بين الصفوف
            for i in range(1, len(table_data)):
                y_line = int(start_y + i * row_height)
                painter.drawLine(table_x, y_line, table_x + table_width, y_line)

            # رسم النصوص في الجدول
            painter.setPen(Qt.black)  # لون النص

            for i, (label, value) in enumerate(table_data):
                row_y = int(start_y + i * row_height)

                # رسم القيمة في العمود الأول (محاذاة يمين) - معكوس
                value_rect = QRect(table_x + 5, row_y + 2, col1_width - 10, row_height - 4)
                painter.drawText(value_rect, Qt.AlignRight | Qt.AlignVCenter, value)

                # رسم التسمية في العمود الثاني (محاذاة يمين) - معكوس وبدون ":"
                label_rect = QRect(separator_x + 5, row_y + 2, col2_width - 10, row_height - 4)
                painter.drawText(label_rect, Qt.AlignRight | Qt.AlignVCenter, label)

            # إرجاع الموضع Y الجديد بعد الجدول
            return start_y + table_height + line_height

        except Exception as e:
            print(f"❌ [ERROR] خطأ في رسم الجدول: {str(e)}")
            return start_y

    def create_registration_duties_tab(self):
        """إنشاء تبويب واجبات التسجيل"""
        try:
            layout = QVBoxLayout(self.registration_duties_tab)

            # مجموعة اختيار القسم
            section_selection_group = QGroupBox("📚 اختيار القسم")
            section_selection_layout = QHBoxLayout(section_selection_group)

            section_label = QLabel("القسم:")
            section_label.setFont(QFont("Calibri", 12, QFont.Bold))

            self.reg_section_combo = QComboBox()
            self.reg_section_combo.setFont(QFont("Calibri", 12))
            self.reg_section_combo.currentTextChanged.connect(self.on_reg_section_changed)

            section_selection_layout.addWidget(section_label)
            section_selection_layout.addWidget(self.reg_section_combo)
            section_selection_layout.addStretch()

            layout.addWidget(section_selection_group)

            # مجموعة إضافة دفعة جديدة
            add_payment_group = QGroupBox("➕ إضافة دفعة تسجيل جديدة")
            add_payment_layout = QGridLayout(add_payment_group)

            # حقول إدخال الدفعة
            payment_type_label = QLabel("نوع الدفعة:")
            payment_type_label.setFont(QFont("Calibri", 12, QFont.Bold))
            self.payment_type_combo = QComboBox()
            self.payment_type_combo.setFont(QFont("Calibri", 12))
            payment_types = ["رسوم التسجيل", "رسوم الكتب", "رسوم الأنشطة", "رسوم أخرى"]
            self.payment_type_combo.addItems(payment_types)

            amount_paid_label = QLabel("المبلغ المدفوع:")
            amount_paid_label.setFont(QFont("Calibri", 12, QFont.Bold))
            self.reg_amount_input = QLineEdit()
            self.reg_amount_input.setFont(QFont("Calibri", 12))
            self.reg_amount_input.setPlaceholderText("مثال: 500")

            payment_method_label = QLabel("طريقة الدفع:")
            payment_method_label.setFont(QFont("Calibri", 12, QFont.Bold))
            self.payment_method_combo = QComboBox()
            self.payment_method_combo.setFont(QFont("Calibri", 12))
            payment_methods = ["نقداً", "شيك", "تحويل بنكي", "بطاقة ائتمان"]
            self.payment_method_combo.addItems(payment_methods)

            reg_payment_date_label = QLabel("تاريخ الدفع:")
            reg_payment_date_label.setFont(QFont("Calibri", 12, QFont.Bold))
            self.reg_payment_date = QDateEdit()
            self.reg_payment_date.setFont(QFont("Calibri", 12))
            self.reg_payment_date.setDate(QDate.currentDate())
            self.reg_payment_date.setCalendarPopup(True)

            reg_notes_label = QLabel("ملاحظات:")
            reg_notes_label.setFont(QFont("Calibri", 12, QFont.Bold))
            self.reg_notes_input = QLineEdit()
            self.reg_notes_input.setFont(QFont("Calibri", 12))
            self.reg_notes_input.setPlaceholderText("ملاحظات اختيارية...")

            # ترتيب الحقول
            add_payment_layout.addWidget(payment_type_label, 0, 0)
            add_payment_layout.addWidget(self.payment_type_combo, 0, 1)
            add_payment_layout.addWidget(amount_paid_label, 0, 2)
            add_payment_layout.addWidget(self.reg_amount_input, 0, 3)

            add_payment_layout.addWidget(payment_method_label, 1, 0)
            add_payment_layout.addWidget(self.payment_method_combo, 1, 1)
            add_payment_layout.addWidget(reg_payment_date_label, 1, 2)
            add_payment_layout.addWidget(self.reg_payment_date, 1, 3)

            add_payment_layout.addWidget(reg_notes_label, 2, 0)
            add_payment_layout.addWidget(self.reg_notes_input, 2, 1, 1, 3)

            # أزرار التحكم
            reg_buttons_layout = QHBoxLayout()

            self.add_payment_btn = QPushButton("➕ إضافة الدفعة")
            self.add_payment_btn.setFont(QFont("Calibri", 12, QFont.Bold))
            self.add_payment_btn.setStyleSheet("""
                QPushButton {
                    background-color: #28a745;
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 10px 20px;
                    margin: 5px;
                }
                QPushButton:hover {
                    background-color: #218838;
                }
            """)
            self.add_payment_btn.clicked.connect(self.add_registration_payment)

            reg_buttons_layout.addWidget(self.add_payment_btn)
            reg_buttons_layout.addStretch()

            add_payment_layout.addLayout(reg_buttons_layout, 3, 0, 1, 4)

            layout.addWidget(add_payment_group)

            # مجموعة عرض الدفعات
            payments_display_group = QGroupBox("📊 دفعات التسجيل")
            payments_display_layout = QVBoxLayout(payments_display_group)

            # أزرار التحكم في الجدول
            reg_table_controls_layout = QHBoxLayout()

            delete_payment_btn = QPushButton("🗑️ حذف الدفعة المحددة")
            delete_payment_btn.setFont(QFont("Calibri", 11, QFont.Bold))
            delete_payment_btn.setStyleSheet("""
                QPushButton {
                    background-color: #dc3545;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 8px 15px;
                    margin: 3px;
                }
                QPushButton:hover {
                    background-color: #c82333;
                }
            """)
            delete_payment_btn.clicked.connect(self.delete_selected_payment)

            print_payment_btn = QPushButton("🖨️ طباعة توصيل")
            print_payment_btn.setFont(QFont("Calibri", 11, QFont.Bold))
            print_payment_btn.setStyleSheet("""
                QPushButton {
                    background-color: #6f42c1;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 8px 15px;
                    margin: 3px;
                }
                QPushButton:hover {
                    background-color: #5a32a3;
                }
            """)
            print_payment_btn.clicked.connect(self.print_payment_receipt)

            # زر طباعة التوصيل الموحد للمحدد
            print_unified_reg_selected_btn = QPushButton("🖨️ طباعة التوصيل الموحد للمحدد")
            print_unified_reg_selected_btn.setFont(QFont("Calibri", 11, QFont.Bold))
            print_unified_reg_selected_btn.setStyleSheet("""
                QPushButton {
                    background-color: #17a2b8;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 8px 15px;
                    margin: 3px;
                }
                QPushButton:hover {
                    background-color: #138496;
                }
            """)
            print_unified_reg_selected_btn.clicked.connect(self.print_unified_selected_registration)

            reg_table_controls_layout.addWidget(delete_payment_btn)
            reg_table_controls_layout.addWidget(print_payment_btn)
            reg_table_controls_layout.addWidget(print_unified_reg_selected_btn)
            reg_table_controls_layout.addStretch()

            payments_display_layout.addLayout(reg_table_controls_layout)

            # جدول الدفعات
            self.registration_table = QTableWidget()
            self.setup_registration_table()
            payments_display_layout.addWidget(self.registration_table)

            layout.addWidget(payments_display_group)

            print(f"✅ [SUCCESS] تم إنشاء تبويب واجبات التسجيل بنجاح")

        except Exception as e:
            print(f"❌ [ERROR] خطأ في إنشاء تبويب واجبات التسجيل: {str(e)}")
            traceback.print_exc()
            raise

    def setup_registration_table(self):
        """إعداد جدول واجبات التسجيل"""
        try:
            columns = ["ID", "اختيار", "القسم", "نوع الدفعة", "المبلغ", "طريقة الدفع", "تاريخ الدفع", "ملاحظات"]
            self.registration_table.setColumnCount(len(columns))
            self.registration_table.setHorizontalHeaderLabels(columns)

            # تطبيق تنسيق الجدول - Calibri 13 أسود غامق
            self.registration_table.setFont(QFont("Calibri", 13, QFont.Bold))
            self.registration_table.setAlternatingRowColors(True)
            self.registration_table.setSelectionBehavior(QAbstractItemView.SelectRows)
            self.registration_table.setSelectionMode(QAbstractItemView.SingleSelection)

            # تعيين عرض الأعمدة
            header = self.registration_table.horizontalHeader()
            header.setStretchLastSection(True)

            # إخفاء عمود ID
            self.registration_table.setColumnHidden(0, True)

            print(f"✅ [SUCCESS] تم إعداد جدول واجبات التسجيل بنجاح")

        except Exception as e:
            print(f"❌ [ERROR] خطأ في إعداد جدول واجبات التسجيل: {str(e)}")
            traceback.print_exc()

    def on_reg_section_changed(self):
        """عند تغيير القسم المحدد في واجبات التسجيل"""
        try:
            selected_section = self.reg_section_combo.currentText()
            if selected_section and selected_section != "اختر القسم":
                # العثور على معرف القسم
                for section in self.student_sections:
                    if section['section'] == selected_section:
                        self.current_reg_section_id = section['id']
                        # تحديث المبلغ المطلوب تلقائياً
                        self.reg_amount_input.setText(str(section['registration_fee']))
                        break

                # تحديث جدول الدفعات للقسم المحدد
                self.load_section_registration_payments()

        except Exception as e:
            print(f"❌ [ERROR] خطأ في تغيير قسم التسجيل: {str(e)}")
            traceback.print_exc()

    def load_section_registration_payments(self):
        """تحميل دفعات التسجيل للقسم المحدد"""
        try:
            if not hasattr(self, 'current_reg_section_id') or not self.current_reg_section_id:
                return

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # جلب دفعات التسجيل للقسم المحدد
            cursor.execute("""
                SELECT
                    rf.id, rf.payment_type, rf.amount_paid, rf.payment_method,
                    rf.payment_date, rf.notes, jb.القسم
                FROM registration_fees rf
                JOIN جدول_البيانات jb ON rf.student_id = jb.id
                WHERE rf.student_id = ?
                ORDER BY rf.payment_date DESC
            """, (self.current_reg_section_id,))

            payments = cursor.fetchall()
            conn.close()

            # تحديث الجدول
            self.registration_table.setRowCount(len(payments))

            for row, payment in enumerate(payments):
                self.registration_table.setItem(row, 0, QTableWidgetItem(str(payment[0])))  # ID

                # إضافة صندوق اختيار
                checkbox = QCheckBox()
                checkbox.setStyleSheet("QCheckBox { margin: 5px; }")
                self.registration_table.setCellWidget(row, 1, checkbox)

                self.registration_table.setItem(row, 2, QTableWidgetItem(payment[6]))       # القسم
                self.registration_table.setItem(row, 3, QTableWidgetItem(payment[1]))       # نوع الدفعة
                self.registration_table.setItem(row, 4, QTableWidgetItem(f"{payment[2]:.2f}"))  # المبلغ
                self.registration_table.setItem(row, 5, QTableWidgetItem(payment[3]))       # طريقة الدفع
                self.registration_table.setItem(row, 6, QTableWidgetItem(payment[4] or ""))     # تاريخ الدفع
                self.registration_table.setItem(row, 7, QTableWidgetItem(payment[5] or ""))     # ملاحظات

                # تلوين الصفوف
                for col in range(self.registration_table.columnCount()):
                    if col == 1:  # تجاهل عمود الاختيار
                        continue
                    item = self.registration_table.item(row, col)
                    if item:
                        item.setBackground(QColor("#d4edda"))  # أخضر فاتح للدفعات المكتملة

            print(f"✅ [SUCCESS] تم تحميل {len(payments)} دفعة للقسم")

        except Exception as e:
            print(f"❌ [ERROR] خطأ في تحميل دفعات التسجيل: {str(e)}")
            traceback.print_exc()

    def add_registration_payment(self):
        """إضافة دفعة تسجيل جديدة"""
        try:
            if not hasattr(self, 'current_reg_section_id') or not self.current_reg_section_id:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار قسم أولاً.")
                return

            # التحقق من صحة البيانات
            try:
                amount_paid = float(self.reg_amount_input.text() or 0)
            except ValueError:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال قيمة صحيحة للمبلغ.")
                return

            if amount_paid <= 0:
                QMessageBox.warning(self, "تحذير", "يجب أن يكون المبلغ أكبر من صفر.")
                return

            # إضافة الدفعة لقاعدة البيانات
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # جلب معلومات القسم
            cursor.execute("""
                SELECT jb.القسم, jb.النوع
                FROM جدول_البيانات jb
                WHERE jb.id = ?
            """, (self.current_reg_section_id,))

            section_info = cursor.fetchone()
            section_name = section_info[0] if section_info else ""
            student_type = section_info[1] if section_info else ""

            # جلب معلومات الأستاذ والمادة
            cursor.execute("""
                SELECT اسم_الاستاذ, المادة
                FROM جدول_المواد_والاقسام
                WHERE القسم = ?
                LIMIT 1
            """, (section_name,))

            teacher_subject = cursor.fetchone()
            teacher_name = teacher_subject[0] if teacher_subject else ""
            subject_name = teacher_subject[1] if teacher_subject else ""

            # إدراج الدفعة
            cursor.execute("""
                INSERT INTO registration_fees
                (student_id, payment_type, amount_paid, payment_date,
                 payment_method, notes, القسم, اسم_الاستاذ, المادة, النوع)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                self.current_reg_section_id,
                self.payment_type_combo.currentText(),
                amount_paid,
                self.reg_payment_date.date().toString("yyyy-MM-dd"),
                self.payment_method_combo.currentText(),
                self.reg_notes_input.text().strip(),
                section_name,
                teacher_name,
                subject_name,
                student_type
            ))

            conn.commit()
            conn.close()

            QMessageBox.information(self, "نجح", "تم إضافة دفعة التسجيل بنجاح.")

            # مسح الحقول وتحديث الجدول
            self.clear_registration_form()
            self.load_section_registration_payments()
            self.update_statistics()

        except Exception as e:
            print(f"❌ [ERROR] خطأ في إضافة دفعة التسجيل: {str(e)}")
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"فشل في إضافة دفعة التسجيل: {str(e)}")

    def clear_registration_form(self):
        """مسح حقول نموذج التسجيل"""
        try:
            self.reg_notes_input.clear()
            self.reg_payment_date.setDate(QDate.currentDate())

        except Exception as e:
            print(f"❌ [ERROR] خطأ في مسح نموذج التسجيل: {str(e)}")
            traceback.print_exc()

    def delete_selected_payment(self):
        """حذف الدفعة المحددة"""
        try:
            current_row = self.registration_table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "تحذير", "يرجى تحديد دفعة للحذف.")
                return

            # تأكيد الحذف
            reply = QMessageBox.question(
                self, "تأكيد الحذف",
                "هل أنت متأكد من حذف هذه الدفعة؟\nهذا الإجراء لا يمكن التراجع عنه.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                payment_id = int(self.registration_table.item(current_row, 0).text())

                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()

                cursor.execute("DELETE FROM registration_fees WHERE id = ?", (payment_id,))

                conn.commit()
                conn.close()

                QMessageBox.information(self, "نجح", "تم حذف الدفعة بنجاح.")
                self.load_section_registration_payments()
                self.update_statistics()

        except Exception as e:
            print(f"❌ [ERROR] خطأ في حذف الدفعة: {str(e)}")
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"فشل في حذف الدفعة: {str(e)}")

    def print_payment_receipt(self):
        """طباعة توصيل الدفعة المحددة"""
        try:
            current_row = self.registration_table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "تحذير", "يرجى تحديد دفعة لطباعة التوصيل.")
                return

            # جلب بيانات الدفعة
            payment_data = {
                'section': self.registration_table.item(current_row, 2).text(),
                'payment_type': self.registration_table.item(current_row, 3).text(),
                'amount': self.registration_table.item(current_row, 4).text(),
                'payment_method': self.registration_table.item(current_row, 5).text(),
                'payment_date': self.registration_table.item(current_row, 6).text(),
                'notes': self.registration_table.item(current_row, 7).text()
            }

            # إنشاء محتوى التوصيل
            receipt_content = self.create_payment_receipt_content(payment_data)

            # طباعة التوصيل
            self.send_to_thermal_printer(receipt_content)

            QMessageBox.information(self, "نجح", "تم إرسال التوصيل للطباعة بنجاح.")

        except Exception as e:
            print(f"❌ [ERROR] خطأ في طباعة توصيل الدفعة: {str(e)}")
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"فشل في طباعة توصيل الدفعة: {str(e)}")

    def create_payment_receipt_content(self, payment_data):
        """إنشاء محتوى توصيل الدفعة"""
        try:
            # الحصول على بيانات المؤسسة
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT المؤسسة, رقم_الهاتف, المدينة FROM بيانات_المؤسسة LIMIT 1")
            institution_data = cursor.fetchone()
            conn.close()

            institution_name = institution_data[0] if institution_data and institution_data[0] else "المؤسسة التعليمية"
            institution_phone = institution_data[1] if institution_data and institution_data[1] else ""
            institution_city = institution_data[2] if institution_data and institution_data[2] else ""

            # بناء محتوى التوصيل
            receipt_lines = []

            # رأس التوصيل
            receipt_lines.append("=" * 50)
            receipt_lines.append(f"{institution_name}".center(50))
            if institution_city:
                receipt_lines.append(f"{institution_city}".center(50))
            receipt_lines.append("=" * 50)
            if institution_phone:
                receipt_lines.append(f"هاتف: {institution_phone}".center(50))

            receipt_lines.append("توصيل دفعة تسجيل".center(50))
            receipt_lines.append("-" * 50)

            # معلومات التلميذ
            receipt_lines.append(f"اسم التلميذ: {self.student_basic_info['name']}".rjust(50))
            receipt_lines.append(f"رمز التلميذ: {self.student_basic_info['code']}".rjust(50))
            if self.student_basic_info['phone']:
                receipt_lines.append(f"الهاتف: {self.student_basic_info['phone']}".rjust(50))

            receipt_lines.append("-" * 50)

            # تفاصيل الدفعة
            receipt_lines.append(f"القسم: {payment_data['section']}".rjust(50))
            receipt_lines.append(f"نوع الدفعة: {payment_data['payment_type']}".rjust(50))
            receipt_lines.append(f"المبلغ المدفوع: {payment_data['amount']} درهم".rjust(50))
            receipt_lines.append(f"طريقة الدفع: {payment_data['payment_method']}".rjust(50))
            receipt_lines.append(f"تاريخ الدفع: {payment_data['payment_date'] or 'غير محدد'}".rjust(50))

            if payment_data['notes']:
                receipt_lines.append(f"ملاحظات: {payment_data['notes']}".rjust(50))

            receipt_lines.append("=" * 50)

            # تذييل التوصيل
            receipt_lines.append(f"تاريخ الطباعة: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}".rjust(50))
            receipt_lines.append("")  # سطر فارغ
            receipt_lines.append("نتمنى لكم التوفيق والنجاح".center(50))

            return "\n".join(receipt_lines)

        except Exception as e:
            print(f"❌ [ERROR] خطأ في إنشاء محتوى توصيل الدفعة: {str(e)}")
            return f"خطأ في إنشاء التوصيل: {str(e)}"

    def print_unified_selected_registration(self):
        """طباعة التوصيل الموحد لواجبات التسجيل المحددة"""
        try:
            # جمع الدفعات المحددة
            selected_payments = []

            for row in range(self.registration_table.rowCount()):
                checkbox = self.registration_table.cellWidget(row, 1)
                if checkbox and checkbox.isChecked():
                    payment_data = {
                        'id': int(self.registration_table.item(row, 0).text()),
                        'section': self.registration_table.item(row, 2).text(),
                        'payment_type': self.registration_table.item(row, 3).text(),
                        'amount': float(self.registration_table.item(row, 4).text()),
                        'payment_method': self.registration_table.item(row, 5).text(),
                        'payment_date': self.registration_table.item(row, 6).text(),
                        'notes': self.registration_table.item(row, 7).text()
                    }
                    selected_payments.append(payment_data)

            if not selected_payments:
                QMessageBox.warning(self, "تحذير", "يرجى تحديد دفعات لطباعة التوصيل الموحد.")
                return

            # إنشاء محتوى التوصيل الموحد
            receipt_content = self.create_unified_selected_registration_content(selected_payments)

            # طباعة التوصيل
            self.send_to_thermal_printer(receipt_content)

            # إزالة علامات الاختيار بعد الطباعة
            for row in range(self.registration_table.rowCount()):
                checkbox = self.registration_table.cellWidget(row, 1)
                if checkbox and checkbox.isChecked():
                    checkbox.setChecked(False)

            QMessageBox.information(self, "نجح", f"تم إرسال التوصيل الموحد للطباعة بنجاح.\nعدد الدفعات: {len(selected_payments)}")

        except Exception as e:
            print(f"❌ [ERROR] خطأ في طباعة التوصيل الموحد لواجبات التسجيل: {str(e)}")
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"فشل في طباعة التوصيل الموحد: {str(e)}")

    def create_unified_selected_registration_content(self, selected_payments):
        """إنشاء محتوى التوصيل الموحد لواجبات التسجيل المحددة"""
        try:
            # الحصول على بيانات المؤسسة
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT المؤسسة, رقم_الهاتف, المدينة FROM بيانات_المؤسسة LIMIT 1")
            institution_data = cursor.fetchone()
            conn.close()

            institution_name = institution_data[0] if institution_data and institution_data[0] else "المؤسسة التعليمية"
            institution_phone = institution_data[1] if institution_data and institution_data[1] else ""
            institution_city = institution_data[2] if institution_data and institution_data[2] else ""

            # بناء محتوى الوصل - تصميم مخصص مطابق للأصل
            receipt_lines = []

            # إطار حول اسم المؤسسة والمدينة
            receipt_lines.append("=" * 40)
            receipt_lines.append(f"{institution_name}".center(40))
            if institution_city:
                receipt_lines.append(f"{institution_city}".center(40))
            receipt_lines.append("=" * 40)

            # الهاتف خارج الإطار
            if institution_phone:
                receipt_lines.append(f"هاتف: {institution_phone}".center(40))

            # وصل التسجيل في الوسط
            receipt_lines.append("وصل التسجيل".center(40))

            # بيانات التلميذ - محاذاة إلى اليمين
            receipt_lines.append(f"اسم التلميذ: {self.student_basic_info['name'] or 'غير محدد'}".rjust(40))
            receipt_lines.append(f"رمز التلميذ: {self.student_basic_info['code'] or 'غير محدد'}".rjust(40))
            if self.student_basic_info['phone']:
                receipt_lines.append(f"رقم الهاتف: {self.student_basic_info['phone']}".rjust(40))

            # عرض الأقسام المسجل بها
            all_sections = set(payment['section'] for payment in selected_payments)
            receipt_lines.append(f"الأقسام: {', '.join(sorted(all_sections))}".rjust(40))
            receipt_lines.append("-" * 40)

            # تجميع البيانات حسب نوع الدفعة وتاريخ الدفع (اليوم فقط، ليس التوقيت)
            payment_summary = {}
            total_paid = 0

            for payment in selected_payments:
                payment_type, amount_paid, payment_date, payment_method, section = payment['payment_type'], payment['amount'], payment['payment_date'], payment['payment_method'], payment['section']

                # استخراج التاريخ فقط (بدون التوقيت) للتجميع
                payment_date_only = None
                if payment_date:
                    try:
                        # إذا كان التاريخ يحتوي على توقيت، استخرج التاريخ فقط
                        if ' ' in str(payment_date):
                            payment_date_only = str(payment_date).split(' ')[0]
                        else:
                            payment_date_only = str(payment_date)
                    except:
                        payment_date_only = str(payment_date) if payment_date else None

                # مفتاح التجميع: نوع الدفعة + تاريخ الدفع (بدون توقيت)
                key = f"{payment_type} - {payment_date_only or 'غير محدد'}"

                if key not in payment_summary:
                    payment_summary[key] = {
                        'payment_type': payment_type,
                        'payment_date': payment_date,
                        'total_amount': 0, 'payments': [], 'sections': set()
                    }

                payment_summary[key]['total_amount'] += amount_paid or 0
                payment_summary[key]['payments'].append({
                    'amount': amount_paid, 'date': payment_date, 'method': payment_method, 'section': section
                })

                if section:
                    payment_summary[key]['sections'].add(section)

                total_paid += amount_paid or 0

            # عرض واجبات التسجيل المجمعة
            for key, summary in payment_summary.items():
                receipt_lines.append(f"نوع الدفعة: {summary['payment_type']}".rjust(40))
                receipt_lines.append(f"تاريخ الدفع: {summary['payment_date'] or 'غير محدد'}".rjust(40))
                receipt_lines.append(f"إجمالي المبلغ: {summary['total_amount']:.2f} درهم".rjust(40))

                # تفاصيل الدفعات
                for i, payment_detail in enumerate(summary['payments'], 1):
                    receipt_lines.append(f"الدفعة {i}: {payment_detail['amount']:.2f} درهم".rjust(40))
                    receipt_lines.append(f"الطريقة: {payment_detail['method'] or 'نقداً'}".rjust(40))
                    if payment_detail['section']:
                        receipt_lines.append(f"القسم: {payment_detail['section']}".rjust(40))

                receipt_lines.append("-" * 30)

            # تذييل التوصيل
            from datetime import datetime
            receipt_lines.append(f"تاريخ الطباعة {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}".rjust(40))
            receipt_lines.append("")  # سطر فارغ
            receipt_lines.append("نتمنى لكم التوفيق والنجاح".center(40))

            return "\n".join(receipt_lines)

        except Exception as e:
            print(f"❌ [ERROR] خطأ في إنشاء التوصيل الموحد لواجبات التسجيل المحددة: {str(e)}")
            return f"خطأ في إنشاء التوصيل: {str(e)}"

    def create_receipts_tab(self):
        """إنشاء تبويب التوصيلات الموحدة"""
        try:
            layout = QVBoxLayout(self.receipts_tab)

            # مجموعة التوصيلات الموحدة
            unified_receipts_group = QGroupBox("🖨️ التوصيلات الموحدة")
            unified_receipts_layout = QVBoxLayout(unified_receipts_group)

            # معلومات التوضيح
            info_label = QLabel(
                "💡 التوصيلات الموحدة تجمع جميع الواجبات من جميع الأقسام في توصيل واحد\n"
                "📋 يمكنك طباعة توصيل شامل للواجبات الشهرية أو واجبات التسجيل"
            )
            info_label.setFont(QFont("Calibri", 11))
            info_label.setStyleSheet("""
                QLabel {
                    color: #7f8c8d;
                    background-color: #ecf0f1;
                    padding: 15px;
                    border-radius: 8px;
                    border: 1px solid #bdc3c7;
                }
            """)
            info_label.setWordWrap(True)
            unified_receipts_layout.addWidget(info_label)

            # أزرار التوصيلات
            receipts_buttons_layout = QHBoxLayout()

            monthly_unified_btn = QPushButton("🖨️ توصيل موحد - الواجبات الشهرية")
            monthly_unified_btn.setFont(QFont("Calibri", 14, QFont.Bold))
            monthly_unified_btn.setStyleSheet("""
                QPushButton {
                    background-color: #e67e22;
                    color: white;
                    border: none;
                    border-radius: 12px;
                    padding: 20px 30px;
                    margin: 10px;
                    min-height: 60px;
                }
                QPushButton:hover {
                    background-color: #d35400;
                }
            """)
            monthly_unified_btn.clicked.connect(self.print_unified_monthly_receipt)

            registration_unified_btn = QPushButton("🖨️ توصيل موحد - واجبات التسجيل")
            registration_unified_btn.setFont(QFont("Calibri", 14, QFont.Bold))
            registration_unified_btn.setStyleSheet("""
                QPushButton {
                    background-color: #27ae60;
                    color: white;
                    border: none;
                    border-radius: 12px;
                    padding: 20px 30px;
                    margin: 10px;
                    min-height: 60px;
                }
                QPushButton:hover {
                    background-color: #229954;
                }
            """)
            registration_unified_btn.clicked.connect(self.print_unified_registration_receipt)

            receipts_buttons_layout.addWidget(monthly_unified_btn)
            receipts_buttons_layout.addWidget(registration_unified_btn)

            unified_receipts_layout.addLayout(receipts_buttons_layout)

            layout.addWidget(unified_receipts_group)

            # مجموعة الإحصائيات
            stats_group = QGroupBox("📊 إحصائيات التلميذ")
            stats_layout = QGridLayout(stats_group)

            # تسميات الإحصائيات
            self.total_sections_label = QLabel("عدد الأقسام: ")
            self.total_monthly_duties_label = QLabel("إجمالي الواجبات الشهرية: ")
            self.total_registration_fees_label = QLabel("إجمالي واجبات التسجيل: ")
            self.total_paid_label = QLabel("إجمالي المدفوع: ")
            self.total_remaining_label = QLabel("إجمالي المتبقي: ")

            # تطبيق تنسيق للتسميات
            for label in [self.total_sections_label, self.total_monthly_duties_label,
                         self.total_registration_fees_label, self.total_paid_label,
                         self.total_remaining_label]:
                label.setFont(QFont("Calibri", 12, QFont.Bold))
                label.setStyleSheet("color: #2c3e50; padding: 8px;")

            stats_layout.addWidget(self.total_sections_label, 0, 0)
            stats_layout.addWidget(self.total_monthly_duties_label, 0, 1)
            stats_layout.addWidget(self.total_registration_fees_label, 1, 0)
            stats_layout.addWidget(self.total_paid_label, 1, 1)
            stats_layout.addWidget(self.total_remaining_label, 2, 0)

            layout.addWidget(stats_group)

            # تحديث الإحصائيات
            self.update_statistics()

            print(f"✅ [SUCCESS] تم إنشاء تبويب التوصيلات الموحدة بنجاح")

        except Exception as e:
            print(f"❌ [ERROR] خطأ في إنشاء تبويب التوصيلات الموحدة: {str(e)}")
            traceback.print_exc()
            raise

    def update_statistics(self):
        """تحديث إحصائيات التلميذ"""
        try:
            # عدد الأقسام
            total_sections = len(self.student_sections)
            self.total_sections_label.setText(f"عدد الأقسام: {total_sections}")

            # حساب الإحصائيات من قاعدة البيانات
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # إحصائيات الواجبات الشهرية
            cursor.execute("""
                SELECT
                    COUNT(*) as total_duties,
                    SUM(amount_required) as total_required,
                    SUM(amount_paid) as total_paid,
                    SUM(amount_remaining) as total_remaining
                FROM monthly_duties md
                JOIN جدول_البيانات jb ON md.student_id = jb.id
                WHERE jb.رمز_التلميذ = ?
            """, (self.student_code,))

            monthly_stats = cursor.fetchone()
            total_duties = monthly_stats[0] if monthly_stats else 0
            total_required = monthly_stats[1] if monthly_stats else 0
            total_paid_monthly = monthly_stats[2] if monthly_stats else 0
            total_remaining = monthly_stats[3] if monthly_stats else 0

            # إحصائيات واجبات التسجيل
            cursor.execute("""
                SELECT
                    COUNT(*) as total_payments,
                    SUM(amount_paid) as total_paid
                FROM registration_fees rf
                JOIN جدول_البيانات jb ON rf.student_id = jb.id
                WHERE jb.رمز_التلميذ = ?
            """, (self.student_code,))

            registration_stats = cursor.fetchone()
            total_payments = registration_stats[0] if registration_stats else 0
            total_paid_registration = registration_stats[1] if registration_stats else 0

            conn.close()

            # تحديث التسميات
            self.total_monthly_duties_label.setText(f"إجمالي الواجبات الشهرية: {total_duties} واجب")
            self.total_registration_fees_label.setText(f"إجمالي دفعات التسجيل: {total_payments} دفعة")
            self.total_paid_label.setText(f"إجمالي المدفوع: {total_paid_monthly + total_paid_registration:.2f} درهم")
            self.total_remaining_label.setText(f"إجمالي المتبقي: {total_remaining:.2f} درهم")

            print(f"✅ [SUCCESS] تم تحديث الإحصائيات بنجاح")

        except Exception as e:
            print(f"❌ [ERROR] خطأ في تحديث الإحصائيات: {str(e)}")
            traceback.print_exc()

    def print_unified_monthly_receipt(self):
        """طباعة التوصيل الموحد للواجبات الشهرية"""
        try:
            print(f"🔍 [DEBUG] بدء طباعة التوصيل الموحد للواجبات الشهرية...")

            # جلب جميع الواجبات الشهرية للتلميذ من جميع الأقسام
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT
                    md.month, md.year, md.amount_required, md.amount_paid,
                    md.amount_remaining, md.payment_date, md.payment_status,
                    jb.القسم, jb.اسم_التلميذ, jb.رمز_التلميذ, jb.رقم_الهاتف_الأول
                FROM monthly_duties md
                JOIN جدول_البيانات jb ON md.student_id = jb.id
                WHERE jb.رمز_التلميذ = ?
                ORDER BY md.year DESC,
                CASE md.month
                    WHEN 'يناير' THEN 1 WHEN 'فبراير' THEN 2 WHEN 'مارس' THEN 3
                    WHEN 'أبريل' THEN 4 WHEN 'مايو' THEN 5 WHEN 'يونيو' THEN 6
                    WHEN 'يوليو' THEN 7 WHEN 'أغسطس' THEN 8 WHEN 'سبتمبر' THEN 9
                    WHEN 'أكتوبر' THEN 10 WHEN 'نوفمبر' THEN 11 WHEN 'ديسمبر' THEN 12
                END DESC
            """, (self.student_code,))

            duties = cursor.fetchall()
            conn.close()

            if not duties:
                QMessageBox.information(self, "معلومة", "لا توجد واجبات شهرية للتلميذ.")
                return

            # إنشاء محتوى التوصيل الموحد
            receipt_content = self.create_unified_monthly_receipt_content(duties)

            # طباعة التوصيل
            self.send_to_thermal_printer(receipt_content)

            QMessageBox.information(self, "نجح", "تم إرسال التوصيل الموحد للواجبات الشهرية للطباعة بنجاح.")

        except Exception as e:
            print(f"❌ [ERROR] خطأ في طباعة التوصيل الموحد الشهري: {str(e)}")
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"فشل في طباعة التوصيل الموحد: {str(e)}")

    def print_unified_registration_receipt(self):
        """طباعة التوصيل الموحد لواجبات التسجيل"""
        try:
            print(f"🔍 [DEBUG] بدء طباعة التوصيل الموحد لواجبات التسجيل...")

            # جلب جميع واجبات التسجيل للتلميذ من جميع الأقسام
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT
                    rf.payment_type, rf.amount_paid, rf.payment_date,
                    rf.payment_method, jb.القسم, jb.اسم_التلميذ,
                    jb.رمز_التلميذ, jb.رقم_الهاتف_الأول
                FROM registration_fees rf
                JOIN جدول_البيانات jb ON rf.student_id = jb.id
                WHERE jb.رمز_التلميذ = ?
                ORDER BY rf.payment_date DESC
            """, (self.student_code,))

            payments = cursor.fetchall()
            conn.close()

            if not payments:
                QMessageBox.information(self, "معلومة", "لا توجد واجبات تسجيل للتلميذ.")
                return

            # إنشاء محتوى التوصيل الموحد
            receipt_content = self.create_unified_registration_receipt_content(payments)

            # طباعة التوصيل
            self.send_to_thermal_printer(receipt_content)

            QMessageBox.information(self, "نجح", "تم إرسال التوصيل الموحد لواجبات التسجيل للطباعة بنجاح.")

        except Exception as e:
            print(f"❌ [ERROR] خطأ في طباعة التوصيل الموحد للتسجيل: {str(e)}")
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"فشل في طباعة التوصيل الموحد: {str(e)}")

    def create_unified_monthly_receipt_content(self, duties):
        """إنشاء محتوى التوصيل الموحد للواجبات الشهرية - نسخة طبق الأصل"""
        try:
            # الحصول على بيانات المؤسسة
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT المؤسسة, رقم_الهاتف, المدينة FROM بيانات_المؤسسة LIMIT 1")
            institution_data = cursor.fetchone()
            conn.close()

            institution_name = institution_data[0] if institution_data and institution_data[0] else "المؤسسة التعليمية"
            institution_phone = institution_data[1] if institution_data and institution_data[1] else ""
            institution_city = institution_data[2] if institution_data and institution_data[2] else ""

            # بناء محتوى الوصل - تصميم مخصص مطابق للأصل
            receipt_lines = []

            # إطار حول اسم المؤسسة والمدينة
            receipt_lines.append("=" * 40)
            receipt_lines.append(f"{institution_name}".center(40))
            if institution_city:
                receipt_lines.append(f"{institution_city}".center(40))
            receipt_lines.append("=" * 40)

            # الهاتف خارج الإطار
            if institution_phone:
                receipt_lines.append(f"هاتف: {institution_phone}".center(40))

            # وصل الأداء في الوسط
            receipt_lines.append("وصل الأداء".center(40))

            # بيانات التلميذ - محاذاة إلى اليمين
            if duties:
                student_name = duties[0][8]  # اسم التلميذ
                student_code = duties[0][9]  # رمز التلميذ
                student_phone = duties[0][10]  # رقم الهاتف

                receipt_lines.append(f"اسم التلميذ: {student_name or 'غير محدد'}".rjust(40))
                receipt_lines.append(f"رمز التلميذ: {student_code or 'غير محدد'}".rjust(40))
                if student_phone:
                    receipt_lines.append(f"رقم الهاتف: {student_phone}".rjust(40))

            receipt_lines.append("-" * 40)

            # تجميع البيانات حسب الشهر والسنة وتاريخ الدفع (اليوم فقط، ليس التوقيت)
            monthly_summary = {}
            total_required = 0
            total_paid = 0
            total_remaining = 0
            all_sections = set()

            for duty in duties:
                month, year, amount_required, amount_paid, amount_remaining = duty[0], duty[1], duty[2], duty[3], duty[4]
                payment_date, payment_status, section = duty[5], duty[6], duty[7]

                # استخراج التاريخ فقط (بدون التوقيت) للتجميع
                payment_date_only = None
                if payment_date:
                    try:
                        # إذا كان التاريخ يحتوي على توقيت، استخرج التاريخ فقط
                        if ' ' in str(payment_date):
                            payment_date_only = str(payment_date).split(' ')[0]
                        else:
                            payment_date_only = str(payment_date)
                    except:
                        payment_date_only = str(payment_date) if payment_date else None

                # مفتاح التجميع: الشهر + السنة + تاريخ الدفع (بدون توقيت)
                key = f"{month} {year} - {payment_date_only or 'غير محدد'}"

                if key not in monthly_summary:
                    monthly_summary[key] = {
                        'month': month, 'year': year,
                        'required': 0, 'paid': 0, 'remaining': 0,
                        'sections': [], 'payment_date': payment_date, 'status': payment_status
                    }

                monthly_summary[key]['required'] += amount_required or 0
                monthly_summary[key]['paid'] += amount_paid or 0
                monthly_summary[key]['remaining'] += amount_remaining or 0

                if section and section not in monthly_summary[key]['sections']:
                    monthly_summary[key]['sections'].append(section)

                if section:
                    all_sections.add(section)

                total_required += amount_required or 0
                total_paid += amount_paid or 0
                total_remaining += amount_remaining or 0

            # عرض الأقسام المسجل بها
            receipt_lines.append(f"الأقسام: {', '.join(sorted(all_sections))}".rjust(40))
            receipt_lines.append("-" * 40)

            # عرض الواجبات الشهرية المجمعة
            for key, summary in monthly_summary.items():
                receipt_lines.append(f"الشهر: {summary['month']} {summary['year']}".rjust(40))
                receipt_lines.append(f"المطلوب: {summary['required']:.2f} درهم".rjust(40))
                receipt_lines.append(f"المدفوع: {summary['paid']:.2f} درهم".rjust(40))
                receipt_lines.append(f"المتبقي: {summary['remaining']:.2f} درهم".rjust(40))
                receipt_lines.append(f"تاريخ الدفع: {summary['payment_date'] or 'غير محدد'}".rjust(40))
                receipt_lines.append(f"الحالة: {summary['status']}".rjust(40))
                receipt_lines.append("-" * 30)

            # تذييل التوصيل
            from datetime import datetime
            receipt_lines.append(f"تاريخ الطباعة {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}".rjust(40))
            receipt_lines.append("")  # سطر فارغ
            receipt_lines.append("نتمنى لكم التوفيق والنجاح".center(40))

            return "\n".join(receipt_lines)

        except Exception as e:
            print(f"❌ [ERROR] خطأ في إنشاء التوصيل الموحد للواجبات الشهرية: {str(e)}")
            return f"خطأ في إنشاء التوصيل: {str(e)}"

    def create_unified_registration_receipt_content(self, payments):
        """إنشاء محتوى التوصيل الموحد لواجبات التسجيل - نسخة طبق الأصل"""
        try:
            # الحصول على بيانات المؤسسة
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT المؤسسة, رقم_الهاتف, المدينة FROM بيانات_المؤسسة LIMIT 1")
            institution_data = cursor.fetchone()
            conn.close()

            institution_name = institution_data[0] if institution_data and institution_data[0] else "المؤسسة التعليمية"
            institution_phone = institution_data[1] if institution_data and institution_data[1] else ""
            institution_city = institution_data[2] if institution_data and institution_data[2] else ""

            # بناء محتوى الوصل - تصميم مخصص مطابق للأصل
            receipt_lines = []

            # إطار حول اسم المؤسسة والمدينة
            receipt_lines.append("=" * 40)
            receipt_lines.append(f"{institution_name}".center(40))
            if institution_city:
                receipt_lines.append(f"{institution_city}".center(40))
            receipt_lines.append("=" * 40)

            # الهاتف خارج الإطار
            if institution_phone:
                receipt_lines.append(f"هاتف: {institution_phone}".center(40))

            # وصل التسجيل في الوسط
            receipt_lines.append("وصل التسجيل".center(40))

            # بيانات التلميذ - محاذاة إلى اليمين
            if payments:
                student_name = payments[0][5]  # اسم التلميذ
                student_code = payments[0][6]  # رمز التلميذ
                student_phone = payments[0][7]  # رقم الهاتف

                receipt_lines.append(f"اسم التلميذ: {student_name or 'غير محدد'}".rjust(40))
                receipt_lines.append(f"رمز التلميذ: {student_code or 'غير محدد'}".rjust(40))
                if student_phone:
                    receipt_lines.append(f"رقم الهاتف: {student_phone}".rjust(40))

            receipt_lines.append("-" * 40)

            # تجميع البيانات حسب نوع الدفعة وتاريخ الدفع (اليوم فقط، ليس التوقيت)
            payment_summary = {}
            total_paid = 0
            all_sections = set()

            for payment in payments:
                payment_type, amount_paid, payment_date, payment_method, section = payment[0], payment[1], payment[2], payment[3], payment[4]

                if section:
                    all_sections.add(section)

                # استخراج التاريخ فقط (بدون التوقيت) للتجميع
                payment_date_only = None
                if payment_date:
                    try:
                        # إذا كان التاريخ يحتوي على توقيت، استخرج التاريخ فقط
                        if ' ' in str(payment_date):
                            payment_date_only = str(payment_date).split(' ')[0]
                        else:
                            payment_date_only = str(payment_date)
                    except:
                        payment_date_only = str(payment_date) if payment_date else None

                # مفتاح التجميع: نوع الدفعة + تاريخ الدفع (بدون توقيت)
                key = f"{payment_type} - {payment_date_only or 'غير محدد'}"

                if key not in payment_summary:
                    payment_summary[key] = {
                        'payment_type': payment_type,
                        'payment_date': payment_date,
                        'total_amount': 0, 'payments': [], 'sections': set()
                    }

                payment_summary[key]['total_amount'] += amount_paid or 0
                payment_summary[key]['payments'].append({
                    'amount': amount_paid, 'date': payment_date, 'method': payment_method, 'section': section
                })

                if section:
                    payment_summary[key]['sections'].add(section)

                total_paid += amount_paid or 0

            # عرض الأقسام المسجل بها
            receipt_lines.append(f"الأقسام: {', '.join(sorted(all_sections))}".rjust(40))
            receipt_lines.append("-" * 40)

            # عرض واجبات التسجيل المجمعة
            for key, summary in payment_summary.items():
                receipt_lines.append(f"نوع الدفعة: {summary['payment_type']}".rjust(40))
                receipt_lines.append(f"تاريخ الدفع: {summary['payment_date'] or 'غير محدد'}".rjust(40))
                receipt_lines.append(f"إجمالي المبلغ: {summary['total_amount']:.2f} درهم".rjust(40))

                # تفاصيل الدفعات
                for i, payment_detail in enumerate(summary['payments'], 1):
                    receipt_lines.append(f"الدفعة {i}: {payment_detail['amount']:.2f} درهم".rjust(40))
                    receipt_lines.append(f"الطريقة: {payment_detail['method'] or 'نقداً'}".rjust(40))
                    if payment_detail['section']:
                        receipt_lines.append(f"القسم: {payment_detail['section']}".rjust(40))

                receipt_lines.append("-" * 30)

            # تذييل التوصيل
            from datetime import datetime
            receipt_lines.append(f"تاريخ الطباعة {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}".rjust(40))
            receipt_lines.append("")  # سطر فارغ
            receipt_lines.append("نتمنى لكم التوفيق والنجاح".center(40))

            return "\n".join(receipt_lines)

        except Exception as e:
            print(f"❌ [ERROR] خطأ في إنشاء التوصيل الموحد لواجبات التسجيل: {str(e)}")
            return f"خطأ في إنشاء التوصيل: {str(e)}"

# دالة اختبار
if __name__ == "__main__":
    import sys
    from PyQt5.QtWidgets import QApplication

    app = QApplication(sys.argv)

    # اختبار النافذة
    window = MultiSectionDutiesWindow(student_code="12345")
    window.show()

    sys.exit(app.exec_())
