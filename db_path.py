#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف بسيط لتوحيد مسار قاعدة البيانات
الهدف الوحيد: جعل جميع الملفات تجد قاعدة البيانات في نفس المكان
"""

import os
import sys

def get_db_path():
    """
    إرجاع مسار قاعدة البيانات الصحيح
    - في البيئة العادية: نفس مجلد الملفات
    - بعد التحزيم: داخل مجلد _internal
    """
    if getattr(sys, 'frozen', False):
        # التطبيق محزم - قاعدة البيانات في مجلد _internal
        app_dir = os.path.dirname(sys.executable)
        internal_dir = os.path.join(app_dir, "_internal")
        return os.path.join(internal_dir, "school_database.db")
    else:
        # التطبيق يعمل من الكود - قاعدة البيانات في نفس مجلد الملفات
        app_dir = os.path.dirname(os.path.abspath(__file__))
        return os.path.join(app_dir, "school_database.db")

# للاختبار
if __name__ == "__main__":
    db_path = get_db_path()
    print(f"مسار قاعدة البيانات: {db_path}")
    print(f"قاعدة البيانات موجودة: {'نعم' if os.path.exists(db_path) else 'لا'}")
