#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تحديث قوائم الأقسام
"""

import sys
import sqlite3
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, Q<PERSON>abel
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("اختبار تحديث الأقسام")
        self.setGeometry(100, 100, 400, 300)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # إعداد الواجهة
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # عنوان
        title = QLabel("اختبار تحديث قوائم الأقسام")
        title.setFont(QFont("Calibri", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # زر فتح نافذة الأساتذة
        btn_teachers = QPushButton("فتح نافذة إدارة الأساتذة")
        btn_teachers.setFont(QFont("Calibri", 12, QFont.Bold))
        btn_teachers.clicked.connect(self.open_teachers_window)
        layout.addWidget(btn_teachers)
        
        # زر فتح نافذة التسجيل
        btn_registration = QPushButton("فتح نافذة التسجيل")
        btn_registration.setFont(QFont("Calibri", 12, QFont.Bold))
        btn_registration.clicked.connect(self.open_registration_window)
        layout.addWidget(btn_registration)
        
        # زر فتح نافذة الأقسام
        btn_sections = QPushButton("فتح نافذة الأقسام")
        btn_sections.setFont(QFont("Calibri", 12, QFont.Bold))
        btn_sections.clicked.connect(self.open_sections_window)
        layout.addWidget(btn_sections)
        
        # تسمية للتعليمات
        instructions = QLabel("""
التعليمات:
1. افتح نافذة التسجيل أولاً
2. لاحظ الأقسام المتاحة في القائمة المنسدلة
3. افتح نافذة إدارة الأساتذة
4. أضف أستاذاً جديداً مع قسم جديد
5. ارجع إلى نافذة التسجيل
6. اضغط زر التحديث أو أعد فتح النافذة
7. تحقق من ظهور القسم الجديد
        """)
        instructions.setFont(QFont("Calibri", 10))
        instructions.setWordWrap(True)
        layout.addWidget(instructions)
        
        # متغيرات لحفظ النوافذ
        self.teachers_window = None
        self.registration_window = None
        self.sections_window = None
    
    def open_teachers_window(self):
        """فتح نافذة إدارة الأساتذة"""
        try:
            from sub262_window import TeachersManagementWindow
            self.teachers_window = TeachersManagementWindow()
            self.teachers_window.show()
        except Exception as e:
            print(f"خطأ في فتح نافذة الأساتذة: {str(e)}")
    
    def open_registration_window(self):
        """فتح نافذة التسجيل"""
        try:
            from sub232_window import MonthlyDutiesWindow
            self.registration_window = MonthlyDutiesWindow()
            self.registration_window.show()
        except Exception as e:
            print(f"خطأ في فتح نافذة التسجيل: {str(e)}")
    
    def open_sections_window(self):
        """فتح نافذة الأقسام"""
        try:
            from sub252_window import StudentsListWindow
            self.sections_window = StudentsListWindow()
            self.sections_window.show()
        except Exception as e:
            print(f"خطأ في فتح نافذة الأقسام: {str(e)}")

def main():
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # التحقق من وجود قاعدة البيانات
    try:
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # التحقق من وجود الجداول المطلوبة
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='جدول_المواد_والاقسام'")
        if not cursor.fetchone():
            print("تحذير: جدول المواد والأقسام غير موجود")
        
        conn.close()
        print("✅ قاعدة البيانات متاحة")
        
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {str(e)}")
    
    window = TestWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
