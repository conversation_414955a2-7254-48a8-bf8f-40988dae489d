# PyInstaller hook for jaraco package
# This hook ensures that all jaraco submodules are properly included

from PyInstaller.utils.hooks import collect_all

# Collect all jaraco modules
datas, binaries, hiddenimports = collect_all('jaraco')

# Add specific jaraco modules that are commonly missing
hiddenimports += [
    'jaraco.text',
    'jaraco.functools',
    'jaraco.context',
    'jaraco.collections',
    'jaraco.itertools',
    'jaraco.classes',
]

# Also collect pkg_resources related modules
pkg_datas, pkg_binaries, pkg_hiddenimports = collect_all('pkg_resources')
datas += pkg_datas
binaries += pkg_binaries
hiddenimports += pkg_hiddenimports

# Add more_itertools which is often required by jaraco
try:
    more_datas, more_binaries, more_hiddenimports = collect_all('more_itertools')
    datas += more_datas
    binaries += more_binaries
    hiddenimports += more_hiddenimports
except:
    pass

# Add importlib_metadata
try:
    meta_datas, meta_binaries, meta_hiddenimports = collect_all('importlib_metadata')
    datas += meta_datas
    binaries += meta_binaries
    hiddenimports += meta_hiddenimports
except:
    pass
