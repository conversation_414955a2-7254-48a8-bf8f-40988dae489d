#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os
from datetime import datetime

class ArchivedAccountsManager:
    """مدير الحسابات المرحلة - نظام احترافي لترحيل البيانات الشهرية"""
    
    def __init__(self, db_path='data.db'):
        self.db_path = db_path
        self.create_archived_accounts_table()
    
    def create_archived_accounts_table(self):
        """إنشاء جدول الحسابات المرحلة"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # إنشاء جدول الحسابات المرحلة
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS الحسابات_المرحلة (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    student_id INTEGER NOT NULL,
                    اسم_التلميذ TEXT NOT NULL,
                    رمز_التلميذ TEXT,
                    القسم TEXT NOT NULL,
                    اسم_الاستاذ TEXT,
                    المجموعة TEXT,
                    month TEXT NOT NULL,
                    year INTEGER NOT NULL,
                    amount_required REAL DEFAULT 0,
                    amount_paid REAL DEFAULT 0,
                    amount_remaining REAL DEFAULT 0,
                    payment_status TEXT DEFAULT 'غير مدفوع',
                    payment_date TEXT,
                    notes TEXT,
                    ترحيل_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    ترحيل_by TEXT DEFAULT 'النظام',
                    is_archived BOOLEAN DEFAULT 1,
                    UNIQUE(student_id, month, year)
                )
            """)
            
            # إنشاء فهارس لتحسين الأداء
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_archived_section_month_year ON الحسابات_المرحلة(القسم, month, year)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_archived_student_month_year ON الحسابات_المرحلة(student_id, month, year)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_archived_teacher ON الحسابات_المرحلة(اسم_الاستاذ)")
            
            conn.commit()
            conn.close()
            
            print("✅ تم إنشاء جدول الحسابات المرحلة بنجاح")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء جدول الحسابات المرحلة: {e}")
    
    def archive_monthly_accounts(self, month, year, force_update=False):
        """ترحيل الحسابات الشهرية - العملية الأساسية"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            print(f"🔄 بدء ترحيل الحسابات لشهر {month}/{year}")
            
            # فحص إذا كان الشهر مرحل مسبقاً
            cursor.execute("""
                SELECT COUNT(*) FROM الحسابات_المرحلة 
                WHERE month = ? AND year = ?
            """, (month, year))
            
            existing_count = cursor.fetchone()[0]
            
            if existing_count > 0 and not force_update:
                print(f"⚠️ الشهر {month}/{year} مرحل مسبقاً ({existing_count} سجل)")
                print("استخدم force_update=True لإعادة الترحيل")
                conn.close()
                return False, f"الشهر مرحل مسبقاً"
            
            if force_update and existing_count > 0:
                print(f"🗑️ حذف الترحيل السابق ({existing_count} سجل)")
                cursor.execute("""
                    DELETE FROM الحسابات_المرحلة 
                    WHERE month = ? AND year = ?
                """, (month, year))
            
            # جلب البيانات للترحيل مع المادة والنوع
            cursor.execute("""
                SELECT
                    md.student_id,
                    jb.اسم_التلميذ,
                    jb.رمز_التلميذ,
                    COALESCE(md.القسم, jb.القسم) as القسم,
                    COALESCE(md.اسم_الاستاذ, mat.اسم_الاستاذ) as اسم_الاستاذ,
                    mat.المجموعة,
                    COALESCE(md.المادة, mat.المادة) as المادة,
                    COALESCE(md.النوع, jb.النوع) as النوع,
                    md.month,
                    md.year,
                    md.amount_required,
                    md.amount_paid,
                    md.amount_remaining,
                    md.payment_status,
                    md.payment_date,
                    md.notes
                FROM monthly_duties md
                JOIN جدول_البيانات jb ON md.student_id = jb.id
                LEFT JOIN جدول_المواد_والاقسام mat ON COALESCE(md.القسم, jb.القسم) = mat.القسم
                WHERE md.month = ? AND md.year = ?
            """, (month, year))
            
            monthly_data = cursor.fetchall()
            
            if not monthly_data:
                print(f"⚠️ لا توجد بيانات مالية لشهر {month}/{year}")
                conn.close()
                return False, "لا توجد بيانات للترحيل"
            
            # ترحيل البيانات
            archived_count = 0
            for data in monthly_data:
                try:
                    cursor.execute("""
                        INSERT INTO الحسابات_المرحلة
                        (student_id, اسم_التلميذ, رمز_التلميذ, القسم, اسم_الاستاذ, المجموعة, المادة, النوع,
                         month, year, amount_required, amount_paid, amount_remaining,
                         payment_status, payment_date, notes, ترحيل_date, ترحيل_by)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (*data, datetime.now().strftime('%Y-%m-%d %H:%M:%S'), 'النظام'))
                    archived_count += 1
                except sqlite3.IntegrityError:
                    # تجاهل التكرارات
                    pass
            
            conn.commit()
            conn.close()
            
            print(f"✅ تم ترحيل {archived_count} سجل لشهر {month}/{year}")
            return True, f"تم ترحيل {archived_count} سجل"
            
        except Exception as e:
            print(f"❌ خطأ في ترحيل الحسابات: {e}")
            return False, str(e)
    
    def get_archived_months(self):
        """جلب قائمة الشهور المرحلة"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT DISTINCT month, year, COUNT(*) as record_count,
                       MIN(ترحيل_date) as first_archive,
                       MAX(ترحيل_date) as last_archive
                FROM الحسابات_المرحلة
                GROUP BY month, year
                ORDER BY year DESC, 
                    CASE month
                        WHEN 'يناير' THEN 1
                        WHEN 'فبراير' THEN 2
                        WHEN 'مارس' THEN 3
                        WHEN 'أبريل' THEN 4
                        WHEN 'مايو' THEN 5
                        WHEN 'يونيو' THEN 6
                        WHEN 'يوليو' THEN 7
                        WHEN 'أغسطس' THEN 8
                        WHEN 'سبتمبر' THEN 9
                        WHEN 'أكتوبر' THEN 10
                        WHEN 'نوفمبر' THEN 11
                        WHEN 'ديسمبر' THEN 12
                        ELSE 13
                    END DESC
            """)
            
            archived_months = cursor.fetchall()
            conn.close()
            
            return archived_months
            
        except Exception as e:
            print(f"❌ خطأ في جلب الشهور المرحلة: {e}")
            return []
    
    def get_archived_data_by_section_month(self, section, month, year):
        """جلب البيانات المرحلة لقسم وشهر محددين"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT 
                    اسم_التلميذ,
                    رمز_التلميذ,
                    amount_required,
                    amount_paid,
                    amount_remaining,
                    payment_status,
                    payment_date,
                    notes,
                    اسم_الاستاذ
                FROM الحسابات_المرحلة
                WHERE القسم = ? AND month = ? AND year = ?
                ORDER BY اسم_التلميذ
            """, (section, month, year))
            
            archived_data = cursor.fetchall()
            conn.close()
            
            return archived_data
            
        except Exception as e:
            print(f"❌ خطأ في جلب البيانات المرحلة: {e}")
            return []
    
    def get_archived_summary_by_section_year(self, section, year):
        """جلب ملخص سنوي من البيانات المرحلة"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT 
                    month,
                    اسم_الاستاذ,
                    COUNT(*) as student_count,
                    SUM(amount_required) as total_required,
                    SUM(amount_paid) as total_paid,
                    SUM(amount_remaining) as total_remaining
                FROM الحسابات_المرحلة
                WHERE القسم = ? AND year = ?
                GROUP BY month, اسم_الاستاذ
                ORDER BY 
                    CASE month
                        WHEN 'يناير' THEN 1
                        WHEN 'فبراير' THEN 2
                        WHEN 'مارس' THEN 3
                        WHEN 'أبريل' THEN 4
                        WHEN 'مايو' THEN 5
                        WHEN 'يونيو' THEN 6
                        WHEN 'يوليو' THEN 7
                        WHEN 'أغسطس' THEN 8
                        WHEN 'سبتمبر' THEN 9
                        WHEN 'أكتوبر' THEN 10
                        WHEN 'نوفمبر' THEN 11
                        WHEN 'ديسمبر' THEN 12
                        ELSE 13
                    END
            """, (section, year))
            
            summary_data = cursor.fetchall()
            conn.close()
            
            return summary_data
            
        except Exception as e:
            print(f"❌ خطأ في جلب الملخص السنوي: {e}")
            return []
    
    def delete_archived_month(self, month, year):
        """حذف ترحيل شهر معين"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                DELETE FROM الحسابات_المرحلة 
                WHERE month = ? AND year = ?
            """, (month, year))
            
            deleted_count = cursor.rowcount
            conn.commit()
            conn.close()
            
            print(f"✅ تم حذف {deleted_count} سجل من شهر {month}/{year}")
            return True, f"تم حذف {deleted_count} سجل"
            
        except Exception as e:
            print(f"❌ خطأ في حذف الترحيل: {e}")
            return False, str(e)

# اختبار النظام
if __name__ == "__main__":
    print("🧪 اختبار نظام الحسابات المرحلة")
    print("=" * 50)
    
    # إنشاء مدير الحسابات المرحلة
    manager = ArchivedAccountsManager()
    
    # عرض الشهور المرحلة
    archived_months = manager.get_archived_months()
    print(f"\n📋 الشهور المرحلة حالياً: {len(archived_months)}")
    for month_data in archived_months:
        print(f"   📅 {month_data[0]}/{month_data[1]} - {month_data[2]} سجل")
    
    print("\n✅ النظام جاهز للاستخدام!")
    print("💡 استخدم manager.archive_monthly_accounts('يناير', 2024) لترحيل شهر")
