# ================= نافذة إعداد الموازنة السنوية =================
# ملف منفصل لإدارة الموازنة السنوية للمؤسسة التعليمية
# يحتوي على: تخطيط الإيرادات والمصاريف، مقارنة المتوقع مع الفعلي، أشرطة التقدم

import sys
import os
import sqlite3
from datetime import datetime, date
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

# استيراد matplotlib اختياري
try:
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
    from matplotlib.figure import Figure
    import numpy as np
from db_path import get_db_path
    # تعيين الخط العربي لـ matplotlib
    plt.rcParams['font.family'] = ['Arial Unicode MS', 'Tahoma', 'DejaVu Sans']
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("تحذير: matplotlib غير متوفر. الرسوم البيانية ستكون معطلة.")

class BudgetManager:
    """مدير الموازنة السنوية"""
    
    def __init__(self, db_path="data.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """إنشاء جداول الموازنة"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # جدول السنوات المالية
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS السنوات_المالية (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    السنة_المالية INTEGER UNIQUE NOT NULL,
                    تاريخ_البداية DATE NOT NULL,
                    تاريخ_النهاية DATE NOT NULL,
                    الحالة TEXT DEFAULT 'نشطة',
                    ملاحظات TEXT,
                    تاريخ_الإنشاء TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    تاريخ_التحديث TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # جدول إعدادات النظام
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS إعدادات_النظام (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    اسم_الإعداد TEXT UNIQUE NOT NULL,
                    قيمة_الإعداد TEXT NOT NULL,
                    وصف_الإعداد TEXT,
                    تاريخ_التحديث TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # جدول الموازنة السنوية المحدث
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS الموازنة_السنوية (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    السنة_المالية INTEGER NOT NULL,
                    نوع_البند TEXT NOT NULL,
                    اسم_البند TEXT NOT NULL,
                    المبلغ_المتوقع REAL NOT NULL DEFAULT 0,
                    المبلغ_الفعلي REAL DEFAULT 0,
                    النسبة_المحققة REAL DEFAULT 0,
                    الحالة TEXT DEFAULT 'نشط',
                    تاريخ_الإنشاء TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    تاريخ_التحديث TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (السنة_المالية) REFERENCES السنوات_المالية(السنة_المالية),
                    UNIQUE(السنة_المالية, نوع_البند, اسم_البند)
                )
            """)
            
            # جدول بنود الموازنة الافتراضية
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS بنود_الموازنة_الافتراضية (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    نوع_البند TEXT NOT NULL,
                    اسم_البند TEXT NOT NULL,
                    وصف_البند TEXT,
                    نشط BOOLEAN DEFAULT 1
                )
            """)
            
            # إدراج البنود الافتراضية
            default_budget_items = [
                # الإيرادات
                ('إيرادات', 'رسوم التسجيل', 'رسوم تسجيل التلاميذ الجدد'),
                ('إيرادات', 'الواجبات الشهرية', 'الرسوم الشهرية للتلاميذ'),
                ('إيرادات', 'رسوم إضافية', 'رسوم الأنشطة والخدمات الإضافية'),
                ('إيرادات', 'منح ودعم', 'المنح والدعم من الجهات المختلفة'),
                ('إيرادات', 'إيرادات أخرى', 'إيرادات متنوعة أخرى'),
                
                # المصاريف
                ('مصاريف', 'رواتب المدرسين', 'رواتب الكادر التدريسي'),
                ('مصاريف', 'رواتب الإداريين', 'رواتب الكادر الإداري'),
                ('مصاريف', 'إيجار المباني', 'تكلفة إيجار المرافق'),
                ('مصاريف', 'فواتير الخدمات', 'كهرباء، ماء، هاتف، إنترنت'),
                ('مصاريف', 'المعدات التعليمية', 'شراء وصيانة المعدات'),
                ('مصاريف', 'القرطاسية', 'المواد المكتبية والتعليمية'),
                ('مصاريف', 'النقل', 'مصاريف النقل والمواصلات'),
                ('مصاريف', 'التأمين', 'أقساط التأمين المختلفة'),
                ('مصاريف', 'الصيانة', 'صيانة المباني والمرافق'),
                ('مصاريف', 'التسويق', 'الدعاية والإعلان'),
                ('مصاريف', 'مصاريف أخرى', 'مصاريف متنوعة أخرى')
            ]
            
            cursor.executemany("""
                INSERT OR IGNORE INTO بنود_الموازنة_الافتراضية (نوع_البند, اسم_البند, وصف_البند)
                VALUES (?, ?, ?)
            """, default_budget_items)

            # إدراج السنوات المالية الافتراضية
            current_year = datetime.now().year
            default_financial_years = []
            for year in range(current_year - 2, current_year + 3):
                start_date = f"{year}-09-01"  # بداية السنة الدراسية
                end_date = f"{year + 1}-08-31"  # نهاية السنة الدراسية
                default_financial_years.append((year, start_date, end_date, 'نشطة' if year == current_year else 'غير نشطة'))

            cursor.executemany("""
                INSERT OR IGNORE INTO السنوات_المالية (السنة_المالية, تاريخ_البداية, تاريخ_النهاية, الحالة)
                VALUES (?, ?, ?, ?)
            """, default_financial_years)

            # إعداد السنة المالية الافتراضية إذا لم تكن محفوظة
            cursor.execute("""
                INSERT OR IGNORE INTO إعدادات_النظام
                (اسم_الإعداد, قيمة_الإعداد, وصف_الإعداد)
                VALUES (?, ?, ?)
            """, ("السنة_المالية_المختارة", str(current_year), "السنة المالية المختارة افتراضياً"))

            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"خطأ في إنشاء قاعدة بيانات الموازنة: {str(e)}")

class BudgetPlanningWindow(QMainWindow):
    """نافذة إعداد الموازنة السنوية"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.manager = BudgetManager()
        self.current_year = 2024  # استخدام 2024 حيث توجد البيانات
        print(f"🚀 بدء تشغيل نافذة الموازنة للسنة {self.current_year}")
        self.create_budget_table_if_not_exists()
        self.init_ui()
        self.load_saved_budget_data()
    
    def center_window(self, window):
        """توسيط النافذة في وسط الشاشة"""
        try:
            screen = QApplication.desktop().screenGeometry()
            window_size = window.geometry()
            x = (screen.width() - window_size.width()) // 2
            y = (screen.height() - window_size.height()) // 2
            window.move(x, y)
        except Exception as e:
            print(f"خطأ في توسيط النافذة: {str(e)}")
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("📊 إعداد الموازنة السنوية")
        self.showMaximized()  # فتح في كامل الشاشة
        self.setLayoutDirection(Qt.RightToLeft)

        # الويدجت الرئيسي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)

        # شريط العنوان مع الأزرار
        title_layout = QHBoxLayout()

        # اختيار السنة
        year_widget = QWidget()
        year_widget.setStyleSheet("""
            QWidget {
                background-color: #2c3e50;
                border-radius: 8px;
                padding: 5px;
            }
        """)
        year_layout = QVBoxLayout(year_widget)

        year_label = QLabel("📅 السنة:")
        year_label.setFont(QFont("Calibri", 12, QFont.Bold))
        year_label.setStyleSheet("color: white;")
        year_layout.addWidget(year_label)

        self.year_combo = QComboBox()
        self.year_combo.setFont(QFont("Calibri", 12, QFont.Bold))
        self.year_combo.setStyleSheet("""
            QComboBox {
                background-color: white;
                border: 2px solid #3498db;
                border-radius: 5px;
                padding: 5px;
                min-width: 80px;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                image: none;
                border: none;
            }
        """)

        # تحديث قائمة السنوات من قاعدة البيانات
        self.update_year_combo()

        # استعادة السنة المالية المحفوظة
        if not self.load_saved_financial_year():
            # إذا لم توجد سنة محفوظة، استخدم السنة الحالية
            current_year_index = self.year_combo.findText(str(self.current_year))
            if current_year_index >= 0:
                self.year_combo.setCurrentIndex(current_year_index)

        self.year_combo.currentTextChanged.connect(self.on_year_changed)
        year_layout.addWidget(self.year_combo)

        title_layout.addWidget(year_widget)

        # العنوان الرئيسي
        title_label = QLabel("📊 إعداد الموازنة السنوية")
        title_label.setFont(QFont("Calibri", 15, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                background-color: #2c3e50;
                color: white;
                padding: 15px;
                border-radius: 8px;
            }
        """)
        title_layout.addWidget(title_label)

        # أزرار الإعداد
        buttons_widget = QWidget()
        buttons_widget.setStyleSheet("""
            QWidget {
                background-color: #2c3e50;
                border-radius: 8px;
                padding: 5px;
            }
        """)
        buttons_layout = QVBoxLayout(buttons_widget)

        # زر إعداد الإيرادات
        setup_revenue_btn = QPushButton("⚙️ إعداد الإيرادات")
        setup_revenue_btn.setFont(QFont("Calibri", 11, QFont.Bold))
        setup_revenue_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border-radius: 6px;
                padding: 8px 15px;
                margin: 2px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        setup_revenue_btn.clicked.connect(self.open_revenue_setup_window)
        buttons_layout.addWidget(setup_revenue_btn)

        # زر إعداد المصاريف
        setup_expenses_btn = QPushButton("⚙️ إعداد المصاريف")
        setup_expenses_btn.setFont(QFont("Calibri", 11, QFont.Bold))
        setup_expenses_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border-radius: 6px;
                padding: 8px 15px;
                margin: 2px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        setup_expenses_btn.clicked.connect(self.open_expenses_setup_window)
        buttons_layout.addWidget(setup_expenses_btn)

        # زر إعداد بداية السنة المالية
        setup_financial_year_btn = QPushButton("📅 إعداد السنة المالية")
        setup_financial_year_btn.setFont(QFont("Calibri", 11, QFont.Bold))
        setup_financial_year_btn.setStyleSheet("""
            QPushButton {
                background-color: #9b59b6;
                color: white;
                border-radius: 6px;
                padding: 8px 15px;
                margin: 2px;
            }
            QPushButton:hover {
                background-color: #8e44ad;
            }
        """)
        setup_financial_year_btn.clicked.connect(self.open_financial_year_setup_window)
        buttons_layout.addWidget(setup_financial_year_btn)

        title_layout.addWidget(buttons_widget)
        main_layout.addLayout(title_layout)

        # تخطيط أفقي للأقسام الثلاثة مع تساوي العرض
        sections_layout = QHBoxLayout()
        sections_layout.setSpacing(10)  # مسافة بين الأقسام

        # قسم الإيرادات والمداخل
        self.create_revenue_section(sections_layout)

        # قسم المصاريف والنفقات
        self.create_expenses_section(sections_layout)

        # قسم الموازنة
        self.create_budget_section(sections_layout)

        # جعل الأقسام متساوية في العرض
        sections_layout.setStretchFactor(sections_layout.itemAt(0).widget(), 1)
        sections_layout.setStretchFactor(sections_layout.itemAt(1).widget(), 1)
        sections_layout.setStretchFactor(sections_layout.itemAt(2).widget(), 1)

        main_layout.addLayout(sections_layout)

        # شريط الحالة
        self.statusBar().setFont(QFont("Calibri", 13, QFont.Bold))
        self.statusBar().showMessage(f"جاهز - نظام إعداد الموازنة السنوية {self.current_year}")

    def create_budget_table_if_not_exists(self):
        """إنشاء جدول الموازنة السنوية إذا لم يكن موجود<|im_start|>"""
        try:
            conn = sqlite3.connect(self.manager.db_path)
            cursor = conn.cursor()

            # التحقق من وجود الجدول
            cursor.execute("""
                SELECT name FROM sqlite_master
                WHERE type='table' AND name='الموازنة_السنوية'
            """)

            if not cursor.fetchone():
                print("📋 إنشاء جدول الموازنة السنوية...")
                # إنشاء الجدول
                cursor.execute("""
                    CREATE TABLE الموازنة_السنوية (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        السنة INTEGER NOT NULL,
                        نوع_البند TEXT NOT NULL,
                        اسم_البند TEXT NOT NULL,
                        المبلغ_المتوقع REAL DEFAULT 0,
                        المبلغ_الفعلي REAL DEFAULT 0,
                        تاريخ_الإنشاء DATETIME DEFAULT CURRENT_TIMESTAMP,
                        تاريخ_التحديث DATETIME DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(السنة, نوع_البند, اسم_البند)
                    )
                """)
                print("✅ تم إنشاء جدول الموازنة السنوية بنجاح")
            else:
                print("✅ جدول الموازنة السنوية موجود")

            # التحقق من وجود بيانات في الجدول
            cursor.execute("SELECT COUNT(*) FROM الموازنة_السنوية WHERE السنة_المالية = ?", (self.current_year,))
            count = cursor.fetchone()[0]

            if count == 0:
                print(f"📋 إدراج بيانات تجريبية للسنة {self.current_year}...")
                self.insert_sample_budget_data(cursor)

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"❌ خطأ في إنشاء جدول الموازنة السنوية: {str(e)}")

    def insert_sample_budget_data(self, cursor):
        """إدراج بيانات تجريبية للموازنة"""
        try:
            # بيانات الإيرادات التجريبية
            revenue_data = [
                ("registration_fees", 50000),
                ("monthly_duties", 300000),
                ("other_revenue", 25000)
            ]

            for revenue_key, amount in revenue_data:
                cursor.execute("""
                    INSERT OR REPLACE INTO الموازنة_السنوية
                    (السنة_المالية, نوع_البند, اسم_البند, المبلغ_المتوقع, المبلغ_الفعلي)
                    VALUES (?, ?, ?, ?, ?)
                """, (self.current_year, "إيرادات", revenue_key, amount, 0))

            # بيانات المصاريف التجريبية
            expense_data = [
                ("رواتب", 200000),
                ("كراء", 60000),
                ("فواتير وأقساط", 30000),
                ("معدات", 15000),
                ("صيانة وتجهيز", 10000),
                ("إعلانات", 5000),
                ("أدوات وأجهزة", 8000),
                ("أخرى", 12000)
            ]

            for expense_type, amount in expense_data:
                cursor.execute("""
                    INSERT OR REPLACE INTO الموازنة_السنوية
                    (السنة_المالية, نوع_البند, اسم_البند, المبلغ_المتوقع, المبلغ_الفعلي)
                    VALUES (?, ?, ?, ?, ?)
                """, (self.current_year, "مصاريف", expense_type, amount, 0))

            print("✅ تم إدراج البيانات التجريبية بنجاح")

        except Exception as e:
            print(f"❌ خطأ في إدراج البيانات التجريبية: {str(e)}")

    def on_year_changed(self, new_year_text):
        """معالجة تغيير السنة"""
        try:
            new_year = int(new_year_text)
            if new_year != self.current_year:
                print(f"🔄 تغيير السنة من {self.current_year} إلى {new_year}")
                self.current_year = new_year

                # حفظ السنة المختارة في قاعدة البيانات
                self.save_selected_financial_year(new_year)

                # تحديث شريط الحالة
                self.statusBar().showMessage(f"جاهز - نظام إعداد الموازنة السنوية {self.current_year}")

                # التحقق من وجود بيانات للسنة الجديدة وإنشاؤها إذا لزم الأمر
                self.check_and_create_year_data()

                # إعادة تحميل البيانات للسنة الجديدة
                self.load_saved_budget_data()

                print(f"✅ تم تحديث السنة المالية إلى {new_year} وحفظ الاختيار")

        except ValueError:
            print(f"❌ خطأ في تحويل السنة: {new_year_text}")

    def check_and_create_year_data(self):
        """التحقق من وجود بيانات للسنة وإنشاؤها إذا لزم الأمر"""
        try:
            conn = sqlite3.connect(self.manager.db_path)
            cursor = conn.cursor()

            # التحقق من وجود بيانات للسنة الحالية
            cursor.execute("SELECT COUNT(*) FROM الموازنة_السنوية WHERE السنة_المالية = ?", (self.current_year,))
            count = cursor.fetchone()[0]

            if count == 0:
                print(f"📋 إنشاء بيانات جديدة للسنة {self.current_year}...")
                self.insert_sample_budget_data(cursor)
                print(f"✅ تم إنشاء بيانات السنة {self.current_year} بنجاح")
            else:
                print(f"✅ بيانات السنة {self.current_year} موجودة ({count} سجل)")

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"❌ خطأ في التحقق من بيانات السنة: {str(e)}")

    def create_revenue_section(self, sections_layout):
        """إنشاء قسم الإيرادات والمداخل"""
        revenue_group = QGroupBox("💰 الإيرادات والمداخل")
        revenue_group.setFont(QFont("Calibri", 14, QFont.Bold))
        revenue_group.setFixedHeight(500)  # ارتفاع ثابت
        revenue_layout = QVBoxLayout(revenue_group)

        # أنواع المداخل الثلاثة
        revenue_types = [
            ("واجبات التسجيل والاشتراكات", "registration_fees"),
            ("الواجبات الشهرية", "monthly_duties"),
            ("أخرى", "other_revenue")
        ]

        self.revenue_progress_bars = {}

        for revenue_name, revenue_key in revenue_types:
            # عنوان النوع
            type_label = QLabel(f"📋 {revenue_name}")
            type_label.setFont(QFont("Calibri", 13, QFont.Bold))
            revenue_layout.addWidget(type_label)

            # شريط التقدم
            progress_bar = QProgressBar()
            progress_bar.setFixedHeight(40)
            progress_bar.setStyleSheet("""
                QProgressBar {
                    border: 2px solid #4682B4;
                    border-radius: 5px;
                    text-align: center;
                    font-weight: bold;
                    background-color: #87CEEB;
                }
                QProgressBar::chunk {
                    background-color: #32CD32;
                    border-radius: 3px;
                }
            """)
            self.revenue_progress_bars[revenue_key] = progress_bar
            revenue_layout.addWidget(progress_bar)

            revenue_layout.addSpacing(5)

        # إضافة مساحة فارغة لملء الارتفاع
        revenue_layout.addStretch()

        sections_layout.addWidget(revenue_group)

    def create_expenses_section(self, sections_layout):
        """إنشاء قسم المصاريف والنفقات"""
        expenses_group = QGroupBox("💸 المصاريف والنفقات")
        expenses_group.setFont(QFont("Calibri", 14, QFont.Bold))
        expenses_group.setFixedHeight(500)  # ارتفاع ثابت
        expenses_layout = QVBoxLayout(expenses_group)

        # منطقة التمرير للمصاريف
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        self.expenses_scroll_layout = QVBoxLayout(scroll_widget)

        self.expense_progress_bars = {}

        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        expenses_layout.addWidget(scroll_area)

        # تحميل أشرطة التقدم للمصاريف المحددة فقط
        self.load_expense_progress_bars()

        sections_layout.addWidget(expenses_group)

    def create_budget_section(self, sections_layout):
        """إنشاء قسم الموازنة"""
        budget_group = QGroupBox("📊 الموازنة")
        budget_group.setFont(QFont("Calibri", 14, QFont.Bold))
        budget_group.setFixedHeight(500)  # ارتفاع ثابت
        budget_layout = QVBoxLayout(budget_group)

        # إجمالي الإيرادات
        revenue_label = QLabel("💰 إجمالي الإيرادات")
        revenue_label.setFont(QFont("Calibri", 13, QFont.Bold))
        budget_layout.addWidget(revenue_label)

        self.total_revenue_progress = QProgressBar()
        self.total_revenue_progress.setFixedHeight(40)
        self.total_revenue_progress.setStyleSheet("""
            QProgressBar {
                border: 2px solid #4682B4;
                border-radius: 5px;
                text-align: center;
                font-weight: bold;
                background-color: #87CEEB;
            }
            QProgressBar::chunk {
                background-color: #32CD32;
                border-radius: 3px;
            }
        """)
        budget_layout.addWidget(self.total_revenue_progress)
        budget_layout.addSpacing(10)

        # إجمالي المصاريف
        expenses_label = QLabel("💸 إجمالي المصاريف")
        expenses_label.setFont(QFont("Calibri", 13, QFont.Bold))
        budget_layout.addWidget(expenses_label)

        self.total_expenses_progress = QProgressBar()
        self.total_expenses_progress.setFixedHeight(40)
        self.total_expenses_progress.setStyleSheet("""
            QProgressBar {
                border: 2px solid #4682B4;
                border-radius: 5px;
                text-align: center;
                font-weight: bold;
                background-color: #87CEEB;
            }
            QProgressBar::chunk {
                background-color: #FF6347;
                border-radius: 3px;
            }
        """)
        budget_layout.addWidget(self.total_expenses_progress)
        budget_layout.addSpacing(10)

        # صافي الربح/الخسارة
        profit_label = QLabel("📈 صافي الربح/الخسارة")
        profit_label.setFont(QFont("Calibri", 13, QFont.Bold))
        budget_layout.addWidget(profit_label)

        self.profit_progress = QProgressBar()
        self.profit_progress.setFixedHeight(40)
        self.profit_progress.setStyleSheet("""
            QProgressBar {
                border: 2px solid #4682B4;
                border-radius: 5px;
                text-align: center;
                font-weight: bold;
                background-color: #87CEEB;
            }
            QProgressBar::chunk {
                background-color: #FFD700;
                border-radius: 3px;
            }
        """)
        budget_layout.addWidget(self.profit_progress)
        budget_layout.addSpacing(20)

        # أزرار الموازنة
        budget_buttons_layout = QHBoxLayout()

        update_btn = QPushButton("🔄 تحديث الموازنة")
        update_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        update_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        update_btn.clicked.connect(self.update_budget_summary)
        budget_buttons_layout.addWidget(update_btn)



        budget_layout.addLayout(budget_buttons_layout)

        # إضافة مساحة فارغة لملء الارتفاع
        budget_layout.addStretch()

        sections_layout.addWidget(budget_group)

    def create_budget_form(self, main_layout):
        """إنشاء نموذج إدخال بند الموازنة"""
        form_group = QGroupBox("📝 إدخال بند الموازنة")
        form_group.setFont(QFont("Calibri", 14, QFont.Bold))
        form_layout = QHBoxLayout(form_group)

        # السنة
        year_label = QLabel("السنة:")
        year_label.setFont(QFont("Calibri", 14, QFont.Bold))
        form_layout.addWidget(year_label)

        self.year_spin = QSpinBox()
        self.year_spin.setRange(2020, 2050)
        self.year_spin.setValue(self.current_year)
        self.year_spin.setFont(QFont("Calibri", 12, QFont.Bold))
        self.year_spin.valueChanged.connect(self.year_changed)
        form_layout.addWidget(self.year_spin)

        # نوع البند
        type_label = QLabel("نوع البند:")
        type_label.setFont(QFont("Calibri", 14, QFont.Bold))
        form_layout.addWidget(type_label)

        self.item_type_combo = QComboBox()
        self.item_type_combo.addItems(["إيرادات", "مصاريف"])
        self.item_type_combo.setFont(QFont("Calibri", 12, QFont.Bold))
        self.item_type_combo.currentTextChanged.connect(self.type_changed)
        form_layout.addWidget(self.item_type_combo)

        # اسم البند
        name_label = QLabel("اسم البند:")
        name_label.setFont(QFont("Calibri", 14, QFont.Bold))
        form_layout.addWidget(name_label)

        self.item_name_combo = QComboBox()
        self.item_name_combo.setEditable(True)
        self.item_name_combo.setFont(QFont("Calibri", 12, QFont.Bold))
        form_layout.addWidget(self.item_name_combo)

        # المبلغ المتوقع
        expected_label = QLabel("المبلغ المتوقع:")
        expected_label.setFont(QFont("Calibri", 14, QFont.Bold))
        form_layout.addWidget(expected_label)

        self.expected_amount_input = QLineEdit()
        self.expected_amount_input.setPlaceholderText("المبلغ المتوقع...")
        self.expected_amount_input.setFont(QFont("Calibri", 12, QFont.Bold))
        form_layout.addWidget(self.expected_amount_input)

        # المبلغ الفعلي
        actual_label = QLabel("المبلغ الفعلي:")
        actual_label.setFont(QFont("Calibri", 14, QFont.Bold))
        form_layout.addWidget(actual_label)

        self.actual_amount_input = QLineEdit()
        self.actual_amount_input.setPlaceholderText("المبلغ الفعلي...")
        self.actual_amount_input.setFont(QFont("Calibri", 12, QFont.Bold))
        form_layout.addWidget(self.actual_amount_input)

        main_layout.addWidget(form_group)

    def create_buttons_group(self, main_layout):
        """إنشاء مجموعة الأزرار"""
        buttons_group = QGroupBox("⚡ العمليات")
        buttons_group.setFont(QFont("Calibri", 14, QFont.Bold))
        buttons_layout = QHBoxLayout(buttons_group)

        # زر إضافة البند
        add_btn = QPushButton("➕ إضافة البند")
        add_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        add_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        add_btn.clicked.connect(self.add_budget_item)
        buttons_layout.addWidget(add_btn)

        # زر مسح الحقول
        clear_btn = QPushButton("🗑️ مسح الحقول")
        clear_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        clear_btn.clicked.connect(self.clear_form)
        buttons_layout.addWidget(clear_btn)

        # زر تحديث
        refresh_btn = QPushButton("🔄 تحديث")
        refresh_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        refresh_btn.clicked.connect(self.load_budget_data)
        buttons_layout.addWidget(refresh_btn)

        # زر الإحصائيات
        stats_btn = QPushButton("📈 الإحصائيات")
        stats_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        stats_btn.setStyleSheet("""
            QPushButton {
                background-color: #6f42c1;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #5a32a3;
            }
        """)
        stats_btn.clicked.connect(self.generate_summary_report)
        buttons_layout.addWidget(stats_btn)

        main_layout.addWidget(buttons_group)

    def create_search_group(self, main_layout):
        """إنشاء مجموعة البحث والتصفية"""
        search_group = QGroupBox("🔍 البحث والتصفية")
        search_group.setFont(QFont("Calibri", 14, QFont.Bold))
        search_layout = QHBoxLayout(search_group)

        # البحث
        search_label = QLabel("بحث:")
        search_label.setFont(QFont("Calibri", 14, QFont.Bold))
        search_layout.addWidget(search_label)

        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("ابحث في بنود الموازنة...")
        self.search_input.setFont(QFont("Calibri", 12, QFont.Bold))
        self.search_input.textChanged.connect(self.filter_budget_items)
        search_layout.addWidget(self.search_input)

        # فلتر النوع
        type_filter_label = QLabel("النوع:")
        type_filter_label.setFont(QFont("Calibri", 14, QFont.Bold))
        search_layout.addWidget(type_filter_label)

        self.type_filter_combo = QComboBox()
        self.type_filter_combo.addItems(["جميع البنود", "إيرادات", "مصاريف"])
        self.type_filter_combo.setFont(QFont("Calibri", 12, QFont.Bold))
        self.type_filter_combo.currentTextChanged.connect(self.filter_budget_items)
        search_layout.addWidget(self.type_filter_combo)

        main_layout.addWidget(search_group)

    def create_budget_table(self, main_layout):
        """إنشاء جدول الموازنة"""
        table_group = QGroupBox("📊 بنود الموازنة")
        table_group.setFont(QFont("Calibri", 14, QFont.Bold))
        table_layout = QVBoxLayout(table_group)

        self.budget_table = QTableWidget()
        self.budget_table.setColumnCount(7)
        self.budget_table.setHorizontalHeaderLabels([
            "ID", "نوع البند", "اسم البند", "المبلغ المتوقع",
            "المبلغ الفعلي", "النسبة المحققة", "الفرق"
        ])

        # تنسيق رؤوس الجدول
        header = self.budget_table.horizontalHeader()
        header.setFont(QFont("Calibri", 13, QFont.Bold))
        header.setStyleSheet("""
            QHeaderView::section {
                background-color: #ff9800;
                color: white;
                padding: 8px;
                border: 1px solid #e67e22;
                font-weight: bold;
            }
        """)

        # تنسيق الجدول
        self.budget_table.setAlternatingRowColors(True)
        self.budget_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.budget_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.budget_table.setSortingEnabled(True)

        # تنسيق الجدول
        self.budget_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #dee2e6;
            }
            QTableWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
        """)

        # إضافة قائمة سياق للجدول
        self.budget_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.budget_table.customContextMenuRequested.connect(self.show_context_menu)

        table_layout.addWidget(self.budget_table)
        main_layout.addWidget(table_group)





    def year_changed(self):
        """تغيير السنة"""
        self.current_year = self.year_spin.value()
        self.statusBar().showMessage(f"تم تغيير السنة إلى {self.current_year}")

    def type_changed(self):
        """تغيير نوع البند"""
        self.load_budget_items()

    def load_budget_items(self):
        """تحميل بنود الموازنة حسب النوع"""
        try:
            conn = sqlite3.connect(self.manager.db_path)
            cursor = conn.cursor()

            item_type = self.item_type_combo.currentText()
            cursor.execute("""
                SELECT اسم_البند FROM بنود_الموازنة_الافتراضية
                WHERE نوع_البند = ? AND نشط = 1
                ORDER BY اسم_البند
            """, (item_type,))
            items = cursor.fetchall()

            self.item_name_combo.clear()
            for item_row in items:
                self.item_name_combo.addItem(item_row[0])

            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بنود الموازنة: {str(e)}")

    def load_budget_data(self):
        """تحميل بيانات الموازنة للسنة المحددة"""
        try:
            conn = sqlite3.connect(self.manager.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT id, نوع_البند, اسم_البند, المبلغ_المتوقع, المبلغ_الفعلي
                FROM الموازنة_السنوية
                WHERE السنة = ?
                ORDER BY نوع_البند, اسم_البند
            """, (self.current_year,))
            budget_data = cursor.fetchall()

            self.budget_table.setRowCount(len(budget_data))

            for row, budget_item in enumerate(budget_data):
                item_id, item_type, item_name, expected, actual = budget_item

                # حساب النسبة والفرق
                if expected and expected > 0:
                    percentage = (actual / expected) * 100 if actual else 0
                    difference = actual - expected if actual else -expected
                else:
                    percentage = 0
                    difference = actual if actual else 0

                # تعبئة الجدول
                items = [
                    str(item_id),
                    item_type,
                    item_name,
                    f"{expected:,.2f}",
                    f"{actual:,.2f}" if actual else "0.00",
                    f"{percentage:.1f}%",
                    f"{difference:,.2f}"
                ]

                for col, value in enumerate(items):
                    item = QTableWidgetItem(value)
                    item.setTextAlignment(Qt.AlignCenter)
                    item.setFont(QFont("Calibri", 12, QFont.Bold))

                    # تلوين حسب النوع والحالة
                    if col == 1:  # نوع البند
                        if item_type == "إيرادات":
                            item.setBackground(QColor("#d5f4e6"))
                        else:
                            item.setBackground(QColor("#ffeaa7"))
                    elif col == 5:  # النسبة المحققة
                        if percentage >= 100:
                            item.setBackground(QColor("#00b894"))
                            item.setForeground(QColor("white"))
                        elif percentage >= 75:
                            item.setBackground(QColor("#fdcb6e"))
                        else:
                            item.setBackground(QColor("#e17055"))
                            item.setForeground(QColor("white"))

                    self.budget_table.setItem(row, col, item)

            conn.close()
            self.statusBar().showMessage(f"تم تحميل {len(budget_data)} بند للسنة {self.current_year}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بيانات الموازنة: {str(e)}")
            self.budget_table.setRowCount(0)
            self.statusBar().showMessage("لا توجد بنود موازنة")

    def add_budget_item(self):
        """إضافة بند موازنة جديد"""
        try:
            # التحقق من صحة البيانات
            if not self.item_name_combo.currentText().strip():
                QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم البند")
                return

            if not self.expected_amount_input.text().strip():
                QMessageBox.warning(self, "تحذير", "يرجى إدخال المبلغ المتوقع")
                return

            try:
                expected_amount = float(self.expected_amount_input.text().strip())
                if expected_amount < 0:
                    QMessageBox.warning(self, "تحذير", "يجب أن يكون المبلغ المتوقع أكبر من أو يساوي صفر")
                    return
            except ValueError:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال مبلغ متوقع صحيح")
                return

            actual_amount = 0
            if self.actual_amount_input.text().strip():
                try:
                    actual_amount = float(self.actual_amount_input.text().strip())
                    if actual_amount < 0:
                        QMessageBox.warning(self, "تحذير", "يجب أن يكون المبلغ الفعلي أكبر من أو يساوي صفر")
                        return
                except ValueError:
                    QMessageBox.warning(self, "تحذير", "يرجى إدخال مبلغ فعلي صحيح")
                    return

            # إدراج البند في قاعدة البيانات
            conn = sqlite3.connect(self.manager.db_path)
            cursor = conn.cursor()

            # التحقق من وجود البند مسبقاً
            cursor.execute("""
                SELECT id FROM الموازنة_السنوية
                WHERE السنة = ? AND نوع_البند = ? AND اسم_البند = ?
            """, (self.current_year, self.item_type_combo.currentText(), self.item_name_combo.currentText()))

            existing = cursor.fetchone()

            if existing:
                # تحديث البند الموجود
                cursor.execute("""
                    UPDATE الموازنة_السنوية
                    SET المبلغ_المتوقع = ?, المبلغ_الفعلي = ?, تاريخ_التحديث = CURRENT_TIMESTAMP
                    WHERE id = ?
                """, (expected_amount, actual_amount, existing[0]))
                message = "تم تحديث البند بنجاح"
            else:
                # إضافة بند جديد
                cursor.execute("""
                    INSERT INTO الموازنة_السنوية (السنة, نوع_البند, اسم_البند, المبلغ_المتوقع, المبلغ_الفعلي)
                    VALUES (?, ?, ?, ?, ?)
                """, (
                    self.current_year,
                    self.item_type_combo.currentText(),
                    self.item_name_combo.currentText(),
                    expected_amount,
                    actual_amount
                ))
                message = "تم إضافة البند بنجاح"

            conn.commit()
            conn.close()

            QMessageBox.information(self, "نجح", message)
            self.clear_form()
            self.load_budget_data()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إضافة البند: {str(e)}")

    def clear_form(self):
        """مسح حقول النموذج"""
        self.item_type_combo.setCurrentIndex(0)
        if self.item_name_combo.count() > 0:
            self.item_name_combo.setCurrentIndex(0)
        self.expected_amount_input.clear()
        self.actual_amount_input.clear()

    def filter_budget_items(self):
        """تصفية بنود الموازنة"""
        search_text = self.search_input.text().lower()
        type_filter = self.type_filter_combo.currentText()

        for row in range(self.budget_table.rowCount()):
            show_row = True

            # فلتر النص
            if search_text:
                row_text = ""
                for col in range(self.budget_table.columnCount()):
                    item = self.budget_table.item(row, col)
                    if item:
                        row_text += item.text().lower() + " "

                if search_text not in row_text:
                    show_row = False

            # فلتر النوع
            if type_filter and type_filter != "جميع البنود":
                type_item = self.budget_table.item(row, 1)
                if not type_item or type_item.text() != type_filter:
                    show_row = False

            self.budget_table.setRowHidden(row, not show_row)

    def show_context_menu(self, position):
        """عرض قائمة السياق"""
        if self.budget_table.itemAt(position) is None:
            return

        menu = QMenu(self)

        edit_action = menu.addAction("✏️ تعديل")
        delete_action = menu.addAction("🗑️ حذف")

        action = menu.exec_(self.budget_table.mapToGlobal(position))

        if action == edit_action:
            self.edit_budget_item()
        elif action == delete_action:
            self.delete_budget_item()

    def edit_budget_item(self):
        """تعديل بند الموازنة"""
        current_row = self.budget_table.currentRow()
        if current_row < 0:
            return

        # جلب بيانات البند
        item_id = self.budget_table.item(current_row, 0).text()

        try:
            conn = sqlite3.connect(self.manager.db_path)
            cursor = conn.cursor()

            cursor.execute("SELECT * FROM الموازنة_السنوية WHERE id = ?", (item_id,))
            budget_item = cursor.fetchone()

            if budget_item:
                # تعبئة النموذج بالبيانات الحالية
                type_index = self.item_type_combo.findText(budget_item[2])
                if type_index >= 0:
                    self.item_type_combo.setCurrentIndex(type_index)

                self.load_budget_items()  # تحديث قائمة الأسماء

                name_index = self.item_name_combo.findText(budget_item[3])
                if name_index >= 0:
                    self.item_name_combo.setCurrentIndex(name_index)
                else:
                    self.item_name_combo.setEditText(budget_item[3])

                self.expected_amount_input.setText(str(budget_item[4]))
                self.actual_amount_input.setText(str(budget_item[5]) if budget_item[5] else "")

                # حذف البند القديم
                cursor.execute("DELETE FROM الموازنة_السنوية WHERE id = ?", (item_id,))
                conn.commit()

                QMessageBox.information(self, "تعديل", "تم تحميل بيانات البند للتعديل.\nيرجى تعديل البيانات والضغط على 'إضافة البند'.")

            conn.close()
            self.load_budget_data()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تعديل البند: {str(e)}")

    def delete_budget_item(self):
        """حذف بند الموازنة"""
        current_row = self.budget_table.currentRow()
        if current_row < 0:
            return

        item_id = self.budget_table.item(current_row, 0).text()
        item_name = self.budget_table.item(current_row, 2).text()
        item_type = self.budget_table.item(current_row, 1).text()

        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف هذا البند؟\n\nالنوع: {item_type}\nالاسم: {item_name}",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                conn = sqlite3.connect(self.manager.db_path)
                cursor = conn.cursor()

                cursor.execute("DELETE FROM الموازنة_السنوية WHERE id = ?", (item_id,))
                conn.commit()
                conn.close()

                QMessageBox.information(self, "تم الحذف", "تم حذف البند بنجاح")
                self.load_budget_data()

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في حذف البند: {str(e)}")

    def show_revenue_chart(self):
        """عرض رسم بياني للإيرادات"""
        if not MATPLOTLIB_AVAILABLE:
            QMessageBox.warning(self, "مكتبة مفقودة",
                              "يرجى تثبيت matplotlib لعرض الرسوم البيانية:\n\npip install matplotlib")
            return
        QMessageBox.information(self, "قريباً", "رسم الإيرادات سيكون متاحاً قريباً")

    def show_expense_chart(self):
        """عرض رسم بياني للمصاريف"""
        if not MATPLOTLIB_AVAILABLE:
            QMessageBox.warning(self, "مكتبة مفقودة",
                              "يرجى تثبيت matplotlib لعرض الرسوم البيانية:\n\npip install matplotlib")
            return
        QMessageBox.information(self, "قريباً", "رسم المصاريف سيكون متاحاً قريباً")

    def show_comparison_chart(self):
        """عرض رسم بياني للمقارنة"""
        if not MATPLOTLIB_AVAILABLE:
            QMessageBox.warning(self, "مكتبة مفقودة",
                              "يرجى تثبيت matplotlib لعرض الرسوم البيانية:\n\npip install matplotlib")
            return
        QMessageBox.information(self, "قريباً", "رسم المقارنة سيكون متاحاً قريباً")

    def generate_summary_report(self):
        """إنشاء تقرير الملخص"""
        try:
            conn = sqlite3.connect(self.manager.db_path)
            cursor = conn.cursor()

            # حساب الإجماليات
            cursor.execute("""
                SELECT نوع_البند, SUM(المبلغ_المتوقع), SUM(المبلغ_الفعلي)
                FROM الموازنة_السنوية
                WHERE السنة = ?
                GROUP BY نوع_البند
            """, (self.current_year,))

            totals = cursor.fetchall()

            report = f"📊 تقرير ملخص الموازنة للسنة {self.current_year}\n"
            report += "=" * 50 + "\n\n"

            total_expected_revenue = 0
            total_actual_revenue = 0
            total_expected_expense = 0
            total_actual_expense = 0

            for total_row in totals:
                item_type, expected, actual = total_row
                expected = expected or 0
                actual = actual or 0

                if item_type == "إيرادات":
                    total_expected_revenue = expected
                    total_actual_revenue = actual
                else:
                    total_expected_expense = expected
                    total_actual_expense = actual

                percentage = (actual / expected * 100) if expected > 0 else 0

                report += f"📋 {item_type}:\n"
                report += f"   المتوقع: {expected:,.2f} درهم\n"
                report += f"   الفعلي: {actual:,.2f} درهم\n"
                report += f"   النسبة المحققة: {percentage:.1f}%\n\n"

            # حساب الربح/الخسارة
            expected_profit = total_expected_revenue - total_expected_expense
            actual_profit = total_actual_revenue - total_actual_expense

            report += "💰 الربح/الخسارة:\n"
            report += f"   المتوقع: {expected_profit:,.2f} درهم\n"
            report += f"   الفعلي: {actual_profit:,.2f} درهم\n"

            if actual_profit > 0:
                report += "   الحالة: ربح ✅\n"
            elif actual_profit < 0:
                report += "   الحالة: خسارة ❌\n"
            else:
                report += "   الحالة: متوازن ⚖️\n"

            self.report_text.setPlainText(report)
            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء تقرير الملخص: {str(e)}")

    def generate_detailed_report(self):
        """إنشاء تقرير مفصل"""
        QMessageBox.information(self, "قريباً", "التقرير المفصل سيكون متاحاً قريباً")

    def export_budget_data(self):
        """تصدير بيانات الموازنة"""
        QMessageBox.information(self, "قريباً", "تصدير البيانات سيكون متاحاً قريباً")

    def load_expense_types(self):
        """تحميل أنواع المصاريف من قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.manager.db_path)
            cursor = conn.cursor()

            cursor.execute("SELECT اسم_النوع FROM أنواع_المصاريف WHERE نشط = 1 ORDER BY اسم_النوع")
            expense_types = cursor.fetchall()

            # إنشاء حقول الإدخال لكل نوع مصروف
            for expense_type_row in expense_types:
                expense_type = expense_type_row[0]
                self.create_expense_input(expense_type)

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل أنواع المصاريف: {str(e)}")
            # إنشاء أنواع افتراضية في حالة عدم وجود الجدول
            default_types = ["رواتب", "إيجار", "كهرباء", "مياه", "قرطاسية", "صيانة", "أخرى"]
            for expense_type in default_types:
                self.create_expense_input(expense_type)

    def create_expense_input(self, expense_type):
        """إنشاء حقل إدخال لنوع مصروف"""
        # عنوان النوع
        type_label = QLabel(f"📋 {expense_type}")
        type_label.setFont(QFont("Calibri", 13, QFont.Bold))
        self.expenses_scroll_layout.addWidget(type_label)

        # حقل إدخال المبلغ المتوقع
        amount_layout = QHBoxLayout()
        amount_label = QLabel("المبلغ المتوقع:")
        amount_label.setFont(QFont("Calibri", 12, QFont.Bold))
        amount_layout.addWidget(amount_label)

        amount_input = QLineEdit()
        amount_input.setPlaceholderText("أدخل المبلغ المتوقع بالدرهم المغربي...")
        amount_input.setFont(QFont("Calibri", 12, QFont.Bold))
        amount_input.setFixedWidth(50)  # عرض 50 نقطة
        amount_input.textChanged.connect(lambda text, key=expense_type: self.update_expense_progress(key))
        self.expense_inputs[expense_type] = amount_input
        amount_layout.addWidget(amount_input)

        self.expenses_scroll_layout.addLayout(amount_layout)

        # شريط التقدم
        progress_bar = QProgressBar()
        progress_bar.setFixedHeight(40)
        progress_bar.setFixedWidth(300)  # عرض 300 نقطة
        progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #8B4513;
                border-radius: 5px;
                text-align: center;
                font-weight: bold;
                background-color: #8B4513;
            }
            QProgressBar::chunk {
                background-color: #FF0000;
                border-radius: 3px;
            }
        """)
        self.expense_progress_bars[expense_type] = progress_bar
        self.expenses_scroll_layout.addWidget(progress_bar)

        self.expenses_scroll_layout.addSpacing(10)

    def update_revenue_progress(self, revenue_key):
        """تحديث شريط تقدم الإيرادات"""
        try:
            # الحصول على المبلغ المتوقع مباشرة من قاعدة البيانات
            conn = sqlite3.connect(self.manager.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT المبلغ_المتوقع FROM الموازنة_السنوية
                WHERE السنة = ? AND نوع_البند = 'إيرادات' AND اسم_البند = ?
            """, (self.current_year, revenue_key))

            result = cursor.fetchone()
            expected_amount = result[0] if result and result[0] else 0
            conn.close()

            actual_amount = self.get_actual_revenue(revenue_key)

            print(f"🔍 تحديث شريط الإيرادات {revenue_key}: متوقع={expected_amount}, فعلي={actual_amount}")

            if expected_amount > 0:
                percentage = min(100, (actual_amount / expected_amount) * 100)
            else:
                percentage = 0

            if revenue_key in self.revenue_progress_bars:
                progress_bar = self.revenue_progress_bars[revenue_key]
                progress_bar.setValue(int(percentage))
                progress_bar.setFormat(f"{actual_amount:,.0f} / {expected_amount:,.0f} ({percentage:.1f}%)")
                print(f"✅ تم تحديث شريط الإيرادات {revenue_key} بنجاح")
            else:
                print(f"❌ شريط الإيرادات {revenue_key} غير موجود في القاموس")

        except (ValueError, ZeroDivisionError, KeyError) as e:
            print(f"خطأ في تحديث شريط الإيرادات {revenue_key}: {str(e)}")

    def get_expected_revenue(self, revenue_key):
        """الحصول على الإيرادات المتوقعة من قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.manager.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT المبلغ_المتوقع FROM الموازنة_السنوية
                WHERE السنة = ? AND نوع_البند = 'إيرادات' AND اسم_البند = ?
            """, (self.current_year, revenue_key))

            result = cursor.fetchone()
            conn.close()

            return result[0] if result and result[0] else 0

        except Exception as e:
            print(f"خطأ في جلب الإيرادات المتوقعة: {str(e)}")
            return 0

    def update_expense_progress(self, expense_type):
        """تحديث شريط تقدم المصاريف"""
        try:
            # الحصول على المبلغ المتوقع مباشرة من الاستعلام المستخدم في load_expense_progress_bars
            conn = sqlite3.connect(self.manager.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT المبلغ_المتوقع FROM الموازنة_السنوية
                WHERE السنة = ? AND نوع_البند = 'مصاريف' AND اسم_البند = ?
            """, (self.current_year, expense_type))

            result = cursor.fetchone()
            expected_amount = result[0] if result and result[0] else 0
            conn.close()

            actual_amount = self.get_actual_expense(expense_type)

            print(f"🔍 تحديث شريط المصاريف {expense_type}: متوقع={expected_amount}, فعلي={actual_amount}")

            if expected_amount > 0:
                percentage = min(100, (actual_amount / expected_amount) * 100)
            else:
                percentage = 0

            if expense_type in self.expense_progress_bars:
                progress_bar = self.expense_progress_bars[expense_type]
                progress_bar.setValue(int(percentage))
                progress_bar.setFormat(f"{actual_amount:,.0f} / {expected_amount:,.0f} ({percentage:.1f}%)")
                print(f"✅ تم تحديث شريط المصاريف {expense_type} بنجاح")
            else:
                print(f"❌ شريط المصاريف {expense_type} غير موجود في القاموس")

        except (ValueError, ZeroDivisionError, KeyError) as e:
            print(f"خطأ في تحديث شريط المصاريف {expense_type}: {str(e)}")

    def get_expected_expense(self, expense_type):
        """الحصول على المصاريف المتوقعة من قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.manager.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT المبلغ_المتوقع FROM الموازنة_السنوية
                WHERE السنة = ? AND نوع_البند = 'مصاريف' AND اسم_البند = ?
            """, (self.current_year, expense_type))

            result = cursor.fetchone()
            conn.close()

            return result[0] if result and result[0] else 0

        except Exception as e:
            print(f"خطأ في جلب المصاريف المتوقعة: {str(e)}")
            return 0

    def get_actual_revenue(self, revenue_key):
        """الحصول على الإيرادات الفعلية"""
        try:
            conn = sqlite3.connect(self.manager.db_path)
            cursor = conn.cursor()

            if revenue_key == "registration_fees":
                # جدول registration_fees يستخدم amount_paid و payment_date
                cursor.execute("SELECT SUM(amount_paid) FROM registration_fees WHERE strftime('%Y', payment_date) = ?", (str(self.current_year),))
            elif revenue_key == "monthly_duties":
                # جدول monthly_duties يستخدم amount_paid و payment_date
                cursor.execute("SELECT SUM(amount_paid) FROM monthly_duties WHERE strftime('%Y', payment_date) = ? AND payment_status != 'غير مدفوع'", (str(self.current_year),))
            else:  # other_revenue
                return 0  # يمكن إضافة جدول للإيرادات الأخرى لاحقاً

            result = cursor.fetchone()
            conn.close()

            return result[0] if result and result[0] else 0

        except Exception as e:
            print(f"خطأ في جلب الإيرادات الفعلية: {str(e)}")
            return 0

    def get_actual_expense(self, expense_type):
        """الحصول على المصاريف الفعلية"""
        try:
            conn = sqlite3.connect(self.manager.db_path)
            cursor = conn.cursor()

            # التحقق من وجود جدول المصاريف أولاً
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='المصاريف'")
            if cursor.fetchone():
                cursor.execute("""
                    SELECT SUM(المبلغ) FROM المصاريف
                    WHERE نوع_المصروف = ? AND strftime('%Y', التاريخ) = ?
                """, (expense_type, str(self.current_year)))

                result = cursor.fetchone()
                conn.close()
                return result[0] if result and result[0] else 0
            else:
                conn.close()
                return 0

        except Exception as e:
            print(f"خطأ في جلب المصاريف الفعلية: {str(e)}")
            return 0

    def save_revenue_data(self):
        """حفظ بيانات الإيرادات"""
        try:
            conn = sqlite3.connect(self.manager.db_path)
            cursor = conn.cursor()

            for revenue_key, input_field in self.revenue_inputs.items():
                try:
                    expected_amount = float(input_field.text() or 0)
                    if expected_amount > 0:
                        # حفظ في جدول الموازنة
                        cursor.execute("""
                            INSERT OR REPLACE INTO الموازنة_السنوية
                            (السنة_المالية, نوع_البند, اسم_البند, المبلغ_المتوقع, المبلغ_الفعلي)
                            VALUES (?, ?, ?, ?, ?)
                        """, (self.current_year, "إيرادات", revenue_key, expected_amount, self.get_actual_revenue(revenue_key)))
                except ValueError:
                    continue

            conn.commit()
            conn.close()

            QMessageBox.information(self, "نجح", "تم حفظ بيانات الإيرادات بنجاح")
            self.update_all_progress_bars()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ بيانات الإيرادات: {str(e)}")

    def save_expenses_data(self):
        """حفظ بيانات المصاريف"""
        try:
            conn = sqlite3.connect(self.manager.db_path)
            cursor = conn.cursor()

            for expense_type, input_field in self.expense_inputs.items():
                try:
                    expected_amount = float(input_field.text() or 0)
                    if expected_amount > 0:
                        # حفظ في جدول الموازنة
                        cursor.execute("""
                            INSERT OR REPLACE INTO الموازنة_السنوية
                            (السنة_المالية, نوع_البند, اسم_البند, المبلغ_المتوقع, المبلغ_الفعلي)
                            VALUES (?, ?, ?, ?, ?)
                        """, (self.current_year, "مصاريف", expense_type, expected_amount, self.get_actual_expense(expense_type)))
                except ValueError:
                    continue

            conn.commit()
            conn.close()

            QMessageBox.information(self, "نجح", "تم حفظ بيانات المصاريف بنجاح")
            self.update_all_progress_bars()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ بيانات المصاريف: {str(e)}")

    def update_all_progress_bars(self):
        """تحديث جميع أشرطة التقدم"""
        # تحديث أشرطة الإيرادات
        for revenue_key in self.revenue_inputs.keys():
            self.update_revenue_progress(revenue_key)

        # تحديث أشرطة المصاريف
        for expense_type in self.expense_inputs.keys():
            self.update_expense_progress(expense_type)

    def update_budget_summary(self):
        """تحديث ملخص الموازنة وأشرطة التقدم"""
        try:
            # حساب إجمالي الإيرادات
            total_expected_revenue = self.get_total_expected_revenue()
            total_actual_revenue = sum(self.get_actual_revenue(key) for key in ["registration_fees", "monthly_duties", "other_revenue"])

            # حساب إجمالي المصاريف
            total_expected_expenses = self.get_total_expected_expenses()
            total_actual_expenses = self.get_total_actual_expenses()

            # حساب الربح/الخسارة
            expected_profit = total_expected_revenue - total_expected_expenses
            actual_profit = total_actual_revenue - total_actual_expenses

            # تحديث شريط إجمالي الإيرادات
            if total_expected_revenue > 0:
                revenue_percentage = min(100, (total_actual_revenue / total_expected_revenue) * 100)
            else:
                revenue_percentage = 0
            self.total_revenue_progress.setValue(int(revenue_percentage))
            self.total_revenue_progress.setFormat(f"{total_actual_revenue:,.0f} / {total_expected_revenue:,.0f} ({revenue_percentage:.1f}%)")

            # تحديث شريط إجمالي المصاريف
            if total_expected_expenses > 0:
                expenses_percentage = min(100, (total_actual_expenses / total_expected_expenses) * 100)
            else:
                expenses_percentage = 0
            self.total_expenses_progress.setValue(int(expenses_percentage))
            self.total_expenses_progress.setFormat(f"{total_actual_expenses:,.0f} / {total_expected_expenses:,.0f} ({expenses_percentage:.1f}%)")

            # تحديث شريط الربح/الخسارة
            if expected_profit != 0:
                profit_percentage = min(100, abs(actual_profit / expected_profit) * 100)
            else:
                profit_percentage = 0

            # تغيير لون شريط الربح حسب الحالة
            if actual_profit > 0:
                profit_color = "#00FF00"  # أخضر للربح
            elif actual_profit < 0:
                profit_color = "#FF0000"  # أحمر للخسارة
            else:
                profit_color = "#FFD700"  # أصفر للتوازن

            self.profit_progress.setStyleSheet(f"""
                QProgressBar {{
                    border: 2px solid #8B4513;
                    border-radius: 5px;
                    text-align: center;
                    font-weight: bold;
                    background-color: #8B4513;
                }}
                QProgressBar::chunk {{
                    background-color: {profit_color};
                    border-radius: 3px;
                }}
            """)

            self.profit_progress.setValue(int(profit_percentage))
            status = "ربح" if actual_profit > 0 else "خسارة" if actual_profit < 0 else "متوازن"
            self.profit_progress.setFormat(f"{actual_profit:,.0f} درهم ({status})")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحديث ملخص الموازنة: {str(e)}")

    def get_total_expected_revenue(self):
        """حساب إجمالي الإيرادات المتوقعة"""
        try:
            conn = sqlite3.connect(self.manager.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT SUM(المبلغ_المتوقع) FROM الموازنة_السنوية
                WHERE السنة = ? AND نوع_البند = 'إيرادات'
            """, (self.current_year,))

            result = cursor.fetchone()
            conn.close()

            return result[0] if result and result[0] else 0

        except Exception as e:
            print(f"خطأ في حساب إجمالي الإيرادات المتوقعة: {str(e)}")
            return 0

    def get_total_expected_expenses(self):
        """حساب إجمالي المصاريف المتوقعة"""
        try:
            conn = sqlite3.connect(self.manager.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT SUM(المبلغ_المتوقع) FROM الموازنة_السنوية
                WHERE السنة = ? AND نوع_البند = 'مصاريف'
            """, (self.current_year,))

            result = cursor.fetchone()
            conn.close()

            return result[0] if result and result[0] else 0

        except Exception as e:
            print(f"خطأ في حساب إجمالي المصاريف المتوقعة: {str(e)}")
            return 0

    def get_total_actual_expenses(self):
        """حساب إجمالي المصاريف الفعلية"""
        try:
            # بنود المصاريف الثابتة
            fixed_expense_types = [
                "رواتب",
                "كراء",
                "فواتير وأقساط",
                "معدات",
                "صيانة وتجهيز",
                "إعلانات",
                "أدوات وأجهزة",
                "أخرى"
            ]

            total = 0
            for expense_type in fixed_expense_types:
                total += self.get_actual_expense(expense_type)

            return total

        except Exception as e:
            print(f"خطأ في حساب إجمالي المصاريف الفعلية: {str(e)}")
            return 0



    def open_financial_year_setup_window(self):
        """فتح نافذة إعداد السنة المالية"""
        try:
            dialog = FinancialYearSetupDialog(self.manager.db_path, self)
            if dialog.exec_() == QDialog.Accepted:
                # تحديث قائمة السنوات في النافذة الرئيسية
                self.update_year_combo()

                # استعادة السنة المالية المحفوظة بعد التحديث
                if not self.load_saved_financial_year():
                    # إذا لم توجد سنة محفوظة، استخدم السنة الحالية
                    current_year_index = self.year_combo.findText(str(self.current_year))
                    if current_year_index >= 0:
                        self.year_combo.setCurrentIndex(current_year_index)

                self.load_saved_budget_data()
                QMessageBox.information(self, "نجح", "تم تحديث إعدادات السنة المالية بنجاح")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة إعداد السنة المالية: {str(e)}")

    def update_year_combo(self):
        """تحديث قائمة السنوات من قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.manager.db_path)
            cursor = conn.cursor()

            cursor.execute("SELECT السنة_المالية FROM السنوات_المالية ORDER BY السنة_المالية DESC")
            years = cursor.fetchall()

            current_selection = self.year_combo.currentText()
            self.year_combo.clear()

            for year_row in years:
                self.year_combo.addItem(str(year_row[0]))

            # استعادة الاختيار السابق إن أمكن
            index = self.year_combo.findText(current_selection)
            if index >= 0:
                self.year_combo.setCurrentIndex(index)

            conn.close()

        except Exception as e:
            print(f"خطأ في تحديث قائمة السنوات: {str(e)}")
            # في حالة الخطأ، استخدم السنوات الافتراضية
            self.year_combo.clear()
            for year in range(2020, 2031):
                self.year_combo.addItem(str(year))

    def save_selected_financial_year(self, year):
        """حفظ السنة المالية المختارة في قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.manager.db_path)
            cursor = conn.cursor()

            # حفظ أو تحديث السنة المالية المختارة
            cursor.execute("""
                INSERT OR REPLACE INTO إعدادات_النظام
                (اسم_الإعداد, قيمة_الإعداد, وصف_الإعداد)
                VALUES (?, ?, ?)
            """, ("السنة_المالية_المختارة", str(year), "السنة المالية المختارة حالياً من قبل المستخدم"))

            conn.commit()
            conn.close()

            print(f"✅ تم حفظ السنة المالية المختارة: {year}")

        except Exception as e:
            print(f"❌ خطأ في حفظ السنة المالية المختارة: {str(e)}")

    def load_saved_financial_year(self):
        """استعادة السنة المالية المحفوظة"""
        try:
            conn = sqlite3.connect(self.manager.db_path)
            cursor = conn.cursor()

            # البحث عن السنة المالية المحفوظة
            cursor.execute("""
                SELECT قيمة_الإعداد FROM إعدادات_النظام
                WHERE اسم_الإعداد = ?
            """, ("السنة_المالية_المختارة",))

            result = cursor.fetchone()
            conn.close()

            if result:
                saved_year = result[0]
                print(f"📅 تم العثور على السنة المالية المحفوظة: {saved_year}")

                # البحث عن السنة في القائمة وتحديدها
                index = self.year_combo.findText(saved_year)
                if index >= 0:
                    self.year_combo.setCurrentIndex(index)
                    self.current_year = int(saved_year)
                    print(f"✅ تم تحديد السنة المالية: {saved_year}")
                    return True
                else:
                    print(f"⚠️ السنة المحفوظة {saved_year} غير موجودة في القائمة")
            else:
                print("📅 لا توجد سنة مالية محفوظة، سيتم استخدام السنة الحالية")

            return False

        except Exception as e:
            print(f"❌ خطأ في استعادة السنة المالية المحفوظة: {str(e)}")
            return False

    def format_number_input(self, field):
        """تنسيق حقل الإدخال بفواصل الأرقام"""
        try:
            # الحصول على النص الحالي
            text = field.text().replace(',', '')  # إزالة الفواصل الموجودة

            if text and text.replace('.', '').replace('-', '').isdigit():
                # تحويل إلى رقم وإعادة تنسيقه
                number = float(text)
                formatted = f"{number:,.2f}"

                # حفظ موقع المؤشر
                cursor_pos = field.cursorPosition()

                # تحديث النص
                field.blockSignals(True)  # منع إرسال إشارات أثناء التحديث
                field.setText(formatted)
                field.blockSignals(False)

                # استعادة موقع المؤشر (تقريبي)
                field.setCursorPosition(min(cursor_pos, len(formatted)))

        except (ValueError, AttributeError):
            pass  # تجاهل الأخطاء في التنسيق

    def load_saved_budget_data(self):
        """تحميل البيانات المحفوظة وتحديث أشرطة التقدم"""
        try:
            print("🔄 بدء تحميل البيانات المحفوظة...")

            # تحديث أشرطة التقدم للمصاريف المحددة فقط
            self.load_expense_progress_bars()

            # تحديث أشرطة التقدم للإيرادات
            for revenue_key in ["registration_fees", "monthly_duties", "other_revenue"]:
                self.update_revenue_progress(revenue_key)

            # تحديث ملخص الموازنة
            self.update_budget_summary()

            print("✅ تم تحميل البيانات المحفوظة بنجاح")

        except Exception as e:
            print(f"خطأ في تحميل البيانات المحفوظة: {str(e)}")

    def open_revenue_setup_window(self):
        """فتح نافذة إعداد الإيرادات"""
        dialog = RevenueSetupDialog(self.manager.db_path, self.current_year, self)
        if dialog.exec_() == QDialog.Accepted:
            # تحديث أشرطة التقدم بعد الحفظ
            for revenue_key in ["registration_fees", "monthly_duties", "other_revenue"]:
                self.update_revenue_progress(revenue_key)
            self.update_budget_summary()

    def open_expenses_setup_window(self):
        """فتح نافذة إعداد المصاريف"""
        dialog = ExpensesSetupDialog(self.manager.db_path, self.current_year, self)
        if dialog.exec_() == QDialog.Accepted:
            # تحديث أشرطة التقدم بعد الحفظ
            self.load_expense_progress_bars()
            self.update_budget_summary()

    def load_expense_progress_bars(self):
        """تحميل أشرطة التقدم للمصاريف المحددة فقط"""
        try:
            # مسح الأشرطة الموجودة
            for i in reversed(range(self.expenses_scroll_layout.count())):
                child = self.expenses_scroll_layout.itemAt(i).widget()
                if child:
                    child.setParent(None)

            self.expense_progress_bars = {}

            # بنود المصاريف الثابتة
            fixed_expense_types = [
                "رواتب",
                "كراء",
                "فواتير وأقساط",
                "معدات",
                "صيانة وتجهيز",
                "إعلانات",
                "أدوات وأجهزة",
                "أخرى"
            ]

            # جلب المصاريف المحددة من قاعدة البيانات
            conn = sqlite3.connect(self.manager.db_path)
            cursor = conn.cursor()

            displayed_count = 0

            for expense_type in fixed_expense_types:
                cursor.execute("""
                    SELECT المبلغ_المتوقع FROM الموازنة_السنوية
                    WHERE السنة = ? AND نوع_البند = 'مصاريف' AND اسم_البند = ? AND المبلغ_المتوقع > 0
                """, (self.current_year, expense_type))

                result = cursor.fetchone()

                if result and result[0] > 0:
                    expected_amount = result[0]
                    displayed_count += 1

                    print(f"📋 إضافة شريط تقدم للمصروف: {expense_type} - المبلغ المتوقع: {expected_amount}")

                    # عنوان النوع
                    type_label = QLabel(f"📋 {expense_type}")
                    type_label.setFont(QFont("Calibri", 13, QFont.Bold))
                    self.expenses_scroll_layout.addWidget(type_label)

                    # شريط التقدم
                    progress_bar = QProgressBar()
                    progress_bar.setFixedHeight(40)
                    progress_bar.setStyleSheet("""
                        QProgressBar {
                            border: 2px solid #4682B4;
                            border-radius: 5px;
                            text-align: center;
                            font-weight: bold;
                            background-color: #87CEEB;
                        }
                        QProgressBar::chunk {
                            background-color: #FF6347;
                            border-radius: 3px;
                        }
                    """)
                    self.expense_progress_bars[expense_type] = progress_bar
                    self.expenses_scroll_layout.addWidget(progress_bar)

                    # تحديث شريط التقدم
                    self.update_expense_progress(expense_type)

                    self.expenses_scroll_layout.addSpacing(5)  # مسافة 5 نقاط

            conn.close()

            print(f"🔍 تم عرض {displayed_count} مصروف محدد للسنة {self.current_year}")

            if displayed_count == 0:
                # عرض رسالة إذا لم توجد مصاريف محددة
                no_data_label = QLabel("لا توجد مصاريف محددة\nاستخدم زر 'إعداد المصاريف' لإضافة مصاريف")
                no_data_label.setFont(QFont("Calibri", 12))
                no_data_label.setAlignment(Qt.AlignCenter)
                no_data_label.setStyleSheet("color: #666; padding: 20px;")
                self.expenses_scroll_layout.addWidget(no_data_label)

        except Exception as e:
            print(f"خطأ في تحميل أشرطة تقدم المصاريف: {str(e)}")

class RevenueSetupDialog(QDialog):
    """نافذة إعداد الإيرادات المتوقعة"""

    def __init__(self, db_path, current_year, parent=None):
        super().__init__(parent)
        self.db_path = db_path
        self.current_year = current_year
        self.init_ui()
        self.load_data()

    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle(f"💰 إعداد الإيرادات المتوقعة - السنة {self.current_year}")
        self.setFixedSize(700, 650)
        self.setLayoutDirection(Qt.RightToLeft)

        # توسيط النافذة
        screen = QApplication.desktop().screenGeometry()
        x = (screen.width() - 700) // 2
        y = (screen.height() - 650) // 2
        self.move(x, y)

        layout = QVBoxLayout(self)

        # العنوان
        title_label = QLabel(f"💰 إعداد الإيرادات المتوقعة - السنة {self.current_year}")
        title_label.setFont(QFont("Calibri", 15, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                background-color: #27ae60;
                color: white;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)

        # أنواع الإيرادات
        revenue_types = [
            ("واجبات التسجيل والاشتراكات", "registration_fees"),
            ("الواجبات الشهرية", "monthly_duties"),
            ("أخرى", "other_revenue")
        ]

        self.revenue_inputs = {}

        for revenue_name, revenue_key in revenue_types:
            group = QGroupBox(f"📋 {revenue_name}")
            group.setFont(QFont("Calibri", 14, QFont.Bold))
            group_layout = QVBoxLayout(group)

            # حقل إدخال المبلغ
            amount_layout = QHBoxLayout()
            amount_label = QLabel("المبلغ المتوقع (درهم مغربي):")
            amount_label.setFont(QFont("Calibri", 12, QFont.Bold))
            amount_layout.addWidget(amount_label)

            amount_input = QLineEdit()
            amount_input.setPlaceholderText("أدخل المبلغ المتوقع... (مثال: 10,000.00)")
            amount_input.setFont(QFont("Calibri", 12, QFont.Bold))
            amount_input.setFixedWidth(200)
            # إضافة تنسيق الأرقام
            amount_input.textChanged.connect(lambda text, field=amount_input: self.format_number_input(field))
            self.revenue_inputs[revenue_key] = amount_input
            amount_layout.addWidget(amount_input)

            group_layout.addLayout(amount_layout)
            layout.addWidget(group)

        # أزرار الحفظ والإلغاء
        buttons_layout = QHBoxLayout()

        save_btn = QPushButton("💾 حفظ")
        save_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        save_btn.clicked.connect(self.save_data)
        buttons_layout.addWidget(save_btn)

        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)

        layout.addLayout(buttons_layout)

    def load_data(self):
        """تحميل البيانات المحفوظة"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT اسم_البند, المبلغ_المتوقع FROM الموازنة_السنوية
                WHERE السنة = ? AND نوع_البند = 'إيرادات'
            """, (self.current_year,))

            data = cursor.fetchall()
            for item_name, expected_amount in data:
                if item_name in self.revenue_inputs:
                    self.revenue_inputs[item_name].setText(str(expected_amount))

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل بيانات الإيرادات: {str(e)}")

    def save_data(self):
        """حفظ البيانات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            for revenue_key, input_field in self.revenue_inputs.items():
                try:
                    expected_amount = float(input_field.text() or 0)
                    if expected_amount >= 0:
                        cursor.execute("""
                            INSERT OR REPLACE INTO الموازنة_السنوية
                            (السنة_المالية, نوع_البند, اسم_البند, المبلغ_المتوقع, المبلغ_الفعلي)
                            VALUES (?, ?, ?, ?, ?)
                        """, (self.current_year, "إيرادات", revenue_key, expected_amount, 0))
                except ValueError:
                    continue

            conn.commit()
            conn.close()

            QMessageBox.information(self, "نجح", "تم حفظ بيانات الإيرادات بنجاح")
            self.accept()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ بيانات الإيرادات: {str(e)}")

    def format_number_input(self, field):
        """تنسيق حقل الإدخال بفواصل الأرقام"""
        try:
            # الحصول على النص الحالي
            text = field.text().replace(',', '')  # إزالة الفواصل الموجودة

            if text and text.replace('.', '').replace('-', '').isdigit():
                # تحويل إلى رقم وإعادة تنسيقه
                number = float(text)
                formatted = f"{number:,.2f}"

                # حفظ موقع المؤشر
                cursor_pos = field.cursorPosition()

                # تحديث النص
                field.blockSignals(True)  # منع إرسال إشارات أثناء التحديث
                field.setText(formatted)
                field.blockSignals(False)

                # استعادة موقع المؤشر (تقريبي)
                field.setCursorPosition(min(cursor_pos, len(formatted)))

        except (ValueError, AttributeError):
            pass  # تجاهل الأخطاء في التنسيق

class ExpensesSetupDialog(QDialog):
    """نافذة إعداد المصاريف المتوقعة"""

    def __init__(self, db_path, current_year, parent=None):
        super().__init__(parent)
        self.db_path = db_path
        self.current_year = current_year
        self.init_ui()
        self.load_expense_types()
        self.load_data()

    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle(f"💸 إعداد المصاريف المتوقعة - السنة {self.current_year}")
        self.setFixedSize(700, 650)
        self.setLayoutDirection(Qt.RightToLeft)

        # توسيط النافذة
        screen = QApplication.desktop().screenGeometry()
        x = (screen.width() - 700) // 2
        y = (screen.height() - 650) // 2
        self.move(x, y)

        layout = QVBoxLayout(self)

        # العنوان
        title_label = QLabel(f"💸 إعداد المصاريف المتوقعة - السنة {self.current_year}")
        title_label.setFont(QFont("Calibri", 15, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                background-color: #e74c3c;
                color: white;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)

        # منطقة التمرير
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        self.scroll_layout = QVBoxLayout(scroll_widget)

        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        layout.addWidget(scroll_area)

        self.expense_inputs = {}

        # أزرار الحفظ والإلغاء
        buttons_layout = QHBoxLayout()

        save_btn = QPushButton("💾 حفظ")
        save_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        save_btn.clicked.connect(self.save_data)
        buttons_layout.addWidget(save_btn)

        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)

        layout.addLayout(buttons_layout)

    def load_expense_types(self):
        """تحميل أنواع المصاريف الثابتة"""
        # بنود المصاريف الثابتة
        fixed_expense_types = [
            "رواتب",
            "كراء",
            "فواتير وأقساط",
            "معدات",
            "صيانة وتجهيز",
            "إعلانات",
            "أدوات وأجهزة",
            "أخرى"
        ]

        for expense_type in fixed_expense_types:
            self.create_expense_input(expense_type)

    def create_expense_input(self, expense_type):
        """إنشاء حقل إدخال لنوع مصروف"""
        group = QGroupBox(f"📋 {expense_type}")
        group.setFont(QFont("Calibri", 14, QFont.Bold))
        group_layout = QVBoxLayout(group)

        # حقل إدخال المبلغ
        amount_layout = QHBoxLayout()
        amount_label = QLabel("المبلغ المتوقع (درهم مغربي):")
        amount_label.setFont(QFont("Calibri", 12, QFont.Bold))
        amount_layout.addWidget(amount_label)

        amount_input = QLineEdit()
        amount_input.setPlaceholderText("أدخل المبلغ المتوقع... (مثال: 10,000.00)")
        amount_input.setFont(QFont("Calibri", 12, QFont.Bold))
        amount_input.setFixedWidth(200)
        # إضافة تنسيق الأرقام
        amount_input.textChanged.connect(lambda text, field=amount_input: self.format_number_input(field))
        self.expense_inputs[expense_type] = amount_input
        amount_layout.addWidget(amount_input)

        group_layout.addLayout(amount_layout)
        self.scroll_layout.addWidget(group)

    def load_data(self):
        """تحميل البيانات المحفوظة"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT اسم_البند, المبلغ_المتوقع FROM الموازنة_السنوية
                WHERE السنة = ? AND نوع_البند = 'مصاريف'
            """, (self.current_year,))

            data = cursor.fetchall()
            for item_name, expected_amount in data:
                if item_name in self.expense_inputs:
                    self.expense_inputs[item_name].setText(str(expected_amount))

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل بيانات المصاريف: {str(e)}")

    def save_data(self):
        """حفظ البيانات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            for expense_type, input_field in self.expense_inputs.items():
                try:
                    expected_amount = float(input_field.text() or 0)
                    if expected_amount >= 0:
                        cursor.execute("""
                            INSERT OR REPLACE INTO الموازنة_السنوية
                            (السنة_المالية, نوع_البند, اسم_البند, المبلغ_المتوقع, المبلغ_الفعلي)
                            VALUES (?, ?, ?, ?, ?)
                        """, (self.current_year, "مصاريف", expense_type, expected_amount, 0))
                except ValueError:
                    continue

            conn.commit()
            conn.close()

            QMessageBox.information(self, "نجح", "تم حفظ بيانات المصاريف بنجاح")
            self.accept()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ بيانات المصاريف: {str(e)}")

    def format_number_input(self, field):
        """تنسيق حقل الإدخال بفواصل الأرقام"""
        try:
            # الحصول على النص الحالي
            text = field.text().replace(',', '')  # إزالة الفواصل الموجودة

            if text and text.replace('.', '').replace('-', '').isdigit():
                # تحويل إلى رقم وإعادة تنسيقه
                number = float(text)
                formatted = f"{number:,.2f}"

                # حفظ موقع المؤشر
                cursor_pos = field.cursorPosition()

                # تحديث النص
                field.blockSignals(True)  # منع إرسال إشارات أثناء التحديث
                field.setText(formatted)
                field.blockSignals(False)

                # استعادة موقع المؤشر (تقريبي)
                field.setCursorPosition(min(cursor_pos, len(formatted)))

        except (ValueError, AttributeError):
            pass  # تجاهل الأخطاء في التنسيق


class FinancialYearSetupDialog(QDialog):
    """نافذة إعداد السنة المالية"""

    def __init__(self, db_path, parent=None):
        super().__init__(parent)
        self.db_path = db_path
        self.init_ui()
        self.load_financial_years()

    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("📅 إعداد السنوات المالية")
        self.setFixedSize(800, 600)
        self.setLayoutDirection(Qt.RightToLeft)

        # تطبيق الأنماط
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                margin: 10px 0;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                background-color: #f8f9fa;
            }
        """)

        layout = QVBoxLayout(self)

        # العنوان
        title_label = QLabel("📅 إدارة السنوات المالية")
        title_label.setFont(QFont("Calibri", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                background-color: #2c3e50;
                color: white;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)

        # قسم إضافة سنة مالية جديدة
        self.create_add_year_section(layout)

        # قسم عرض السنوات المالية الموجودة
        self.create_years_table_section(layout)

        # أزرار الحفظ والإلغاء
        self.create_buttons_section(layout)

    def create_add_year_section(self, layout):
        """إنشاء قسم إضافة سنة مالية جديدة"""
        group = QGroupBox("➕ إضافة سنة مالية جديدة")
        group.setFont(QFont("Calibri", 14, QFont.Bold))
        group_layout = QFormLayout(group)

        # السنة المالية
        self.year_spin = QSpinBox()
        self.year_spin.setRange(2020, 2050)
        self.year_spin.setValue(datetime.now().year)
        self.year_spin.setFont(QFont("Calibri", 12, QFont.Bold))
        group_layout.addRow("السنة المالية:", self.year_spin)

        # تاريخ البداية
        self.start_date = QDateEdit()
        self.start_date.setDate(QDate(datetime.now().year, 9, 1))  # 1 سبتمبر
        self.start_date.setCalendarPopup(True)
        self.start_date.setFont(QFont("Calibri", 12, QFont.Bold))
        group_layout.addRow("تاريخ البداية:", self.start_date)

        # تاريخ النهاية
        self.end_date = QDateEdit()
        self.end_date.setDate(QDate(datetime.now().year + 1, 8, 31))  # 31 أغسطس
        self.end_date.setCalendarPopup(True)
        self.end_date.setFont(QFont("Calibri", 12, QFont.Bold))
        group_layout.addRow("تاريخ النهاية:", self.end_date)

        # الحالة
        self.status_combo = QComboBox()
        self.status_combo.addItems(["نشطة", "غير نشطة", "مكتملة", "ملغاة"])
        self.status_combo.setFont(QFont("Calibri", 12, QFont.Bold))
        group_layout.addRow("الحالة:", self.status_combo)

        # ملاحظات
        self.notes_text = QTextEdit()
        self.notes_text.setMaximumHeight(80)
        self.notes_text.setFont(QFont("Calibri", 12))
        self.notes_text.setPlaceholderText("ملاحظات اختيارية...")
        group_layout.addRow("ملاحظات:", self.notes_text)

        # زر الإضافة
        add_btn = QPushButton("➕ إضافة السنة المالية")
        add_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        add_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
                margin: 10px 0;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        add_btn.clicked.connect(self.add_financial_year)
        group_layout.addRow("", add_btn)

        layout.addWidget(group)

    def create_years_table_section(self, layout):
        """إنشاء قسم عرض السنوات المالية"""
        group = QGroupBox("📋 السنوات المالية الموجودة")
        group.setFont(QFont("Calibri", 14, QFont.Bold))
        group_layout = QVBoxLayout(group)

        # جدول السنوات المالية
        self.years_table = QTableWidget()
        self.years_table.setColumnCount(6)
        self.years_table.setHorizontalHeaderLabels([
            "السنة المالية", "تاريخ البداية", "تاريخ النهاية",
            "الحالة", "ملاحظات", "تاريخ الإنشاء"
        ])

        # تنسيق الجدول
        header = self.years_table.horizontalHeader()
        header.setFont(QFont("Calibri", 12, QFont.Bold))
        header.setStyleSheet("""
            QHeaderView::section {
                background-color: #3498db;
                color: white;
                padding: 8px;
                border: 1px solid #2980b9;
                font-weight: bold;
            }
        """)

        self.years_table.setAlternatingRowColors(True)
        self.years_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.years_table.setSelectionMode(QAbstractItemView.SingleSelection)

        # تنسيق الجدول
        self.years_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #dee2e6;
            }
            QTableWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
        """)

        # قائمة السياق
        self.years_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.years_table.customContextMenuRequested.connect(self.show_context_menu)

        group_layout.addWidget(self.years_table)
        layout.addWidget(group)

    def create_buttons_section(self, layout):
        """إنشاء قسم الأزرار"""
        buttons_layout = QHBoxLayout()

        # زر الحفظ
        save_btn = QPushButton("💾 حفظ وإغلاق")
        save_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border-radius: 6px;
                padding: 10px 30px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        save_btn.clicked.connect(self.accept)
        buttons_layout.addWidget(save_btn)

        # زر الإلغاء
        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border-radius: 6px;
                padding: 10px 30px;
            }
            QPushButton:hover {
                background-color: #545b62;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)

        layout.addLayout(buttons_layout)

    def add_financial_year(self):
        """إضافة سنة مالية جديدة"""
        try:
            year = self.year_spin.value()
            start_date = self.start_date.date().toString("yyyy-MM-dd")
            end_date = self.end_date.date().toString("yyyy-MM-dd")
            status = self.status_combo.currentText()
            notes = self.notes_text.toPlainText().strip()

            # التحقق من صحة التواريخ
            if self.start_date.date() >= self.end_date.date():
                QMessageBox.warning(self, "خطأ", "تاريخ البداية يجب أن يكون قبل تاريخ النهاية")
                return

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # التحقق من وجود السنة مسبقاً
            cursor.execute("SELECT id FROM السنوات_المالية WHERE السنة_المالية = ?", (year,))
            if cursor.fetchone():
                QMessageBox.warning(self, "خطأ", f"السنة المالية {year} موجودة مسبقاً")
                conn.close()
                return

            # إدراج السنة الجديدة
            cursor.execute("""
                INSERT INTO السنوات_المالية
                (السنة_المالية, تاريخ_البداية, تاريخ_النهاية, الحالة, ملاحظات)
                VALUES (?, ?, ?, ?, ?)
            """, (year, start_date, end_date, status, notes))

            conn.commit()
            conn.close()

            QMessageBox.information(self, "نجح", f"تم إضافة السنة المالية {year} بنجاح")
            self.load_financial_years()
            self.clear_form()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إضافة السنة المالية: {str(e)}")

    def load_financial_years(self):
        """تحميل السنوات المالية في الجدول"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT السنة_المالية, تاريخ_البداية, تاريخ_النهاية,
                       الحالة, ملاحظات, تاريخ_الإنشاء
                FROM السنوات_المالية
                ORDER BY السنة_المالية DESC
            """)

            years_data = cursor.fetchall()
            self.years_table.setRowCount(len(years_data))

            for row, year_data in enumerate(years_data):
                for col, value in enumerate(year_data):
                    item = QTableWidgetItem(str(value) if value else "")
                    item.setTextAlignment(Qt.AlignCenter)
                    item.setFont(QFont("Calibri", 11))

                    # تلوين حسب الحالة
                    if col == 3:  # عمود الحالة
                        if value == "نشطة":
                            item.setBackground(QColor("#d5f4e6"))
                            item.setForeground(QColor("#155724"))
                        elif value == "غير نشطة":
                            item.setBackground(QColor("#fff3cd"))
                            item.setForeground(QColor("#856404"))
                        elif value == "مكتملة":
                            item.setBackground(QColor("#d1ecf1"))
                            item.setForeground(QColor("#0c5460"))
                        elif value == "ملغاة":
                            item.setBackground(QColor("#f8d7da"))
                            item.setForeground(QColor("#721c24"))

                    self.years_table.setItem(row, col, item)

            # تعديل عرض الأعمدة
            self.years_table.resizeColumnsToContents()
            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل السنوات المالية: {str(e)}")

    def clear_form(self):
        """مسح النموذج"""
        self.year_spin.setValue(datetime.now().year)
        self.start_date.setDate(QDate(datetime.now().year, 9, 1))
        self.end_date.setDate(QDate(datetime.now().year + 1, 8, 31))
        self.status_combo.setCurrentIndex(0)
        self.notes_text.clear()

    def show_context_menu(self, position):
        """عرض قائمة السياق"""
        if self.years_table.itemAt(position) is None:
            return

        menu = QMenu(self)

        edit_action = menu.addAction("✏️ تعديل")
        delete_action = menu.addAction("🗑️ حذف")
        activate_action = menu.addAction("✅ تفعيل")
        deactivate_action = menu.addAction("❌ إلغاء تفعيل")

        action = menu.exec_(self.years_table.mapToGlobal(position))

        if action == edit_action:
            self.edit_financial_year()
        elif action == delete_action:
            self.delete_financial_year()
        elif action == activate_action:
            self.change_year_status("نشطة")
        elif action == deactivate_action:
            self.change_year_status("غير نشطة")

    def edit_financial_year(self):
        """تعديل سنة مالية"""
        current_row = self.years_table.currentRow()
        if current_row < 0:
            return

        year = int(self.years_table.item(current_row, 0).text())
        start_date = self.years_table.item(current_row, 1).text()
        end_date = self.years_table.item(current_row, 2).text()
        status = self.years_table.item(current_row, 3).text()
        notes = self.years_table.item(current_row, 4).text()

        # تعبئة النموذج
        self.year_spin.setValue(year)
        self.start_date.setDate(QDate.fromString(start_date, "yyyy-MM-dd"))
        self.end_date.setDate(QDate.fromString(end_date, "yyyy-MM-dd"))

        status_index = self.status_combo.findText(status)
        if status_index >= 0:
            self.status_combo.setCurrentIndex(status_index)

        self.notes_text.setPlainText(notes)

        # حذف السجل القديم
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("DELETE FROM السنوات_المالية WHERE السنة_المالية = ?", (year,))
            conn.commit()
            conn.close()

            self.load_financial_years()
            QMessageBox.information(self, "تعديل", "تم تحميل بيانات السنة للتعديل.\nيرجى تعديل البيانات والضغط على 'إضافة السنة المالية'.")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تعديل السنة المالية: {str(e)}")

    def delete_financial_year(self):
        """حذف سنة مالية"""
        current_row = self.years_table.currentRow()
        if current_row < 0:
            return

        year = int(self.years_table.item(current_row, 0).text())

        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف السنة المالية {year}؟\n\nتحذير: سيتم حذف جميع بيانات الموازنة المرتبطة بهذه السنة!",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()

                # حذف بيانات الموازنة المرتبطة
                cursor.execute("DELETE FROM الموازنة_السنوية WHERE السنة_المالية = ?", (year,))

                # حذف السنة المالية
                cursor.execute("DELETE FROM السنوات_المالية WHERE السنة_المالية = ?", (year,))

                conn.commit()
                conn.close()

                QMessageBox.information(self, "تم الحذف", f"تم حذف السنة المالية {year} وجميع بياناتها بنجاح")
                self.load_financial_years()

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في حذف السنة المالية: {str(e)}")

    def change_year_status(self, new_status):
        """تغيير حالة السنة المالية"""
        current_row = self.years_table.currentRow()
        if current_row < 0:
            return

        year = int(self.years_table.item(current_row, 0).text())

        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                UPDATE السنوات_المالية
                SET الحالة = ?, تاريخ_التحديث = CURRENT_TIMESTAMP
                WHERE السنة_المالية = ?
            """, (new_status, year))

            conn.commit()
            conn.close()

            QMessageBox.information(self, "تم التحديث", f"تم تغيير حالة السنة المالية {year} إلى '{new_status}'")
            self.load_financial_years()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تغيير حالة السنة المالية: {str(e)}")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    window = BudgetPlanningWindow()
    window.show()

    sys.exit(app.exec_())
