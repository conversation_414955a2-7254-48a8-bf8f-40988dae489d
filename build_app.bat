@echo off
chcp 65001 > nul
echo ========================================
echo       تحزيم نظام إدارة المدرسة
echo ========================================
echo.

echo [1/4] التحقق من وجود PyInstaller...
python -c "import PyInstaller" 2>nul
if errorlevel 1 (
    echo ❌ PyInstaller غير مثبت. جاري التثبيت...
    pip install pyinstaller
    if errorlevel 1 (
        echo ❌ فشل في تثبيت PyInstaller
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت PyInstaller بنجاح
) else (
    echo ✅ PyInstaller موجود
)

echo.
echo [2/4] تنظيف الملفات القديمة...
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"
if exist "__pycache__" rmdir /s /q "__pycache__"
echo ✅ تم تنظيف الملفات القديمة

echo.
echo [3/4] بدء عملية التحزيم...
echo هذه العملية قد تستغرق عدة دقائق...
pyinstaller main_window.spec --clean --noconfirm

if errorlevel 1 (
    echo ❌ فشل في عملية التحزيم
    echo تحقق من الأخطاء أعلاه
    pause
    exit /b 1
)

echo.
echo [4/4] التحقق من نتائج التحزيم...
if exist "dist\نظام_إدارة_المدرسة\نظام_إدارة_المدرسة.exe" (
    echo ✅ تم التحزيم بنجاح!
    echo 📁 المجلد: dist\نظام_إدارة_المدرسة\
    echo 🚀 الملف التنفيذي: نظام_إدارة_المدرسة.exe
    echo.
    echo هل تريد فتح مجلد البرنامج المحزم؟ (y/n)
    set /p choice=
    if /i "%choice%"=="y" (
        explorer "dist\نظام_إدارة_المدرسة"
    )
) else (
    echo ❌ لم يتم العثور على الملف التنفيذي
    echo تحقق من مجلد dist للتأكد من وجود الملفات
)

echo.
echo ========================================
echo           انتهت العملية
echo ========================================
pause
