@echo off
chcp 65001 > nul
echo ========================================
echo       تحزيم نظام إدارة المدرسة
echo ========================================
echo.

echo [1/5] التحقق من وجود PyInstaller...
python -c "import PyInstaller" 2>nul
if errorlevel 1 (
    echo ❌ PyInstaller غير مثبت. جاري التثبيت...
    pip install pyinstaller
    if errorlevel 1 (
        echo ❌ فشل في تثبيت PyInstaller
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت PyInstaller بنجاح
) else (
    echo ✅ PyInstaller موجود
)

echo.
echo [2/5] التحقق من المكتبات المطلوبة وتثبيتها...
echo جاري تثبيت المكتبات المطلوبة لحل مشكلة jaraco.text...
pip install --upgrade setuptools wheel
pip install jaraco.text more-itertools importlib-metadata zipp
echo ✅ تم تثبيت المكتبات المطلوبة

echo.
echo [3/5] تنظيف الملفات القديمة...
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"
if exist "__pycache__" rmdir /s /q "__pycache__"
echo ✅ تم تنظيف الملفات القديمة

echo.
echo [4/5] بدء عملية التحزيم...
echo هذه العملية قد تستغرق عدة دقائق...
echo استخدام الـ hooks المخصصة لحل مشكلة jaraco.text...
pyinstaller main_window.spec --clean --noconfirm

if errorlevel 1 (
    echo ❌ فشل في عملية التحزيم
    echo تحقق من الأخطاء أعلاه
    echo.
    echo محاولة التحزيم مع خيارات إضافية...
    pyinstaller main_window.spec --clean --noconfirm --hidden-import=jaraco.text --hidden-import=pkg_resources.py2_warn
    if errorlevel 1 (
        echo ❌ فشل في المحاولة الثانية أيضاً
        pause
        exit /b 1
    )
)

echo.
echo [5/5] التحقق من نتائج التحزيم...
if exist "dist\نظام_إدارة_المدرسة\نظام_إدارة_المدرسة.exe" (
    echo ✅ تم التحزيم بنجاح!
    echo 📁 المجلد: dist\نظام_إدارة_المدرسة\
    echo 🚀 الملف التنفيذي: نظام_إدارة_المدرسة.exe
    echo.
    echo هل تريد فتح مجلد البرنامج المحزم؟ (y/n)
    set /p choice=
    if /i "%choice%"=="y" (
        explorer "dist\نظام_إدارة_المدرسة"
    )
) else (
    echo ❌ لم يتم العثور على الملف التنفيذي
    echo تحقق من مجلد dist للتأكد من وجود الملفات
)

echo.
echo ========================================
echo           انتهت العملية
echo ========================================
pause
