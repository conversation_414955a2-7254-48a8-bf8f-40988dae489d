# دليل اختبار زر حذف التلميذ

## خطوات الاختبار

### 🧪 **اختبار 1: حذف تلميذ بدون سجلات أداء**

#### الخطوات:
1. افتح تبويب "اللوائح والأقسام"
2. ابحث عن تلميذ ليس له سجلات أداء مالي
3. حدد التلميذ (صف واحد فقط)
4. اضغط على زر "🗑️ حذف تلميذ"

#### النتيجة المتوقعة:
```
⚠️ تأكيد حذف التلميذ

📋 معلومات التلميذ:
• الاسم: [اسم التلميذ]
• الرمز: [رمز التلميذ]
• القسم: [قسم التلميذ]

✅ لا توجد سجلات أداء مالي

🗑️ هل أنت متأكد من حذف هذا التلميذ نهائياً؟
⚠️ هذا الإجراء لا يمكن التراجع عنه!
```

#### عند الموافقة:
```
✅ تم حذف التلميذ بنجاح!

📋 التلميذ المحذوف:
• الاسم: [اسم التلميذ]
• الرمز: [رمز التلميذ]
• ID: [رقم التلميذ]

🔄 سيتم تحديث القائمة تلقائياً.
```

---

### 🧪 **اختبار 2: محاولة حذف تلميذ له سجلات أداء**

#### الخطوات:
1. افتح تبويب "اللوائح والأقسام"
2. ابحث عن تلميذ له سجلات أداء مالي
3. حدد التلميذ
4. اضغط على زر "🗑️ حذف تلميذ"

#### النتيجة المتوقعة:
```
❌ لا يمكن حذف التلميذ: [اسم التلميذ]

📋 معلومات التلميذ:
• الاسم: [اسم التلميذ]
• الرمز: [رمز التلميذ]
• القسم: [قسم التلميذ]

🚫 سبب المنع:
• يوجد X سجل أداء في واجبات التسجيل
• يوجد Y سجل أداء في الواجبات الشهرية

💡 لحذف التلميذ، يجب حذف جميع سجلات الأداء أولاً.
```

---

### 🧪 **اختبار 3: عدم تحديد أي تلميذ**

#### الخطوات:
1. افتح تبويب "اللوائح والأقسام"
2. لا تحدد أي تلميذ
3. اضغط على زر "🗑️ حذف تلميذ"

#### النتيجة المتوقعة:
```
تحذير
يرجى تحديد تلميذ واحد لحذفه.
```

---

### 🧪 **اختبار 4: تحديد أكثر من تلميذ**

#### الخطوات:
1. افتح تبويب "اللوائح والأقسام"
2. حدد أكثر من تلميذ (مثلاً 3 تلاميذ)
3. اضغط على زر "🗑️ حذف تلميذ"

#### النتيجة المتوقعة:
```
تحذير
تم تحديد 3 تلميذ. يرجى تحديد تلميذ واحد فقط للحذف.
```

---

### 🧪 **اختبار 5: تلميذ غير موجود (حالة نادرة)**

#### الخطوات:
1. حدد تلميذ
2. احذف التلميذ من قاعدة البيانات خارجياً
3. اضغط على زر "🗑️ حذف تلميذ"

#### النتيجة المتوقعة:
```
تحذير
لم يتم العثور على التلميذ بالرقم: [ID]
```

---

## قائمة التحقق

### ✅ **قبل الاختبار**
- [ ] تأكد من وجود نسخة احتياطية من قاعدة البيانات
- [ ] تأكد من وجود تلاميذ بدون سجلات أداء للاختبار
- [ ] تأكد من وجود تلاميذ بسجلات أداء للاختبار
- [ ] افتح نافذة اللوائح والأقسام

### ✅ **أثناء الاختبار**
- [ ] اختبر عدم تحديد أي تلميذ
- [ ] اختبر تحديد تلميذ واحد بدون سجلات
- [ ] اختبر تحديد تلميذ واحد بسجلات أداء
- [ ] اختبر تحديد أكثر من تلميذ
- [ ] اختبر إلغاء عملية الحذف

### ✅ **بعد الاختبار**
- [ ] تحقق من تحديث القائمة بعد الحذف
- [ ] تحقق من إزالة التحديدات
- [ ] تحقق من عدم وجود أخطاء في وحدة التحكم
- [ ] تحقق من سلامة قاعدة البيانات

---

## استكشاف الأخطاء

### ❌ **إذا لم يعمل الزر**
1. تحقق من وجود الزر في الواجهة
2. تحقق من ربط الزر بالوظيفة
3. تحقق من رسائل الخطأ في وحدة التحكم

### ❌ **إذا ظهرت رسائل خطأ**
1. تحقق من اتصال قاعدة البيانات
2. تحقق من وجود الجداول المطلوبة
3. تحقق من صلاحيات الكتابة

### ❌ **إذا لم تتحدث القائمة**
1. تحقق من استدعاء `refresh_data()`
2. تحقق من استدعاء `clear_all_selections()`
3. أعد تشغيل النافذة

---

## نصائح للاختبار

### 💡 **نصائح عامة**
- اختبر جميع الحالات الممكنة
- استخدم بيانات اختبار وليس بيانات حقيقية
- احتفظ بنسخة احتياطية قبل الاختبار
- سجل أي مشاكل أو أخطاء تواجهها

### 💡 **نصائح الأمان**
- لا تختبر على بيانات حقيقية مهمة
- تأكد من إمكانية استرداد البيانات
- اختبر في بيئة منفصلة إذا أمكن
- تحقق من النسخ الاحتياطية بانتظام

---

**اتبع هذا الدليل للتأكد من عمل زر حذف التلميذ بشكل صحيح وآمن!** 🧪
