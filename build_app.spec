# -*- mode: python ; coding: utf-8 -*-

"""
ملف تحزيم بسيط - يضمن أن قاعدة البيانات تكون في مجلد التطبيق
"""

a = Analysis(
    ['main_window.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('db_path.py', '.'),  # تضمين ملف مسار قاعدة البيانات
        ('school_database.db', '.'),  # تضمين قاعدة البيانات في _internal
    ],
    hiddenimports=[
        'jaraco.text',  # لحل مشكلة PyInstaller الشائعة
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='SchoolApp',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,  # إخفاء وحدة التحكم
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='SchoolApp',
)
