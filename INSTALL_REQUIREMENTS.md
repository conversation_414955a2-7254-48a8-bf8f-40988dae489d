# 📦 تعليمات تثبيت المتطلبات للنظام المالي

## 🚀 التشغيل السريع

النظام المالي يعمل الآن بدون مشاكل! جميع النوافذ تعمل بشكل صحيح.

### تشغيل النظام الكامل:
```bash
python financial_system_launcher.py
```

### تشغيل النوافذ منفردة:
```bash
python expense_management_window.py      # نافذة مسك المصاريف
python budget_planning_window.py         # نافذة الموازنة السنوية
python profit_loss_window.py            # نافذة تقارير الأرباح والخسائر
python cash_flow_window.py              # نافذة التدفقات النقدية
```

## 📊 حول الرسوم البيانية

حالياً، الرسوم البيانية معطلة لأن مكتبة `matplotlib` غير مثبتة. النظام يعمل بشكل كامل بدونها، ولكن لتفعيل الرسوم البيانية:

### تثبيت matplotlib (اختياري):

#### الطريقة الأولى:
```bash
pip install matplotlib numpy
```

#### الطريقة الثانية:
```bash
python -m pip install matplotlib numpy
```

#### الطريقة الثالثة (إذا كان لديك مشاكل في الشبكة):
```bash
pip install --user matplotlib numpy
```

## ✅ الميزات المتاحة حالياً

### 💰 نافذة مسك المصاريف
- ✅ إدخال المصاريف بالكامل
- ✅ تصنيف المصاريف
- ✅ البحث والتصفية
- ✅ تعديل وحذف المصاريف
- ✅ واجهة احترافية

### 📊 نافذة الموازنة السنوية
- ✅ إدخال بنود الموازنة
- ✅ مقارنة المتوقع مع الفعلي
- ✅ تقارير ملخصة
- ✅ حساب النسب والفروقات
- ⚠️ الرسوم البيانية (تحتاج matplotlib)

### 📈 نافذة تقارير الأرباح والخسائر
- ✅ تقارير شهرية/فصلية/سنوية
- ✅ تحليل الإيرادات والمصاريف
- ✅ حساب الربح الصافي
- ✅ مؤشرات الأداء
- ⚠️ الرسوم البيانية (تحتاج matplotlib)

### 💸 نافذة التدفقات النقدية
- ✅ تتبع التدفقات الداخلة والخارجة
- ✅ تحليل السيولة
- ✅ إحصائيات مفصلة
- ✅ فترات زمنية مختلفة
- ⚠️ الرسوم البيانية (تحتاج matplotlib)

## 🔧 استكشاف الأخطاء

### إذا ظهرت رسالة "مكتبة مفقودة":
- هذا طبيعي عند الضغط على أزرار الرسوم البيانية
- النظام يعمل بشكل كامل بدون matplotlib
- لتفعيل الرسوم البيانية، ثبت matplotlib كما هو موضح أعلاه

### إذا لم تعمل النوافذ:
1. تأكد من وجود PyQt5:
   ```bash
   pip install PyQt5
   ```

2. تأكد من وجود ملف قاعدة البيانات `data.db` في نفس المجلد

3. تأكد من أن جميع الملفات في نفس المجلد:
   - expense_management_window.py
   - budget_planning_window.py
   - profit_loss_window.py
   - cash_flow_window.py
   - financial_system_launcher.py

## 🎯 الخطوات التالية

1. **جرب النظام الآن** - جميع النوافذ تعمل!
2. **أدخل بعض البيانات التجريبية** لاختبار الوظائف
3. **ثبت matplotlib لاحقاً** إذا كنت تريد الرسوم البيانية
4. **استمتع بالنظام المالي الشامل!** 🎉

## 📞 ملاحظات مهمة

- النظام محفوظ ومتوافق مع قاعدة البيانات الموجودة
- جميع البيانات محفوظة في `data.db`
- النوافذ مصممة بنفس تنسيق `archived_accounts_window.py`
- الواجهة عربية بالكامل مع دعم RTL
- النوافذ تفتح في وسط الشاشة
- رؤوس الجداول برتقالية اللون حسب التفضيلات

---

**النظام جاهز للاستخدام الفوري! 🚀**
