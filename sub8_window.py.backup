import sqlite3
from PyQt5.QtWidgets import (<PERSON><PERSON><PERSON><PERSON>, QVBoxLayout, QHBoxLayout, QPushButton,
                            QMessageBox, QInputDialog, QFrame, QLabel,
                            QGraphicsDropShadowEffect, QSpacerItem, QSizePolicy,
                            QLineEdit, QGridLayout, QFileDialog, QProgressDialog,
                            QApplication)
from PyQt5.QtGui import QFont, QColor, QIcon
from PyQt5.QtCore import Qt, QDateTime
import os
import shutil
import zipfile
import datetime
import tempfile
import sys

# تم إزالة استيراد مكتبات Access لأنها لم تعد مطلوبة
# البرنامج يستخدم الآن ملفات Excel بدلاً من Access

# تعريف متغيرات للتوافق مع الكود القديم
PYODBC_AVAILABLE = False
PANDAS_ACCESS_AVAILABLE = False
print("تم تعطيل دعم ملفات Access. البرنامج يستخدم الآن ملفات Excel فقط.")

# استيراد pandas كبديل للتعامل مع ملفات Excel
try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False

# استيراد xlrd و openpyxl للتعامل المباشر مع ملفات Excel
# تم إزالة xlrd لأنه غير مستخدم في الكود الحالي
XLRD_AVAILABLE = False

try:
    import openpyxl
    OPENPYXL_AVAILABLE = True
except ImportError:
    OPENPYXL_AVAILABLE = False

class Sub8Window(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        print("INFO: بدء تهيئة نافذة إعدادات البرنامج...")
        
        self.setWindowTitle(" إعدادات البرنامج ")
        
        # تحديد مسار قاعدة البيانات بناءً على الوالد
        if parent and hasattr(parent, 'db_path'):
            self.db_path = parent.db_path
            print(f"INFO: تم تحديد مسار قاعدة البيانات من الوالد: {self.db_path}")
        elif parent and hasattr(parent, 'db_folder'):
            self.db_path = os.path.join(parent.db_folder, "data.db")
            print(f"INFO: تم تحديد مسار قاعدة البيانات من مجلد الوالد: {self.db_path}")
        else:
            # استخدام مجلد البرنامج الحالي كمسار افتراضي (مسار نسبي)
            script_dir = os.path.dirname(os.path.abspath(__file__))
            self.db_path = os.path.join(script_dir, "data.db")
            print(f"INFO: استخدام مسار قاعدة البيانات النسبي: {self.db_path}")
        
        # التحقق من وجود قاعدة البيانات
        if os.path.exists(self.db_path):
            print("INFO: تم العثور على ملف قاعدة البيانات")
        else:
            print("WARNING: ملف قاعدة البيانات غير موجود")
        
        # إزالة setFixedSize للسماح بالتكامل كتبويب
        # self.setFixedSize(900, 600)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setStyleSheet("background-color: #f5f5f5;")
        
        print("INFO: تم تطبيق الأنماط الأساسية")

        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)

        # إنشاء عنوان الصفحة بتصميم أكبر وأكثر وضوحًا
        title_frame = QFrame()
        title_frame.setStyleSheet("""
            QFrame {
                background-color: #0D47A1;  /* لون أزرق غامق */
                border-radius: 12px;
                min-height: 80px;  /* زيادة ارتفاع الإطار */
            }
        """)
        title_layout = QHBoxLayout(title_frame)
        title_layout.setContentsMargins(20, 15, 20, 15)  # زيادة الهوامش

        # إضافة أيقونة للعنوان بحجم أكبر
        title_label = QLabel("⚙️   إعدادات البرنامج ")
        title_label.setFont(QFont("Amiri", 22, QFont.Bold))  # زيادة حجم الخط
        title_label.setStyleSheet("color: white;")
        title_label.setAlignment(Qt.AlignCenter)
        title_layout.addWidget(title_label)

        # إضافة تأثير الظل للعنوان
        self.apply_shadow(title_frame)

        # إضافة العنوان للتخطيط الرئيسي
        main_layout.addWidget(title_frame)

        # إنشاء إطار للأزرار
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 10px;
                border: 1px solid #e0e0e0;
            }
        """)

        # إضافة تأثير الظل للإطار
        self.apply_shadow(buttons_frame)

        # تخطيط الأزرار
        buttons_layout = QVBoxLayout(buttons_frame)
        buttons_layout.setContentsMargins(20, 20, 20, 20)
        buttons_layout.setSpacing(15)  # تقليل المسافة بين الأزرار

        # تعديل التخطيط ليكون شبكي بدلاً من عمودي
        grid_layout = QGridLayout()
        grid_layout.setHorizontalSpacing(15)
        grid_layout.setVerticalSpacing(15)
        buttons_layout.addLayout(grid_layout)

        # إنشاء الأزرار مع إضافة زر استيراد البيانات من إكسل وإعدادات الطابعة
        button_data = [
            ("🗑️ حذف جميع البيانات", "#e74c3c", self.delete_all_data),  # أحمر
            ("💾 نسخ احتياطي للبيانات", "#f39c12", self.backup_database),  # برتقالي فاتح
            ("📂 استيراد نسخة احتياطية", "#9b59b6", self.restore_backup),  # بنفسجي
            ("📊 استيراد البيانات من ملف إكسل", "#27ae60", self.import_excel_data),  # أخضر
            ("🖨️ إعدادات الطابعة", "#3498db", self.open_printer_settings),  # أزرق
        ]

        # تنظيم الأزرار في صفوف (عمود واحد أو أكثر حسب رغبتك)
        self.buttons = []
        for i, (text, color, handler) in enumerate(button_data):
            btn = QPushButton(text)
            btn.setFont(QFont("Amiri", 15, QFont.Bold))
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    color: white;
                    border: none;
                    border-radius: 10px;
                    padding: 12px;
                    text-align: right;
                    min-height: 40px;
                    min-width: 200px;
                }}
                QPushButton:hover {{
                    background-color: {self.darken_color(color)};
                }}
                QPushButton:pressed {{
                    background-color: {self.darken_color(color, factor=30)};
                }}
            """)
            btn.clicked.connect(handler)
            # ترتيب الأزرار في عمود واحد
            grid_layout.addWidget(btn, i, 0)
            self.buttons.append(btn)

        # إضافة مساحة مرنة في نهاية تخطيط الأزرار
        buttons_layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding))

        # إضافة إطار الأزرار للتخطيط الرئيسي
        main_layout.addWidget(buttons_frame)

        print("INFO: تم إنشاء نافذة إعدادات البرنامج بنجاح")

    def apply_shadow(self, widget):
        """تطبيق تأثير الظل على العنصر"""
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(15)
        shadow.setXOffset(3)
        shadow.setYOffset(3)
        shadow.setColor(QColor(0, 0, 0, 60))
        widget.setGraphicsEffect(shadow)

    def darken_color(self, color, factor=15):
        """تغميق اللون بنسبة معينة"""
        # تحويل اللون من تنسيق hex إلى RGB
        color = color.lstrip('#')
        r, g, b = int(color[0:2], 16), int(color[2:4], 16), int(color[4:6], 16)

        # تقليل قيم RGB بنسبة factor
        r = max(0, r - factor)
        g = max(0, g - factor)
        b = max(0, b - factor)

        # إعادة تحويل اللون إلى تنسيق hex
        return f"#{r:02x}{g:02x}{b:02x}"

    def closeEvent(self, event):
        """منع إغلاق النافذة مباشرة وتوجيه المستخدم لاستخدام زر تسجيل الخروج"""
        try:
            # استيراد الدالة من sub100_window إذا كانت متاحة
            try:
                from sub100_window import CustomDialogs
                CustomDialogs.show_custom_warning_message(
                    self,
                    message="""
                    <div dir='rtl' style='text-align: right;'>
                        <p style='font-family: Calibri; font-size: 14pt; color: #e74c3c; font-weight: bold; margin-bottom: 15px;'>
                            لا يمكن إغلاق النافذة بهذه الطريقة
                        </p>
                        <p style='font-family: Calibri; font-size: 13pt; color: #333333; margin-bottom: 10px;'>
                            الرجاء استخدام زر "تسجيل الخروج" الموجود في الشريط العلوي لإغلاق البرنامج.
                        </p>
                    </div>
                    """,
                    title="تنبيه"
                )
            except ImportError:
                # إذا لم تكن الدالة متاحة، استخدم الطريقة التقليدية
                QMessageBox.warning(
                    self,
                    "تنبيه",
                    "لا يمكن إغلاق النافذة بهذه الطريقة.\nالرجاء استخدام زر 'تسجيل الخروج' الموجود في الشريط العلوي لإغلاق البرنامج."
                )

            # منع إغلاق النافذة
            event.ignore()
        except Exception as e:
            print(f"خطأ في عرض رسالة منع الإغلاق: {e}")
            # في حالة حدوث خطأ، نسمح بإغلاق النافذة
            event.accept()

    def delete_all_data(self):
        """حذف جميع البيانات مع التأكد من كلمة المرور"""
        # إنشاء مربع حوار لإدخال كلمة المرور
        password_dialog = QInputDialog(self)
        password_dialog.setWindowTitle("التحقق من الهوية")
        password_dialog.setLabelText("الرجاء إدخال رمز الحذف للمتابعة:")
        password_dialog.setTextEchoMode(QLineEdit.Password)
        password_dialog.setStyleSheet("""
            QInputDialog {
                background-color: white;
            }
            QLabel {
                font-family: 'Calibri';
                font-size: 13pt;
                font-weight: bold;
                color: black;
            }
            QLineEdit {
                padding: 8px;
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                font-family: 'Calibri';
                font-size: 13pt;
            }
            QPushButton {
                font-family: 'Calibri';
                font-size: 13pt;
                font-weight: bold;
                min-width: 80px;
                padding: 5px;
            }
        """)

        ok = password_dialog.exec_()
        password = password_dialog.textValue()

        # التحقق من صحة كلمة المرور
        if ok and password == "12345":
            # تنفيذ عملية الحذف فورا
            self.perform_deletion()
        else:
            if ok:  # إذا ضغط المستخدم على "موافق" لكن كلمة المرور خاطئة
                self.show_status_message("رمز الحذف غير صحيح!", "error")

    def perform_deletion(self):
        """تنفيذ عملية حذف البيانات من الجداول المحددة"""
        try:
            # قائمة الجداول المراد حذف بياناتها
            tables_to_clear = [
                'الحساب_الرئيسي',
                'المسحوبات',
                'registration_fees',
                'احصائيات_الغياب_الشهرية',
                'احصائيات_الغياب_السنوية',
                'الحسابات_المرحلة',
                'المصاريف',
                'الموازنة_السنوية',
                'الواجبات_الشهرية',
                'تدوين_الغياب',
                'جدول_الاداءات',
                'جدول_البيانات',
                'جدول_المواد_والاقسام',
                'واجبات_التسجيل',
                'monthly_duties'
            ]

            # تنفيذ حذف البيانات أولاً
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # التحقق من وجود كل جدول قبل محاولة حذف البيانات منه
            deleted_tables = []
            for table in tables_to_clear:
                try:
                    cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
                    if cursor.fetchone():
                        cursor.execute(f"DELETE FROM {table}")
                        deleted_tables.append(table)
                        print(f"تم حذف البيانات من جدول: {table}")
                    else:
                        print(f"الجدول غير موجود: {table}")
                except Exception as table_error:
                    print(f"خطأ في حذف بيانات الجدول {table}: {table_error}")
                    continue

            # عرض رسالة تعليمات عادية لتأكيد الحذف
            instruction_dialog = QMessageBox(self)
            instruction_dialog.setWindowTitle("تأكيد الحذف")
            instruction_dialog.setText("تم حذف جميع البيانات بنجاح")

            # إنشاء نص تفصيلي للجداول التي تم حذفها فعلياً
            if deleted_tables:
                deleted_tables_text = "تم مسح البيانات من الجداول التالية:\n"
                for table in deleted_tables:
                    deleted_tables_text += f"• {table}\n"
            else:
                deleted_tables_text = "لم يتم العثور على جداول صالحة للحذف.\n"
            
            deleted_tables_text += "\nمع الاحتفاظ بالسنة الدراسية في جدول بيانات_المؤسسة"
            deleted_tables_text += "\n\nسيتم إغلاق البرنامج الآن لإتمام عملية الحذف وضغط قاعدة البيانات."

            instruction_dialog.setInformativeText(deleted_tables_text)
            instruction_dialog.setIcon(QMessageBox.Information)
            instruction_dialog.setStandardButtons(QMessageBox.Ok)

            # إضافة أيقونة البرنامج إلى نافذة الرسالة
            icon_path = "01.ico"
            if os.path.exists(icon_path):
                instruction_dialog.setWindowIcon(QIcon(icon_path))

            instruction_dialog.setStyleSheet("""
                QMessageBox {
                    background-color: white;
                }
                QLabel {
                    font-family: 'Calibri';
                    font-size: 13pt;
                    font-weight: bold;
                    color: black;
                }
                QPushButton {
                    font-family: 'Calibri';
                    font-size: 13pt;
                    font-weight: bold;
                    color: white;
                    background-color: #0D47A1;
                    border: none;
                    border-radius: 5px;
                    padding: 5px 15px;
                    min-width: 140px;
                }
                QPushButton:hover {
                    background-color: #1565C0;
                }
                QPushButton:pressed {
                    background-color: #0D47A1;
                }
            """)

          
            # ضغط قاعدة البيانات
            try:
                conn.execute("VACUUM")
                print("تم ضغط قاعدة البيانات بنجاح")
            except Exception as e:
                print(f"خطأ أثناء ضغط قاعدة البيانات: {str(e)}")

            # إكمال العملية وإغلاق الاتصال
            conn.commit()
            conn.close()

            # إضافة دالة للخروج من البرنامج بعد الضغط على زر موافق
            def exit_application():
                print("جاري إغلاق البرنامج...")
                QApplication.quit()

            # ربط زر موافق بدالة الخروج
            instruction_dialog.buttonClicked.connect(exit_application)

            # عرض رسالة التأكيد
            instruction_dialog.exec_()

        except Exception as e:
            self.show_status_message(f"خطأ أثناء حذف البيانات: {str(e)}", "error")

    def show_status_message(self, message, status="info"):
        """عرض رسالة حالة"""
        icon = QMessageBox.Information
        title = "معلومات"

        if status == "error":
            icon = QMessageBox.Critical
            title = "خطأ"
        elif status == "warning":
            icon = QMessageBox.Warning
            title = "تحذير"
        elif status == "success":
            icon = QMessageBox.Information
            title = "نجاح"
        elif status == "progress":
            # في حالة رسائل التقدم، نكتفي بعرض في وحدة التحكم
            print(message)
            return

        msg_box = QMessageBox(self)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setIcon(icon)
        msg_box.setStyleSheet("""
            QMessageBox {
                background-color: white;
            }
            QLabel {
                font-family: 'Calibri';
                font-size: 13pt;
                font-weight: bold;
                color: black;
            }
            QPushButton {
                font-family: 'Calibri';
                font-size: 13pt;
                font-weight: bold;
                min-width: 80px;
                padding: 5px;
            }
        """)
        msg_box.exec_()

    def backup_database(self):
        """عمل نسخة احتياطية لقاعدة البيانات"""
        try:
            # 1. إنشاء مجلد النسخ الاحتياطي على سطح المكتب
            desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
            main_folder = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة")
            backup_folder = os.path.join(main_folder, "النسخ الاحتياطي للبرنامج")

            # إنشاء المجلدات إذا لم تكن موجودة
            if not os.path.exists(main_folder):
                os.makedirs(main_folder)
            if not os.path.exists(backup_folder):
                os.makedirs(backup_folder)
                self.show_status_message(f"تم إنشاء مجلد النسخ الاحتياطي: {backup_folder}", "info")

            # 2. توليد اسم ملف النسخة الاحتياطية (التاريخ والوقت)
            current_datetime = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
            backup_name = f"database_backup_{current_datetime}"
            backup_sqlite = os.path.join(backup_folder, f"{backup_name}.sqlite")
            backup_zip = os.path.join(backup_folder, f"{backup_name}.zip")

            # 3. إصلاح وضغط قاعدة البيانات
            # 3.1 فتح اتصال بقاعدة البيانات الأصلية
            conn = sqlite3.connect(self.db_path)

            # 3.2 إصلاح قاعدة البيانات
            conn.execute("PRAGMA integrity_check")  # التحقق من سلامة قاعدة البيانات
            conn.execute("VACUUM")  # تنظيف وضغط قاعدة البيانات

            # 3.3 إنشاء نسخة احتياطية مؤقتة
            backup_conn = sqlite3.connect(backup_sqlite)
            conn.backup(backup_conn)

            # 3.4 إغلاق الاتصال بقواعد البيانات
            backup_conn.close()
            conn.close()

            # 4. ضغط ملف النسخة الاحتياطية
            with zipfile.ZipFile(backup_zip, 'w', zipfile.ZIP_DEFLATED) as zipf:
                zipf.write(backup_sqlite, os.path.basename(backup_sqlite))

            # 5. حذف الملف المؤقت بعد إنشاء ملف الضغط
            os.remove(backup_sqlite)

            # 6. حساب حجم النسخة الاحتياطية
            backup_size_kb = os.path.getsize(backup_zip) / 1024
            backup_size_mb = backup_size_kb / 1024

            size_text = f"{backup_size_mb:.2f} MB" if backup_size_mb >= 1 else f"{backup_size_kb:.2f} KB"

            # تم إلغاء فتح مجلد النسخ الاحتياطيات تلقائياً

            # إظهار رسالة النجاح مع معلومات إضافية
            success_message = (
                f"تم عمل نسخة احتياطية بنجاح!\n\n"
                f"اسم الملف: {os.path.basename(backup_zip)}\n"
                f"المسار: {backup_folder}\n"
                f"حجم الملف: {size_text}\n"
                f"التاريخ والوقت: {current_datetime.replace('_', ' ')}"
            )

            self.show_status_message(success_message, "success")

        except Exception as e:
            error_message = f"حدث خطأ أثناء عمل نسخة احتياطية:\n{str(e)}"
            self.show_status_message(error_message, "error")

    def restore_backup(self):
        """استيراد نسخة احتياطية من الملفات المضغوطة أو ملفات SQLite مباشرة"""
        try:
            # فتح حوار اختيار الملف في مجلد النسخ الاحتياطيات على سطح المكتب
            desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
            main_folder = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة")
            backup_folder = os.path.join(main_folder, "النسخ الاحتياطي للبرنامج")

            # إنشاء المجلدات إذا لم تكن موجودة
            if not os.path.exists(main_folder):
                os.makedirs(main_folder)
            if not os.path.exists(backup_folder):
                os.makedirs(backup_folder)

            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "اختر ملف النسخة الاحتياطية",
                backup_folder,
                "جميع ملفات النسخ الاحتياطية (*.zip *.sqlite *.db);;ملفات مضغوطة (*.zip);;ملفات قواعد بيانات (*.sqlite *.db)"
            )

            if not file_path:
                # المستخدم ألغى العملية
                return

            # فحص نوع الملف المختار
            is_zip = file_path.lower().endswith('.zip')
            is_sqlite = file_path.lower().endswith(('.sqlite', '.db'))

            if not (is_zip or is_sqlite):
                self.show_status_message("الملف المختار ليس ملف نسخة احتياطية معتمد. يرجى اختيار ملف بامتداد ZIP أو SQLite.", "error")
                return

            # عرض رسالة تأكيد للمستخدم
            confirm_dialog = QMessageBox(self)
            confirm_dialog.setWindowTitle("تأكيد استعادة النسخة الاحتياطية")
            confirm_dialog.setText("هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟")
            confirm_dialog.setInformativeText("ستتم استبدال جميع البيانات الحالية بالبيانات من النسخة الاحتياطية.")
            confirm_dialog.setIcon(QMessageBox.Warning)
            confirm_dialog.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
            confirm_dialog.button(QMessageBox.Yes).setText("نعم")
            confirm_dialog.button(QMessageBox.No).setText("لا")
            confirm_dialog.setStyleSheet("""
                QMessageBox {
                    background-color: white;
                }
                QLabel {
                    font-family: 'Calibri';
                    font-size: 13pt;
                    font-weight: bold;
                    color: black;
                }
                QPushButton {
                    font-family: 'Calibri';
                    font-size: 13pt;
                    font-weight: bold;
                    min-width: 80px;
                    padding: 5px;
                }
            """)

            # التحقق من رغبة المستخدم في المتابعة
            if confirm_dialog.exec_() != QMessageBox.Yes:
                return

            # إنشاء مؤشر تقدم العملية
            progress = QProgressDialog("جاري استعادة النسخة الاحتياطية...", "إلغاء", 0, 100, self)
            progress.setWindowTitle("استعادة النسخة الاحتياطية")
            progress.setWindowModality(Qt.WindowModal)
            progress.setAutoClose(True)
            progress.setMinimumDuration(0)
            progress.setStyleSheet("""
                QProgressDialog {
                    background-color: white;
                }
                QLabel {
                    font-family: 'Calibri';
                    font-size: 13pt;
                    font-weight: bold;
                    color: black;
                }
                QProgressBar {
                    border: 1px solid #bdc3c7;
                    border-radius: 3px;
                    text-align: center;
                    background-color: #ecf0f1;
                }
                QProgressBar::chunk {
                    background-color: #9b59b6;
                    width: 10px;
                }
                QPushButton {
                    font-family: 'Calibri';
                    font-size: 13pt;
                    font-weight: bold;
                    min-width: 80px;
                    padding: 5px;
                }
            """)

            # إظهار مؤشر التقدم
            progress.setValue(0)
            progress.show()

            backup_file_path = None
            temp_dir = None

            # معالجة حسب نوع الملف
            if is_zip:
                # 1. استخراج النسخة الاحتياطية من الملف المضغوط إلى مجلد مؤقت
                progress.setValue(10)
                progress.setLabelText("جاري فحص الملف المضغوط...")

                # التحقق من صحة الملف المضغوط
                try:
                    with zipfile.ZipFile(file_path, 'r') as zip_ref:
                        # التأكد من أن الملف يحتوي على ملف النسخة الاحتياطية
                        file_list = zip_ref.namelist()
                        backup_files = [f for f in file_list if f.endswith(('.sqlite', '.db'))]

                        if not backup_files:
                            self.show_status_message("الملف المختار لا يحتوي على نسخة احتياطية صالحة", "error")
                            progress.close()
                            return

                        # استخراج الملف إلى مجلد مؤقت
                        temp_dir = tempfile.mkdtemp()
                        progress.setValue(30)
                        progress.setLabelText("جاري استخراج النسخة الاحتياطية...")

                        zip_ref.extract(backup_files[0], temp_dir)
                        backup_file_path = os.path.join(temp_dir, backup_files[0])
                except Exception as e:
                    self.show_status_message(f"خطأ في استخراج الملف: {str(e)}", "error")
                    progress.close()
                    return
            else:  # ملف SQLite مباشر
                # في حالة كان الملف قاعدة بيانات مباشرة
                backup_file_path = file_path
                progress.setValue(30)
                progress.setLabelText("تم تحديد ملف قاعدة البيانات...")

            # 2. فحص النسخة الاحتياطية
            progress.setValue(50)
            progress.setLabelText("جاري التحقق من النسخة الاحتياطية...")

            try:
                # التحقق من صحة قاعدة البيانات
                test_conn = sqlite3.connect(backup_file_path)
                test_cursor = test_conn.cursor()

                # التحقق من وجود الجداول الأساسية
                test_cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in test_cursor.fetchall()]

                # الحد الأدنى من الجداول المتوقعة
                essential_tables = ["بيانات_المؤسسة"]

                if not all(table in tables for table in essential_tables):
                    self.show_status_message("النسخة الاحتياطية غير صالحة: لا تحتوي على جميع الجداول الأساسية", "error")
                    test_conn.close()
                    progress.close()
                    # تنظيف المجلد المؤقت
                    if temp_dir:
                        shutil.rmtree(temp_dir, ignore_errors=True)
                    return

                test_conn.close()
            except Exception as e:
                self.show_status_message(f"خطأ في فحص النسخة الاحتياطية: {str(e)}", "error")
                progress.close()
                # تنظيف المجلد المؤقت
                if temp_dir:
                    shutil.rmtree(temp_dir, ignore_errors=True)
                return

            # 3. استبدال قاعدة البيانات الحالية بالنسخة الاحتياطية
            progress.setValue(70)
            progress.setLabelText("جاري استبدال قاعدة البيانات...")

            try:
                # إغلاق أي اتصالات مفتوحة بقاعدة البيانات
                # على المستخدم إغلاق جميع النوافذ المفتوحة قبل الاستعادة

                # إنشاء نسخة احتياطية من قاعدة البيانات الحالية قبل الاستبدال
                current_time = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
                pre_restore_backup = f"pre_restore_backup_{current_time}.db"
                pre_restore_path = os.path.join(
                    os.path.dirname(os.path.abspath(self.db_path)),
                    "Backups",
                    pre_restore_backup
                )

                # التأكد من وجود مجلد النسخ الاحتياطية
                backup_folder = os.path.join(os.path.dirname(os.path.abspath(self.db_path)), "Backups")
                if not os.path.exists(backup_folder):
                    os.makedirs(backup_folder)

                # نسخ قاعدة البيانات الحالية
                shutil.copy2(self.db_path, pre_restore_path)

                # استبدال قاعدة البيانات الحالية بالنسخة الاحتياطية
                shutil.copy2(backup_file_path, self.db_path)

                progress.setValue(90)
                progress.setLabelText("تم استعادة النسخة الاحتياطية بنجاح!")

                # تنظيف المجلد المؤقت إن وجد
                if temp_dir:
                    shutil.rmtree(temp_dir, ignore_errors=True)

                # إكمال العملية
                progress.setValue(100)

                # عرض رسالة نجاح
                file_name = os.path.basename(file_path)
                success_message = (
                    f"تمت استعادة النسخة الاحتياطية بنجاح!\n\n"
                    f"اسم الملف المستعاد: {file_name}\n"
                    f"تم حفظ نسخة من قاعدة البيانات السابقة في:\n"
                    f"{pre_restore_backup}\n\n"
                    f"يرجى إعادة تشغيل البرنامج لتطبيق التغييرات."
                )
                self.show_status_message(success_message, "success")

                # إغلاق مؤشر التقدم
                progress.close()

            except Exception as e:
                self.show_status_message(f"خطأ في استعادة النسخة الاحتياطية: {str(e)}", "error")
                progress.close()
                # تنظيف المجلد المؤقت
                if temp_dir:
                    shutil.rmtree(temp_dir, ignore_errors=True)
                return

        except Exception as e:
            self.show_status_message(f"حدث خطأ أثناء استعادة النسخة الاحتياطية: {str(e)}", "error")

    def import_excel_data(self):
        """استيراد البيانات من ملف إكسل إلى جدول_البيانات"""
        try:
            # التحقق من توفر مكتبات Excel
            if not PANDAS_AVAILABLE and not OPENPYXL_AVAILABLE:
                self.show_status_message(
                    "لا يمكن استيراد البيانات من ملفات Excel.\n"
                    "يرجى تثبيت مكتبة pandas أو openpyxl أولاً.",
                    "error"
                )
                return

            # فتح متصفح الملفات لاختيار ملف Excel
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "اختر ملف Excel للاستيراد",
                "",
                "ملفات Excel (*.xlsx *.xls);;جميع الملفات (*)"
            )

            if not file_path:
                # المستخدم ألغى العملية
                return

            # التحقق من امتداد الملف
            if not file_path.lower().endswith(('.xlsx', '.xls')):
                self.show_status_message("يرجى اختيار ملف Excel صالح (.xlsx أو .xls)", "error")
                return

            # قراءة أول صف لعرض معاينة الأعمدة
            try:
                if PANDAS_AVAILABLE:
                    preview_df = pd.read_excel(file_path, header=0, nrows=0)  # قراءة رؤوس الأعمدة فقط
                    available_columns = list(preview_df.columns)
                else:
                    from openpyxl import load_workbook
                    wb = load_workbook(file_path)
                    ws = wb.active
                    available_columns = []
                    for cell in ws[1]:
                        if cell.value:
                            available_columns.append(str(cell.value).strip())

                # عرض معاينة الأعمدة
                columns_preview = '\n'.join([f"• {col}" for col in available_columns if col])

                # عرض رسالة تأكيد مع معاينة الأعمدة
                confirm_dialog = QMessageBox(self)
                confirm_dialog.setWindowTitle("تأكيد استيراد البيانات")
                confirm_dialog.setText("هل أنت متأكد من استيراد البيانات من ملف Excel؟")
                confirm_dialog.setInformativeText(
                    f"ستتم إضافة البيانات إلى جدول_البيانات مع إنشاء رموز تلاميذ تلقائية.\n\n"
                    f"الأعمدة الموجودة في الملف:\n{columns_preview}\n\n"
                    f"الأعمدة المطلوبة: القسم، اسم_المجموعة، اسم_التلميذ، النوع"
                )
                confirm_dialog.setIcon(QMessageBox.Question)
                confirm_dialog.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
                confirm_dialog.button(QMessageBox.Yes).setText("نعم")
                confirm_dialog.button(QMessageBox.No).setText("لا")
                confirm_dialog.setStyleSheet("""
                    QMessageBox {
                        background-color: white;
                    }
                    QLabel {
                        font-family: 'Calibri';
                        font-size: 13pt;
                        font-weight: bold;
                        color: black;
                    }
                    QPushButton {
                        font-family: 'Calibri';
                        font-size: 13pt;
                        font-weight: bold;
                        min-width: 80px;
                        padding: 5px;
                    }
                """)

                if confirm_dialog.exec_() != QMessageBox.Yes:
                    return

            except Exception as e:
                self.show_status_message(f"خطأ في قراءة ملف Excel: {str(e)}", "error")
                return

            # إنشاء مؤشر تقدم
            progress = QProgressDialog("جاري استيراد البيانات من ملف Excel...", "إلغاء", 0, 100, self)
            progress.setWindowTitle("استيراد البيانات")
            progress.setWindowModality(Qt.WindowModal)
            progress.setAutoClose(True)
            progress.setMinimumDuration(0)
            progress.setValue(0)
            progress.show()

            # قراءة ملف Excel
            progress.setValue(20)
            progress.setLabelText("جاري قراءة ملف Excel...")

            if PANDAS_AVAILABLE:
                # استخدام pandas لقراءة الملف
                df = pd.read_excel(file_path, header=0)  # الصف الأول يحتوي على رؤوس الأعمدة

                # طباعة أسماء الأعمدة الموجودة للتشخيص
                print(f"الأعمدة الموجودة في الملف: {list(df.columns)}")

                # تنظيف أسماء الأعمدة من المسافات الزائدة
                df.columns = df.columns.str.strip()

                # التحقق من وجود الأعمدة المطلوبة مع مرونة في الأسماء
                required_columns = ['القسم', 'اسم_المجموعة', 'اسم_التلميذ', 'النوع']
                column_mapping = {}

                # البحث عن الأعمدة بطريقة مرنة
                for req_col in required_columns:
                    found = False
                    for df_col in df.columns:
                        # مقارنة مرنة تتجاهل المسافات والحالة
                        if req_col.strip().replace(' ', '').lower() == str(df_col).strip().replace(' ', '').lower():
                            column_mapping[req_col] = df_col
                            found = True
                            break

                    if not found:
                        # محاولة البحث بالكلمات المفتاحية
                        if req_col == 'القسم':
                            for df_col in df.columns:
                                if 'قسم' in str(df_col) or 'class' in str(df_col).lower():
                                    column_mapping[req_col] = df_col
                                    found = True
                                    break
                        elif req_col == 'اسم_المجموعة':
                            for df_col in df.columns:
                                if 'مجموعة' in str(df_col) or 'group' in str(df_col).lower():
                                    column_mapping[req_col] = df_col
                                    found = True
                                    break
                        elif req_col == 'اسم_التلميذ':
                            for df_col in df.columns:
                                if 'تلميذ' in str(df_col) or 'اسم' in str(df_col) or 'name' in str(df_col).lower():
                                    column_mapping[req_col] = df_col
                                    found = True
                                    break
                        elif req_col == 'النوع':
                            for df_col in df.columns:
                                if 'نوع' in str(df_col) or 'جنس' in str(df_col) or 'gender' in str(df_col).lower():
                                    column_mapping[req_col] = df_col
                                    found = True
                                    break

                # التحقق من العثور على جميع الأعمدة
                missing_columns = [col for col in required_columns if col not in column_mapping]

                if missing_columns:
                    available_columns = '\n'.join([f"- {col}" for col in df.columns])
                    self.show_status_message(
                        f"لم يتم العثور على الأعمدة التالية:\n{', '.join(missing_columns)}\n\n"
                        f"الأعمدة المتوفرة في الملف:\n{available_columns}\n\n"
                        f"يرجى التأكد من أن الملف يحتوي على الأعمدة:\nالقسم، اسم_المجموعة، اسم_التلميذ، النوع",
                        "error"
                    )
                    progress.close()
                    return

                print(f"تم العثور على الأعمدة: {column_mapping}")

                # تحويل البيانات إلى قائمة
                data_rows = []
                for _, row in df.iterrows():
                    data_rows.append([
                        str(row[column_mapping['القسم']]) if pd.notna(row[column_mapping['القسم']]) else '',
                        str(row[column_mapping['اسم_المجموعة']]) if pd.notna(row[column_mapping['اسم_المجموعة']]) else '',
                        str(row[column_mapping['اسم_التلميذ']]) if pd.notna(row[column_mapping['اسم_التلميذ']]) else '',
                        str(row[column_mapping['النوع']]) if pd.notna(row[column_mapping['النوع']]) else ''
                    ])
            else:
                # استخدام openpyxl كبديل
                from openpyxl import load_workbook
                wb = load_workbook(file_path)
                ws = wb.active

                # قراءة رؤوس الأعمدة من الصف الأول
                headers = []
                for cell in ws[1]:
                    header_value = str(cell.value).strip() if cell.value else ''
                    headers.append(header_value)

                print(f"الأعمدة الموجودة في الملف: {headers}")

                # التحقق من وجود الأعمدة المطلوبة مع مرونة في الأسماء
                required_columns = ['القسم', 'اسم_المجموعة', 'اسم_التلميذ', 'النوع']
                column_indices = {}

                # البحث عن الأعمدة بطريقة مرنة
                for req_col in required_columns:
                    found = False
                    for i, header in enumerate(headers):
                        # مقارنة مرنة تتجاهل المسافات والحالة
                        if req_col.strip().replace(' ', '').lower() == header.strip().replace(' ', '').lower():
                            column_indices[req_col] = i
                            found = True
                            break

                    if not found:
                        # محاولة البحث بالكلمات المفتاحية
                        if req_col == 'القسم':
                            for i, header in enumerate(headers):
                                if 'قسم' in header or 'class' in header.lower():
                                    column_indices[req_col] = i
                                    found = True
                                    break
                        elif req_col == 'اسم_المجموعة':
                            for i, header in enumerate(headers):
                                if 'مجموعة' in header or 'group' in header.lower():
                                    column_indices[req_col] = i
                                    found = True
                                    break
                        elif req_col == 'اسم_التلميذ':
                            for i, header in enumerate(headers):
                                if 'تلميذ' in header or 'اسم' in header or 'name' in header.lower():
                                    column_indices[req_col] = i
                                    found = True
                                    break
                        elif req_col == 'النوع':
                            for i, header in enumerate(headers):
                                if 'نوع' in header or 'جنس' in header or 'gender' in header.lower():
                                    column_indices[req_col] = i
                                    found = True
                                    break

                # التحقق من العثور على جميع الأعمدة
                missing_columns = [col for col in required_columns if col not in column_indices]

                if missing_columns:
                    available_headers = '\n'.join([f"- {header}" for header in headers if header])
                    self.show_status_message(
                        f"لم يتم العثور على الأعمدة التالية:\n{', '.join(missing_columns)}\n\n"
                        f"الأعمدة المتوفرة في الملف:\n{available_headers}\n\n"
                        f"يرجى التأكد من أن الملف يحتوي على الأعمدة:\nالقسم، اسم_المجموعة، اسم_التلميذ، النوع",
                        "error"
                    )
                    progress.close()
                    return

                print(f"تم العثور على الأعمدة: {column_indices}")

                # قراءة البيانات (تجاهل الصف الأول)
                data_rows = []
                for row in ws.iter_rows(min_row=2, values_only=True):
                    if row and any(cell for cell in row):  # تجاهل الصفوف الفارغة
                        data_rows.append([
                            str(row[column_indices['القسم']]) if len(row) > column_indices['القسم'] and row[column_indices['القسم']] else '',
                            str(row[column_indices['اسم_المجموعة']]) if len(row) > column_indices['اسم_المجموعة'] and row[column_indices['اسم_المجموعة']] else '',
                            str(row[column_indices['اسم_التلميذ']]) if len(row) > column_indices['اسم_التلميذ'] and row[column_indices['اسم_التلميذ']] else '',
                            str(row[column_indices['النوع']]) if len(row) > column_indices['النوع'] and row[column_indices['النوع']] else ''
                        ])

            progress.setValue(50)
            progress.setLabelText("جاري معالجة البيانات...")

            # الاتصال بقاعدة البيانات
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # إنشاء جدول_البيانات إذا لم يكن موجوداً
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS جدول_البيانات (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    القسم TEXT,
                    اسم_المجموعة TEXT,
                    اسم_التلميذ TEXT,
                    النوع TEXT,
                    رمز_التلميذ TEXT UNIQUE
                )
            """)

            # الحصول على آخر رمز تلميذ لحساب الرقم التالي
            cursor.execute("SELECT MAX(CAST(SUBSTR(رمز_التلميذ, 2) AS INTEGER)) FROM جدول_البيانات WHERE رمز_التلميذ LIKE 'R%'")
            result = cursor.fetchone()
            last_number = result[0] if result[0] else 9999  # البدء من R10000
            next_number = last_number + 1

            progress.setValue(70)
            progress.setLabelText("جاري إدراج البيانات...")

            # إدراج البيانات
            inserted_count = 0
            skipped_count = 0

            for i, row_data in enumerate(data_rows):
                قسم, اسم_مجموعة, اسم_تلميذ, نوع = row_data

                # تجاهل الصفوف الفارغة
                if not اسم_تلميذ.strip():
                    skipped_count += 1
                    continue

                # إنشاء رمز التلميذ
                رمز_تلميذ = f"R{next_number}"
                next_number += 1

                try:
                    cursor.execute("""
                        INSERT INTO جدول_البيانات (القسم, اسم_المجموعة, اسم_التلميذ, النوع, رمز_التلميذ)
                        VALUES (?, ?, ?, ?, ?)
                    """, (قسم, اسم_مجموعة, اسم_تلميذ, نوع, رمز_تلميذ))
                    inserted_count += 1
                except sqlite3.IntegrityError:
                    # في حالة تكرار رمز التلميذ، جرب رقم آخر
                    next_number += 1
                    رمز_تلميذ = f"R{next_number}"
                    try:
                        cursor.execute("""
                            INSERT INTO جدول_البيانات (القسم, اسم_المجموعة, اسم_التلميذ, النوع, رمز_التلميذ)
                            VALUES (?, ?, ?, ?, ?)
                        """, (قسم, اسم_مجموعة, اسم_تلميذ, نوع, رمز_تلميذ))
                        inserted_count += 1
                        next_number += 1
                    except sqlite3.IntegrityError:
                        skipped_count += 1
                        continue

                # تحديث مؤشر التقدم
                if i % 10 == 0:
                    progress_value = 70 + (i / len(data_rows)) * 20
                    progress.setValue(int(progress_value))

            # حفظ التغييرات
            conn.commit()
            conn.close()

            progress.setValue(100)
            progress.close()

            # عرض رسالة النجاح
            success_message = (
                f"تم استيراد البيانات بنجاح!\n\n"
                f"عدد السجلات المدرجة: {inserted_count}\n"
                f"عدد السجلات المتجاهلة: {skipped_count}\n"
                f"ملف المصدر: {os.path.basename(file_path)}"
            )
            self.show_status_message(success_message, "success")

        except Exception as e:
            self.show_status_message(f"حدث خطأ أثناء استيراد البيانات: {str(e)}", "error")

    def open_printer_settings(self):
        """فتح نافذة إعدادات الطابعة"""
        try:
            from sub3_window import PrinterSettingsWindow

            # إنشاء نافذة إعدادات الطابعة
            self.printer_settings_window = PrinterSettingsWindow(self.db_path)

            # عرض النافذة في وسط الشاشة
            self.printer_settings_window.show()
            self.printer_settings_window.raise_()
            self.printer_settings_window.activateWindow()

        except ImportError as e:
            self.show_status_message(f"خطأ في استيراد نافذة إعدادات الطابعة:\n{str(e)}", "error")
        except Exception as e:
            self.show_status_message(f"خطأ في فتح نافذة إعدادات الطابعة:\n{str(e)}", "error")

if __name__ == '__main__':
    import sys
    from PyQt5.QtWidgets import QApplication
    app = QApplication(sys.argv)
    window = Sub8Window()
    window.show()
    sys.exit(app.exec_())


