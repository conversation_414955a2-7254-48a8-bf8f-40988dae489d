# 🏦 النظام المالي للمؤسسة التعليمية

## نظرة عامة
النظام المالي الشامل للمؤسسة التعليمية هو مجموعة من النوافذ المتخصصة لإدارة جميع الجوانب المالية للمؤسسة التعليمية بطريقة احترافية ومنظمة.

## 📁 ملفات النظام

### 1. النوافذ الرئيسية
- **`expense_management_window.py`** - نافذة مسك المصاريف
- **`budget_planning_window.py`** - نافذة إعداد الموازنة السنوية  
- **`profit_loss_window.py`** - نافذة تقارير الأرباح والخسائر (P&L)
- **`cash_flow_window.py`** - نافذة التدفقات النقدية (Cash Flow)

### 2. ملفات التشغيل والتوثيق
- **`financial_system_launcher.py`** - المشغل الرئيسي للنظام
- **`FINANCIAL_SYSTEM_README.md`** - هذا الملف (التوثيق)

## 🚀 كيفية التشغيل

### التشغيل الشامل
```bash
python financial_system_launcher.py
```

### تشغيل نافذة منفردة
```bash
# نافذة مسك المصاريف
python expense_management_window.py

# نافذة الموازنة السنوية
python budget_planning_window.py

# نافذة تقارير الأرباح والخسائر
python profit_loss_window.py

# نافذة التدفقات النقدية
python cash_flow_window.py
```

## 💰 نافذة مسك المصاريف

### الميزات الرئيسية
- ✅ إدخال المصاريف بتفاصيل كاملة (التاريخ، المبلغ، النوع، الجهة المستفيدة)
- ✅ تصنيف المصاريف (رواتب، كراء، فواتير، معدات، صيانة، إعلانات...)
- ✅ طرق الأداء المختلفة (نقداً، تحويل بنكي، شيك، بطاقة ائتمان)
- ✅ البحث والتصفية المتقدمة
- ✅ تعديل وحذف المصاريف
- ✅ واجهة مستخدم احترافية مع تنسيق جميل

### كيفية الاستخدام
1. اختر التاريخ
2. أدخل المبلغ
3. اختر نوع المصروف من القائمة
4. أدخل اسم الجهة المستفيدة
5. اختر طريقة الأداء
6. أضف ملاحظات (اختياري)
7. اضغط "إضافة المصروف"

## 📊 نافذة إعداد الموازنة السنوية

### الميزات الرئيسية
- ✅ تخطيط الإيرادات والمصاريف المتوقعة
- ✅ مقارنة المبالغ المتوقعة مع الفعلية
- ✅ أشرطة تقدم ملونة تعكس الأداء
- ✅ تقارير ملخصة وتفصيلية
- ✅ رسوم بيانية للمقارنة
- ✅ حساب الربح/الخسارة المتوقعة

### أنواع البنود
#### الإيرادات
- رسوم التسجيل
- الواجبات الشهرية
- رسوم إضافية
- منح ودعم
- إيرادات أخرى

#### المصاريف
- رواتب المدرسين والإداريين
- إيجار المباني
- فواتير الخدمات
- المعدات التعليمية
- القرطاسية والمواد
- النقل والمواصلات
- التأمين والصيانة

## 📈 نافذة تقارير الأرباح والخسائر (P&L)

### الميزات الرئيسية
- ✅ تقارير شهرية، فصلية، وسنوية
- ✅ تفصيل الإيرادات والمصاريف
- ✅ حساب الربح الصافي أو العجز
- ✅ مؤشرات الأداء المالي
- ✅ رسوم بيانية تطورية
- ✅ تحليل مقارن بين الفترات

### أنواع التقارير
1. **التقرير الشهري** - تحليل شهر محدد
2. **التقرير الفصلي** - تحليل فصل دراسي
3. **التقرير السنوي** - تحليل السنة كاملة

### مؤشرات الأداء
- هامش الربح
- نسبة المصاريف إلى الإيرادات
- تقييم الأداء (ممتاز/جيد/مقبول/يحتاج تحسين)

## 💸 نافذة التدفقات النقدية (Cash Flow)

### الميزات الرئيسية
- ✅ تتبع الأموال الداخلة والخارجة
- ✅ تحليل السيولة النقدية
- ✅ تقارير يومية وأسبوعية وشهرية
- ✅ رسوم بيانية للتدفقات
- ✅ إحصائيات مفصلة
- ✅ تحليل أنماط التدفق

### أقسام التقرير
1. **ملخص التدفقات** - نظرة عامة شاملة
2. **التدفقات الداخلة** - جدول مفصل للإيرادات
3. **التدفقات الخارجة** - جدول مفصل للمصاريف
4. **الرسوم البيانية** - تمثيل بصري للبيانات

## 🎨 التصميم والواجهة

### خصائص التصميم
- ✅ واجهة عربية بالكامل (RTL)
- ✅ تنسيق احترافي مشابه لـ `archived_accounts_window.py`
- ✅ ألوان متناسقة وجذابة
- ✅ رؤوس جداول برتقالية اللون
- ✅ أيقونات تعبيرية واضحة
- ✅ نوافذ متوسطة في وسط الشاشة
- ✅ خطوط Calibri واضحة ومقروءة

### نظام الألوان
- **الأخضر (#27ae60)** - للإيرادات والعمليات الإيجابية
- **الأحمر (#e74c3c)** - للمصاريف والعمليات السلبية
- **الأزرق (#3498db)** - للمعلومات والتحديث
- **البنفسجي (#9b59b6)** - للتقارير والتحليل
- **البرتقالي (#ff9800)** - لرؤوس الجداول

## 🔧 المتطلبات التقنية

### المكتبات المطلوبة
```python
PyQt5>=5.15.0
matplotlib>=3.5.0
numpy>=1.21.0
sqlite3 (مدمجة مع Python)
```

### تثبيت المتطلبات
```bash
pip install PyQt5 matplotlib numpy
```

## 🗃️ قاعدة البيانات

### الجداول المستخدمة
1. **`المصاريف`** - بيانات المصاريف
2. **`أنواع_المصاريف`** - تصنيفات المصاريف
3. **`الموازنة_السنوية`** - بيانات الموازنة
4. **`بنود_الموازنة_الافتراضية`** - البنود المعيارية
5. **`registration_fees`** - رسوم التسجيل (موجودة مسبقاً)
6. **`monthly_duties`** - الواجبات الشهرية (موجودة مسبقاً)

## 📋 التقارير والطباعة

### أنواع التقارير
- تقارير الأرباح والخسائر (P&L)
- تقارير التدفقات النقدية
- تقارير الموازنة السنوية
- تقارير المصاريف المفصلة

### التكامل مع نظام الطباعة
- التقارير مرتبطة مباشرة بـ `print101.py`
- إمكانية طباعة جميع التقارير
- تصدير البيانات بصيغ مختلفة

## 🔒 الأمان والنسخ الاحتياطي

### ميزات الأمان
- التحقق من صحة البيانات المدخلة
- رسائل تأكيد للعمليات الحساسة
- حماية من الأخطاء والاستثناءات

### النسخ الاحتياطي
- يُنصح بعمل نسخة احتياطية من `data.db` بانتظام
- حفظ التقارير المهمة كملفات PDF

## 📞 الدعم والصيانة

### في حالة وجود مشاكل
1. تأكد من تثبيت جميع المتطلبات
2. تحقق من وجود ملف `data.db`
3. راجع رسائل الخطأ في وحدة التحكم
4. تأكد من صحة البيانات المدخلة

### التطوير المستقبلي
- إضافة المزيد من الرسوم البيانية
- تحسين واجهة المستخدم
- إضافة ميزات تصدير متقدمة
- تطوير تقارير إضافية

---

**تم تطوير هذا النظام بعناية فائقة ليوفر حلولاً مالية شاملة للمؤسسات التعليمية** 🎓💼
