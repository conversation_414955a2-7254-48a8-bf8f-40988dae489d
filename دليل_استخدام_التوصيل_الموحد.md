# دليل استخدام التوصيل الموحد

## نظرة عامة

التوصيل الموحد هو حل متطور لطباعة توصيل واحد للتلاميذ المسجلين في أقسام متعددة، بدلاً من طباعة توصيل منفصل لكل قسم.

## الوصول للميزة

### 📍 **الموقع**
- **التبويب**: اللوائح والأقسام (`sub252_window.py`)
- **الزر**: "🖨️ توصيل موحد" (اللون الأخضر المائل للزرقة)
- **الموقع**: في شريط الأزرار العلوي

## خطوات الاستخدام

### 🔢 **الخطوة 1: فتح النافذة**
1. افتح تبويب "اللوائح والأقسام"
2. ابحث عن زر "🖨️ توصيل موحد" في شريط الأزرار
3. اضغط على الزر لفتح نافذة الطباعة

### 📝 **الخطوة 2: إدخال البيانات**

#### أ. رمز التلميذ
```
رمز التلميذ: [12345]
             أدخل رمز التلميذ...
```
- **مطلوب**: يجب إدخال رمز التلميذ
- **التحقق**: النظام يتحقق من وجود التلميذ
- **البحث**: يبحث في جميع الأقسام تلقائياً

#### ب. نوع التوصيل
```
نوع التوصيل: [الواجبات الشهرية ▼]
```
**الخيارات المتاحة:**
- ✅ **الواجبات الشهرية** - يجمع جميع الواجبات الشهرية
- ✅ **واجبات التسجيل** - يجمع جميع واجبات التسجيل

### 🖨️ **الخطوة 3: الطباعة**
1. تأكد من صحة البيانات المدخلة
2. اضغط على زر "🖨️ طباعة التوصيل"
3. ستظهر نافذة معاينة التوصيل
4. راجع المحتوى قبل الطباعة النهائية

## أنواع التوصيلات

### 📊 **توصيل الواجبات الشهرية**

#### المحتوى:
- **معلومات التلميذ**: الاسم، الرمز، الهاتف، المؤسسة
- **تفاصيل شهرية**: مجمعة حسب الشهر والسنة
- **الأقسام**: قائمة بجميع الأقسام المسجل بها
- **المبالغ**: المطلوب، المدفوع، المتبقي لكل شهر
- **الإجماليات**: مجموع جميع الأشهر والأقسام

#### مثال:
```
الشهر: يناير 2024
الأقسام: الرياضيات، العلوم، العربية
المطلوب: 600.00 درهم (200 × 3 أقسام)
المدفوع: 600.00 درهم
المتبقي: 0.00 درهم
```

### 💰 **توصيل واجبات التسجيل**

#### المحتوى:
- **معلومات التلميذ**: الاسم، الرمز، الهاتف، المؤسسة
- **الأقسام المسجل بها**: قائمة شاملة
- **تفاصيل الدفعات**: مجمعة حسب نوع الدفعة
- **تفاصيل فردية**: كل دفعة مع تاريخها وطريقة الدفع
- **الإجماليات**: مجموع جميع الدفعات

#### مثال:
```
نوع الدفعة: رسوم التسجيل
الأقسام: الرياضيات، العلوم، العربية
إجمالي المبلغ: 1500.00 درهم

الدفعة 1: 500.00 درهم - 2024-01-10 - نقداً - الرياضيات
الدفعة 2: 500.00 درهم - 2024-01-10 - نقداً - العلوم
الدفعة 3: 500.00 درهم - 2024-01-10 - نقداً - العربية
```

## المميزات

### ✅ **التجميع الذكي**
- **حسب الشهر**: للواجبات الشهرية
- **حسب نوع الدفعة**: لواجبات التسجيل
- **حسب القسم**: عرض جميع الأقسام
- **الإجماليات**: حسابات تلقائية دقيقة

### ✅ **المعلومات الشاملة**
- **بيانات التلميذ**: كاملة ومحدثة
- **تفاصيل الدفعات**: تواريخ وطرق الدفع
- **حالة الأداء**: مدفوع، غير مدفوع، جزئي
- **الأقسام**: قائمة واضحة بجميع الأقسام

### ✅ **التصميم المهني**
- **تخطيط منظم**: سهل القراءة والفهم
- **معلومات المؤسسة**: رأس مهني
- **تاريخ الطباعة**: للمرجعية
- **تنسيق موحد**: متسق مع باقي النظام

## حالات الاستخدام

### 🎯 **الحالة 1: تلميذ في قسمين**
```
التلميذ: أحمد محمد
الأقسام: الرياضيات + العلوم
النتيجة: توصيل واحد يجمع الواجبات من القسمين
```

### 🎯 **الحالة 2: تلميذ في ثلاثة أقسام**
```
التلميذ: فاطمة علي
الأقسام: العربية + الفرنسية + الإنجليزية
النتيجة: توصيل واحد شامل لجميع الأقسام
```

### 🎯 **الحالة 3: تلميذ بدفعات متعددة**
```
التلميذ: محمد أحمد
الدفعات: رسوم تسجيل + رسوم كتب + رسوم أنشطة
النتيجة: توصيل موحد يفصل كل نوع دفعة
```

## رسائل النظام

### ✅ **رسائل النجاح**
```
تم إرسال التوصيل الموحد للتلميذ [الاسم] للطباعة بنجاح.
```

### ⚠️ **رسائل التحذير**
```
يرجى إدخال رمز التلميذ.
```

### ❌ **رسائل الخطأ**
```
لم يتم العثور على تلميذ برمز: [الرمز]
لا توجد واجبات شهرية للتلميذ: [الاسم]
لا توجد واجبات تسجيل للتلميذ: [الاسم]
```

## نصائح للاستخدام الأمثل

### 💡 **نصائح عامة**
1. **تأكد من الرمز**: تحقق من صحة رمز التلميذ قبل الطباعة
2. **راجع المعاينة**: اقرأ التوصيل بعناية قبل الطباعة النهائية
3. **احفظ نسخة**: احتفظ بنسخة رقمية للمرجعية
4. **تحقق من التواريخ**: تأكد من صحة تواريخ الدفعات

### 💡 **نصائح للكفاءة**
1. **استخدم البحث**: ابحث عن التلميذ في الجدول أولاً للتأكد من الرمز
2. **اختر النوع المناسب**: حدد نوع التوصيل حسب الحاجة
3. **طباعة دورية**: اطبع التوصيلات بانتظام لتجنب التراكم
4. **تنظيم الملفات**: احفظ التوصيلات في مجلدات منظمة

## استكشاف الأخطاء

### 🔍 **مشاكل شائعة وحلولها**

#### المشكلة: "لم يتم العثور على تلميذ"
**الأسباب المحتملة:**
- رمز التلميذ غير صحيح
- التلميذ غير مسجل في النظام
- خطأ في كتابة الرمز

**الحلول:**
1. تحقق من رمز التلميذ في جدول البيانات
2. ابحث عن التلميذ بالاسم أولاً
3. تأكد من عدم وجود مسافات إضافية

#### المشكلة: "لا توجد واجبات"
**الأسباب المحتملة:**
- التلميذ لم يؤد أي واجبات بعد
- البيانات لم تُحدث
- مشكلة في قاعدة البيانات

**الحلول:**
1. تحقق من وجود سجلات في جداول الواجبات
2. حدث البيانات باستخدام زر "🔄 تحديث النموذج"
3. تأكد من صحة ربط البيانات

### 🔧 **صيانة دورية**
1. **نظف قاعدة البيانات** من السجلات المكررة
2. **حدث البيانات** بانتظام
3. **تحقق من الروابط** بين الجداول
4. **اختبر الطباعة** دورياً

---

**استخدم هذا الدليل للحصول على أفضل تجربة مع ميزة التوصيل الموحد!** 📚
